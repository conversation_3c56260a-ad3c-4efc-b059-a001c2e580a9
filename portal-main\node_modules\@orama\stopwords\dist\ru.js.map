{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'и',\n    'в',\n    'во',\n    'не',\n    'что',\n    'он',\n    'на',\n    'я',\n    'с',\n    'со',\n    'как',\n    'а',\n    'то',\n    'все',\n    'она',\n    'так',\n    'его',\n    'но',\n    'да',\n    'ты',\n    'к',\n    'у',\n    'же',\n    'вы',\n    'за',\n    'бы',\n    'по',\n    'только',\n    'ее',\n    'мне',\n    'было',\n    'вот',\n    'от',\n    'меня',\n    'еще',\n    'нет',\n    'о',\n    'из',\n    'ему',\n    'теперь',\n    'когда',\n    'даже',\n    'ну',\n    'вдруг',\n    'ли',\n    'если',\n    'уже',\n    'или',\n    'ни',\n    'быть',\n    'был',\n    'него',\n    'до',\n    'вас',\n    'нибудь',\n    'опять',\n    'уж',\n    'вам',\n    'сказал',\n    'ведь',\n    'там',\n    'потом',\n    'себя',\n    'ничего',\n    'ей',\n    'может',\n    'они',\n    'тут',\n    'где',\n    'есть',\n    'надо',\n    'ней',\n    'для',\n    'мы',\n    'тебя',\n    'их',\n    'чем',\n    'была',\n    'сам',\n    'чтоб',\n    'без',\n    'будто',\n    'человек',\n    'чего',\n    'раз',\n    'тоже',\n    'себе',\n    'под',\n    'жизнь',\n    'будет',\n    'ж',\n    'тогда',\n    'кто',\n    'этот',\n    'говорил',\n    'того',\n    'потому',\n    'этого',\n    'какой',\n    'совсем',\n    'ним',\n    'здесь',\n    'этом',\n    'один',\n    'почти',\n    'мой',\n    'тем',\n    'чтобы',\n    'нее',\n    'кажется',\n    'сейчас',\n    'были',\n    'куда',\n    'зачем',\n    'сказать',\n    'всех',\n    'никогда',\n    'сегодня',\n    'можно',\n    'при',\n    'наконец',\n    'два',\n    'об',\n    'другой',\n    'хоть',\n    'после',\n    'над',\n    'больше',\n    'тот',\n    'через',\n    'эти',\n    'нас',\n    'про',\n    'всего',\n    'них',\n    'какая',\n    'много',\n    'разве',\n    'сказала',\n    'три',\n    'эту',\n    'моя',\n    'впрочем',\n    'хорошо',\n    'свою',\n    'этой',\n    'перед',\n    'иногда',\n    'лучше',\n    'чуть',\n    'том',\n    'нельзя',\n    'такой',\n    'им',\n    'более',\n    'всегда',\n    'конечно',\n    'всю',\n    'между'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,IACA,IACA,KACA,KACA,MACA,KACA,KACA,IACA,IACA,KACA,MACA,IACA,KACA,MACA,MACA,MACA,MACA,KACA,KACA,KACA,IACA,IACA,KACA,KACA,KACA,KACA,KACA,SACA,KACA,MACA,OACA,MACA,KACA,OACA,MACA,MACA,IACA,KACA,MACA,SACA,QACA,OACA,KACA,QACA,KACA,OACA,MACA,MACA,KACA,OACA,MACA,OACA,KACA,MACA,SACA,QACA,KACA,MACA,SACA,OACA,MACA,QACA,OACA,SACA,KACA,QACA,MACA,MACA,MACA,OACA,OACA,MACA,MACA,KACA,OACA,KACA,MACA,OACA,MACA,OACA,MACA,QACA,UACA,OACA,MACA,OACA,OACA,MACA,QACA,QACA,IACA,QACA,MACA,OACA,UACA,OACA,SACA,QACA,QACA,SACA,MACA,QACA,OACA,OACA,QACA,MACA,MACA,QACA,MACA,UACA,SACA,OACA,OACA,QACA,UACA,OACA,UACA,UACA,QACA,MACA,UACA,MACA,KACA,SACA,OACA,QACA,MACA,SACA,MACA,QACA,MACA,MACA,MACA,QACA,MACA,QACA,QACA,QACA,UACA,MACA,MACA,MACA,UACA,SACA,OACA,OACA,QACA,SACA,QACA,OACA,MACA,SACA,QACA,KACA,QACA,SACA,UACA,MACA,QACH,AAAC"}