---
title: 快速开始
---

在本章节中，让我们一起构建 TEN Agent 的 Playground。

## 前提条件

import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

<Tabs items={['API Keys', '安装', '最低系统要求']} defaultIndex={0}>
  <Tab>
    A<PERSON>a [ App ID
    ](https://docs.agora.io/en/video-calling/get-started/manage-agora-account?platform=web#create-an-agora-project)
    和 [ App Certificate
    ](https://docs.agora.io/en/video-calling/get-started/manage-agora-account?platform=web#create-an-agora-project)
    (每月有免费额度)
  </Tab>
  <Tab>
    <p>
      [Docker](https://www.docker.com/) / [Docker
      Compose](https://docs.docker.com/compose/)
    </p>
    <p>[Node.js(LTS) v18](https://nodejs.org/en)</p>
  </Tab>
  <Tab>
🎉 CPU >= 2 核

😄 内存 >= 4 GB

</Tab>
</Tabs>

**Apple Silicon 上的 Docker 设置**

<Callout>
  对于 Apple Silicon Mac，请在 Docker 设置中取消选中 "Use Rosetta for x86/amd64
  emulation"。注意：这可能会导致在 ARM 上的构建时间变慢，但在部署到 x64
  服务器时性能将恢复正常。
</Callout>

## 下一步

**1. 克隆 TEN Agent 仓库**

```sh title="终端"
git clone https://github.com/TEN-framework/TEN-Agent.git
```

**2. 准备配置文件**

在你的代码编辑器中打开 TEN Agent。在项目根目录中使用 `cd` 命令从 `.env.example` 创建 `.env` 文件。

```sh title="终端"
cp ./.env.example ./.env
```

**3. 在 .env 文件中设置 Agora App ID 和 App Certificate**

打开 `.env` 文件并填写 Agora App ID 和 App Certificate。这些将用于连接 Agora RTC 扩展。

```bash title=".env"
AGORA_APP_ID=
AGORA_APP_CERTIFICATE=
```

**4. 启动 agent 构建工具容器**

在同一目录下，运行 `docker` 命令来组合容器：

```bash title="终端"
docker compose up -d
```

**5. 进入容器**

使用以下命令进入容器：

```bash title="终端"
docker exec -it ten_agent_dev bash
```

**6. 构建 agent**

使用以下命令构建 agent：

```bash title="终端"
task use
```

**7. 启动 Web 服务器**

使用以下命令启动 Web 服务器：

```bash title="终端"
task run
```

**8. 编辑 Playground 设置**

在 [localhost:3000](http://localhost:3000) 打开 Playground 来配置你的 agent。

1. 选择一个图类型 (例如，语音 Agent，实时对话 Agent)
2. 选择一个对应的模块
3. 选择一个扩展并配置其 API 密钥设置
