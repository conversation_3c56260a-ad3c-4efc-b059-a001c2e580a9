{"version": 3, "file": "sync-blocking-checker.js", "sourceRoot": "", "sources": ["../../../src/components/sync-blocking-checker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAY,WAAW,EAAE,SAAS,EAAE,MAAM,aAAa,CAAA;AAE9D,mFAAmF;AACnF,MAAM,IAAI,GACR,UAAU,CAAC,OAAO,EAAE,WAAW;IAC/B,SAAS,WAAW,CAAC,OAAe,EAAE,OAAyB;QAC7D,OAAO,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAA;IACxD,CAAC,CAAA;AAEH,MAAM,UAAU,cAAc,CAAqB,KAAQ;IACzD,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC3C,cAAc,CAAC,GAAG,EAAE;YAClB,KAAK,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;QAChC,CAAC,CAAC,CAAA;QAEF,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC;IAED,IAAI,KAAK,CAAC,WAAW,CAAE,GAAG,IAAI,EAAE,CAAC;QAC/B,IAAI,CACF,uMAAuM,EACvM,EAAE,IAAI,EAAE,WAAW,EAAE,CACtB,CAAA;QAED,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;IACzB,CAAC;SAAM,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACnC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAA;IACtB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAAqB,KAAQ;IACvD,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;QACzC,cAAc,CAAC,GAAG,EAAE;YAClB,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;QAC9B,CAAC,CAAC,CAAA;QAEF,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;IACtB,CAAC;IAED,IAAI,KAAK,CAAC,SAAS,CAAE,GAAG,IAAI,EAAE,CAAC;QAC7B,IAAI,CACF,mMAAmM,EACnM,EAAE,IAAI,EAAE,WAAW,EAAE,CACtB,CAAA;QAED,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;IACvB,CAAC;SAAM,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAA;IACpB,CAAC;AACH,CAAC"}