"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),Object.defineProperty(exports,"stopwords",{enumerable:!0,get:function(){return stopwords}});let stopwords=["पर","इन","वह","यिह","वुह","जिन्हें","जिन्हों","तिन्हें","तिन्हों","किन्हों","किन्हें","इत्यादि","द्वारा","इन्हें","इन्हों","उन्हों","बिलकुल","निहायत","ऱ्वासा","इन्हीं","उन्हीं","उन्हें","इसमें","जितना","दुसरा","कितना","दबारा","साबुत","वग़ैरह","दूसरे","कौनसा","लेकिन","होता","करने","किया","लिये","अपने","नहीं","दिया","इसका","करना","वाले","सकते","इसके","सबसे","होने","करते","बहुत","वर्ग","करें","होती","अपनी","उनके","कहते","होते","करता","उनकी","इसकी","सकता","रखें","अपना","उसके","जिसे","तिसे","किसे","किसी","काफ़ी","पहले","नीचे","बाला","यहाँ","जैसा","जैसे","मानो","अंदर","भीतर","पूरा","सारा","होना","उनको","वहाँ","वहीं","जहाँ","जीधर","उनका","इनका","\uFEFFके","हैं","गया","बनी","एवं","हुआ","साथ","बाद","लिए","कुछ","कहा","यदि","हुई","इसे","हुए","अभी","सभी","कुल","रहा","रहे","इसी","उसे","जिस","जिन","तिस","तिन","कौन","किस","कोई","ऐसे","तरह","किर","साभ","संग","यही","बही","उसी","फिर","मगर","का","एक","यह","से","को","इस","कि","जो","कर","मे","ने","तो","ही","या","हो","था","तक","आप","ये","थे","दो","वे","थी","जा","ना","उस","एस","पे","उन","सो","भी","और","घर","तब","जब","अत","व","न"];