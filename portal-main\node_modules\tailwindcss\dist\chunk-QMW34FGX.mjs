import{a as At,b as _,c as W,d as E,e as rt,f as de,g as _e,h as $t}from"./chunk-P5FH2LZE.mjs";import{a as Ct}from"./chunk-HTB5LLOP.mjs";var Nt="4.1.4";var $e=92,De=47,Ue=42,Mr=34,Wr=39,Br=58,Ie=59,pe=10,Ne=32,Fe=9,Vt=123,it=125,at=40,Tt=41,qr=91,Gr=93,St=45,nt=64,Jr=33;function me(r){r[0]==="\uFEFF"&&(r=r.slice(1)),r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=[],o=null,s=null,l="",d="",u;for(let c=0;c<r.length;c++){let m=r.charCodeAt(c);if(m===$e)l+=r.slice(c,c+2),c+=1;else if(m===De&&r.charCodeAt(c+1)===Ue){let g=c;for(let w=c+2;w<r.length;w++)if(u=r.charCodeAt(w),u===$e)w+=1;else if(u===Ue&&r.charCodeAt(w+1)===De){c=w+1;break}let h=r.slice(g,c+1);h.charCodeAt(2)===Jr&&i.push(ze(h.slice(2,-2)))}else if(m===Wr||m===Mr){let g=c;for(let h=c+1;h<r.length;h++)if(u=r.charCodeAt(h),u===$e)h+=1;else if(u===m){c=h;break}else{if(u===Ie&&r.charCodeAt(h+1)===pe)throw new Error(`Unterminated string: ${r.slice(g,h+1)+String.fromCharCode(m)}`);if(u===pe)throw new Error(`Unterminated string: ${r.slice(g,h)+String.fromCharCode(m)}`)}l+=r.slice(g,c+1)}else{if((m===Ne||m===pe||m===Fe)&&(u=r.charCodeAt(c+1))&&(u===Ne||u===pe||u===Fe))continue;if(m===pe){if(l.length===0)continue;u=l.charCodeAt(l.length-1),u!==Ne&&u!==pe&&u!==Fe&&(l+=" ")}else if(m===St&&r.charCodeAt(c+1)===St&&l.length===0){let g="",h=c,w=-1;for(let A=c+2;A<r.length;A++)if(u=r.charCodeAt(A),u===$e)A+=1;else if(u===De&&r.charCodeAt(A+1)===Ue){for(let k=A+2;k<r.length;k++)if(u=r.charCodeAt(k),u===$e)k+=1;else if(u===Ue&&r.charCodeAt(k+1)===De){A=k+1;break}}else if(w===-1&&u===Br)w=l.length+A-h;else if(u===Ie&&g.length===0){l+=r.slice(h,A),c=A;break}else if(u===at)g+=")";else if(u===qr)g+="]";else if(u===Vt)g+="}";else if((u===it||r.length-1===A)&&g.length===0){c=A-1,l+=r.slice(h,A);break}else(u===Tt||u===Gr||u===it)&&g.length>0&&r[A]===g[g.length-1]&&(g=g.slice(0,-1));let v=ot(l,w);if(!v)throw new Error("Invalid custom property, expected a value");o?o.nodes.push(v):t.push(v),l=""}else if(m===Ie&&l.charCodeAt(0)===nt)s=Ve(l),o?o.nodes.push(s):t.push(s),l="",s=null;else if(m===Ie&&d[d.length-1]!==")"){let g=ot(l);if(!g)throw l.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${l.trim()}\``);o?o.nodes.push(g):t.push(g),l=""}else if(m===Vt&&d[d.length-1]!==")")d+="}",s=B(l.trim()),o&&o.nodes.push(s),e.push(o),o=s,l="",s=null;else if(m===it&&d[d.length-1]!==")"){if(d==="")throw new Error("Missing opening {");if(d=d.slice(0,-1),l.length>0)if(l.charCodeAt(0)===nt)s=Ve(l),o?o.nodes.push(s):t.push(s),l="",s=null;else{let h=l.indexOf(":");if(o){let w=ot(l,h);if(!w)throw new Error(`Invalid declaration: \`${l.trim()}\``);o.nodes.push(w)}}let g=e.pop()??null;g===null&&o&&t.push(o),o=g,l="",s=null}else if(m===at)d+=")",l+="(";else if(m===Tt){if(d[d.length-1]!==")")throw new Error("Missing opening (");d=d.slice(0,-1),l+=")"}else{if(l.length===0&&(m===Ne||m===pe||m===Fe))continue;l+=String.fromCharCode(m)}}}if(l.charCodeAt(0)===nt&&t.push(Ve(l)),d.length>0&&o){if(o.kind==="rule")throw new Error(`Missing closing } at ${o.selector}`);if(o.kind==="at-rule")throw new Error(`Missing closing } at ${o.name} ${o.params}`)}return i.length>0?i.concat(t):t}function Ve(r,t=[]){for(let i=5;i<r.length;i++){let e=r.charCodeAt(i);if(e===Ne||e===at){let o=r.slice(0,i).trim(),s=r.slice(i).trim();return I(o,s,t)}}return I(r.trim(),"",t)}function ot(r,t=r.indexOf(":")){if(t===-1)return null;let i=r.indexOf("!important",t+1);return a(r.slice(0,t).trim(),r.slice(t+1,i===-1?r.length:i).trim(),i!==-1)}function se(r){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let t=String(r),i=t.length,e=-1,o,s="",l=t.charCodeAt(0);if(i===1&&l===45)return"\\"+t;for(;++e<i;){if(o=t.charCodeAt(e),o===0){s+="\uFFFD";continue}if(o>=1&&o<=31||o===127||e===0&&o>=48&&o<=57||e===1&&o>=48&&o<=57&&l===45){s+="\\"+o.toString(16)+" ";continue}if(o>=128||o===45||o===95||o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122){s+=t.charAt(e);continue}s+="\\"+t.charAt(e)}return s}function ge(r){return r.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,t=>t.length>2?String.fromCodePoint(Number.parseInt(t.slice(1).trim(),16)):t[1])}var Rt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function Et(r,t){return(Rt.get(t)??[]).some(i=>r===i||r.startsWith(`${i}-`))}var Le=class{constructor(t=new Map,i=new Set([])){this.values=t;this.keyframes=i}prefix=null;add(t,i,e=0){if(t.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${t}\``);t==="--*"?this.values.clear():this.clearNamespace(t.slice(0,-2),0)}if(e&4){let o=this.values.get(t);if(o&&!(o.options&4))return}i==="initial"?this.values.delete(t):this.values.set(t,{value:i,options:e})}keysInNamespaces(t){let i=[];for(let e of t){let o=`${e}-`;for(let s of this.values.keys())s.startsWith(o)&&s.indexOf("--",2)===-1&&(Et(s,e)||i.push(s.slice(o.length)))}return i}get(t){for(let i of t){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(t){return(this.getOptions(t)&4)===4}getOptions(t){return t=ge(this.#r(t)),this.values.get(t)?.options??0}entries(){return this.prefix?Array.from(this.values,t=>(t[0]=this.prefixKey(t[0]),t)):this.values.entries()}prefixKey(t){return this.prefix?`--${this.prefix}-${t.slice(2)}`:t}#r(t){return this.prefix?`--${t.slice(3+this.prefix.length)}`:t}clearNamespace(t,i){let e=Rt.get(t)??[];e:for(let o of this.values.keys())if(o.startsWith(t)){if(i!==0&&(this.getOptions(o)&i)!==i)continue;for(let s of e)if(o.startsWith(s))continue e;this.values.delete(o)}}#e(t,i){for(let e of i){let o=t!==null?`${e}-${t}`:e;if(!this.values.has(o))if(t!==null&&t.includes(".")){if(o=`${e}-${t.replaceAll(".","_")}`,!this.values.has(o))continue}else continue;if(!Et(o,e))return o}return null}#t(t){let i=this.values.get(t);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${se(this.prefixKey(t))}${e?`, ${e}`:""})`}markUsedVariable(t){let i=ge(this.#r(t)),e=this.values.get(i);if(!e)return!1;let o=e.options&16;return e.options|=16,!o}resolve(t,i,e=0){let o=this.#e(t,i);if(!o)return null;let s=this.values.get(o);return(e|s.options)&1?s.value:this.#t(o)}resolveValue(t,i){let e=this.#e(t,i);return e?this.values.get(e).value:null}resolveWith(t,i,e=[]){let o=this.#e(t,i);if(!o)return null;let s={};for(let d of e){let u=`${o}${d}`,c=this.values.get(u);c&&(c.options&1?s[d]=c.value:s[d]=this.#t(u))}let l=this.values.get(o);return l.options&1?[l.value,s]:[this.#t(o),s]}namespace(t){let i=new Map,e=`${t}-`;for(let[o,s]of this.values)o===t?i.set(null,s.value):o.startsWith(`${e}-`)?i.set(o.slice(t.length),s.value):o.startsWith(e)&&i.set(o.slice(e.length),s.value);return i}addKeyframes(t){this.keyframes.add(t)}getKeyframes(){return Array.from(this.keyframes)}};var q=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function st(r){return{kind:"word",value:r}}function Hr(r,t){return{kind:"function",value:r,nodes:t}}function Yr(r){return{kind:"separator",value:r}}function ee(r,t,i=null){for(let e=0;e<r.length;e++){let o=r[e],s=!1,l=0,d=t(o,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(r.splice(e,1),l=0):u.length===1?(r[e]=u[0],l=1):(r.splice(e,1,...u),l=u.length):r[e]=u)}})??0;if(s){d===0?e--:e+=l-1;continue}if(d===2)return 2;if(d!==1&&o.kind==="function"&&ee(o.nodes,t,o)===2)return 2}}function Y(r){let t="";for(let i of r)switch(i.kind){case"word":case"separator":{t+=i.value;break}case"function":t+=i.value+"("+Y(i.nodes)+")"}return t}var Kt=92,Zr=41,Pt=58,Ot=44,Qr=34,jt=61,_t=62,Dt=60,Ut=10,Xr=40,ei=39,It=47,Ft=32,zt=9;function G(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,o="",s;for(let l=0;l<r.length;l++){let d=r.charCodeAt(l);switch(d){case Kt:{o+=r[l]+r[l+1],l++;break}case Pt:case Ot:case jt:case _t:case Dt:case Ut:case It:case Ft:case zt:{if(o.length>0){let g=st(o);e?e.nodes.push(g):t.push(g),o=""}let u=l,c=l+1;for(;c<r.length&&(s=r.charCodeAt(c),!(s!==Pt&&s!==Ot&&s!==jt&&s!==_t&&s!==Dt&&s!==Ut&&s!==It&&s!==Ft&&s!==zt));c++);l=c-1;let m=Yr(r.slice(u,c));e?e.nodes.push(m):t.push(m);break}case ei:case Qr:{let u=l;for(let c=l+1;c<r.length;c++)if(s=r.charCodeAt(c),s===Kt)c+=1;else if(s===d){l=c;break}o+=r.slice(u,l+1);break}case Xr:{let u=Hr(o,[]);o="",e?e.nodes.push(u):t.push(u),i.push(u),e=u;break}case Zr:{let u=i.pop();if(o.length>0){let c=st(o);u.nodes.push(c),o=""}i.length>0?e=i[i.length-1]:e=null;break}default:o+=String.fromCharCode(d)}}return o.length>0&&t.push(st(o)),t}function Me(r){let t=[];return ee(G(r),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||t.push(e.value)}),1}),t}var ri=64;function L(r,t=[]){return{kind:"rule",selector:r,nodes:t}}function I(r,t="",i=[]){return{kind:"at-rule",name:r,params:t,nodes:i}}function B(r,t=[]){return r.charCodeAt(0)===ri?Ve(r,t):L(r,t)}function a(r,t,i=!1){return{kind:"declaration",property:r,value:t,important:i}}function ze(r){return{kind:"comment",value:r}}function ne(r,t){return{kind:"context",context:r,nodes:t}}function D(r){return{kind:"at-root",nodes:r}}function F(r,t,i=[],e={}){for(let o=0;o<r.length;o++){let s=r[o],l=i[i.length-1]??null;if(s.kind==="context"){if(F(s.nodes,t,i,{...e,...s.context})===2)return 2;continue}i.push(s);let d=!1,u=0,c=t(s,{parent:l,context:e,path:i,replaceWith(m){d||(d=!0,Array.isArray(m)?m.length===0?(r.splice(o,1),u=0):m.length===1?(r[o]=m[0],u=1):(r.splice(o,1,...m),u=m.length):(r[o]=m,u=1))}})??0;if(i.pop(),d){c===0?o--:o+=u-1;continue}if(c===2)return 2;if(c!==1&&"nodes"in s){i.push(s);let m=F(s.nodes,t,i,e);if(i.pop(),m===2)return 2}}}function We(r,t,i=[],e={}){for(let o=0;o<r.length;o++){let s=r[o],l=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),We(s.nodes,t,i,e),i.pop();else if(s.kind==="context"){We(s.nodes,t,i,{...e,...s.context});continue}i.push(s),t(s,{parent:l,context:e,path:i,replaceWith(d){Array.isArray(d)?d.length===0?r.splice(o,1):d.length===1?r[o]=d[0]:r.splice(o,1,...d):r[o]=d,o+=d.length-1}}),i.pop()}}function he(r,t,i=3){let e=[],o=new Set,s=new q(()=>new Set),l=new q(()=>new Set),d=new Set,u=new Set,c=[],m=[],g=new q(()=>new Set);function h(v,A,k={},b=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(k.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}k.keyframes||s.get(A).add(v)}if(v.value.includes("var("))if(k.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let V of Me(v.value))g.get(V).add(v.property);else t.trackUsedVariables(v.value);if(v.property==="animation")for(let V of Lt(v.value))u.add(V);i&2&&v.value.includes("color-mix(")&&l.get(A).add(v),A.push(v)}else if(v.kind==="rule")if(v.selector==="&")for(let V of v.nodes){let S=[];h(V,S,k,b+1),S.length>0&&A.push(...S)}else{let V={...v,nodes:[]};for(let S of v.nodes)h(S,V.nodes,k,b+1);V.nodes.length>0&&A.push(V)}else if(v.kind==="at-rule"&&v.name==="@property"&&b===0){if(o.has(v.params))return;if(i&1){let S=v.params,K=null,O=!1;for(let U of v.nodes)U.kind==="declaration"&&(U.property==="initial-value"?K=U.value:U.property==="inherits"&&(O=U.value==="true"));O?c.push(a(S,K??"initial")):m.push(a(S,K??"initial"))}o.add(v.params);let V={...v,nodes:[]};for(let S of v.nodes)h(S,V.nodes,k,b+1);A.push(V)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(k={...k,keyframes:!0});let V={...v,nodes:[]};for(let S of v.nodes)h(S,V.nodes,k,b+1);v.name==="@keyframes"&&k.theme&&d.add(V),(V.nodes.length>0||V.name==="@layer"||V.name==="@charset"||V.name==="@custom-media"||V.name==="@namespace"||V.name==="@import")&&A.push(V)}else if(v.kind==="at-root")for(let V of v.nodes){let S=[];h(V,S,k,0);for(let K of S)e.push(K)}else if(v.kind==="context"){if(v.context.reference)return;for(let V of v.nodes)h(V,A,{...k,...v.context},b)}else v.kind==="comment"&&A.push(v)}let w=[];for(let v of r)h(v,w,{},0);e:for(let[v,A]of s)for(let k of A){if(Mt(k.property,t.theme,g)){if(k.property.startsWith(t.theme.prefixKey("--animate-")))for(let S of Lt(k.value))u.add(S);continue}let V=v.indexOf(k);if(v.splice(V,1),v.length===0){let S=ii(w,K=>K.kind==="rule"&&K.nodes===v);if(!S||S.length===0)continue e;S.unshift({kind:"at-root",nodes:w});do{let K=S.pop();if(!K)break;let O=S[S.length-1];if(!O||O.kind!=="at-root"&&O.kind!=="at-rule")break;let U=O.nodes.indexOf(K);if(U===-1)break;O.nodes.splice(U,1)}while(!0);continue e}}for(let v of d)if(!u.has(v.params)){let A=e.indexOf(v);e.splice(A,1)}if(w=w.concat(e),i&2)for(let[v,A]of l)for(let k of A){let b=v.indexOf(k);if(b===-1||k.value==null)continue;let V=G(k.value),S=!1;if(ee(V,(U,{replaceWith:P})=>{if(U.kind!=="function"||U.value!=="color-mix")return;let H=!1,M=!1;if(ee(U.nodes,(z,{replaceWith:te})=>{if(z.kind=="word"&&z.value.toLowerCase()==="currentcolor"){M=!0,S=!0;return}let X=z,ae=null,n=new Set;do{if(X.kind!=="function"||X.value!=="var")return;let f=X.nodes[0];if(!f||f.kind!=="word")return;let p=f.value;if(n.has(p)){H=!0;return}if(n.add(p),S=!0,ae=t.theme.resolveValue(null,[f.value]),!ae){H=!0;return}if(ae.toLowerCase()==="currentcolor"){M=!0;return}ae.startsWith("var(")?X=G(ae)[0]:X=null}while(X);te({kind:"word",value:ae})}),H||M){let z=U.nodes.findIndex(X=>X.kind==="separator"&&X.value.trim().includes(","));if(z===-1)return;let te=U.nodes.length>z?U.nodes[z+1]:null;if(!te)return;P(te)}else if(S){let z=U.nodes[2];z.kind==="word"&&(z.value==="oklab"||z.value==="oklch"||z.value==="lab"||z.value==="lch")&&(z.value="srgb")}}),!S)continue;let K={...k,value:Y(V)},O=B("@supports (color: color-mix(in lab, red, red))",[k]);v.splice(b,1,K,O)}if(i&1){let v=[];if(c.length>0&&v.push(B(":root, :host",c)),m.length>0&&v.push(B("*, ::before, ::after, ::backdrop",m)),v.length>0){let A=w.findIndex(k=>!(k.kind==="comment"||k.kind==="at-rule"&&(k.name==="@charset"||k.name==="@import")));w.splice(A<0?w.length:A,0,I("@layer","properties",[])),w.push(B("@layer properties",[I("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]))}}return w}function re(r){function t(e,o=0){let s="",l="  ".repeat(o);if(e.kind==="declaration")s+=`${l}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){s+=`${l}${e.selector} {
`;for(let d of e.nodes)s+=t(d,o+1);s+=`${l}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${l}${e.name} ${e.params};
`;s+=`${l}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let d of e.nodes)s+=t(d,o+1);s+=`${l}}
`}else if(e.kind==="comment")s+=`${l}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return s}let i="";for(let e of r){let o=t(e);o!==""&&(i+=o)}return i}function ii(r,t){let i=[];return F(r,(e,{path:o})=>{if(t(e))return i=[...o],2}),i}function Mt(r,t,i,e=new Set){if(e.has(r)||(e.add(r),t.getOptions(r)&24))return!0;{let s=i.get(r)??[];for(let l of s)if(Mt(l,t,i,e))return!0}return!1}function Lt(r){return r.split(/[\s,]+/)}function ue(r){if(r.indexOf("(")===-1)return ke(r);let t=G(r);return ft(t),r=Y(t),r=At(r),r}function ke(r,t=!1){let i="";for(let e=0;e<r.length;e++){let o=r[e];o==="\\"&&r[e+1]==="_"?(i+="_",e+=1):o==="_"&&!t?i+=" ":i+=o}return i}function ft(r){for(let t of r)switch(t.kind){case"function":{if(t.value==="url"||t.value.endsWith("_url")){t.value=ke(t.value);break}if(t.value==="var"||t.value.endsWith("_var")||t.value==="theme"||t.value.endsWith("_theme")){t.value=ke(t.value);for(let i=0;i<t.nodes.length;i++){if(i==0&&t.nodes[i].kind==="word"){t.nodes[i].value=ke(t.nodes[i].value,!0);continue}ft([t.nodes[i]])}break}t.value=ke(t.value),ft(t.nodes);break}case"separator":case"word":{t.value=ke(t.value);break}default:ni(t)}}function ni(r){throw new Error(`Unexpected value: ${r}`)}var ct=new Uint8Array(256);function fe(r){let t=0,i=r.length;for(let e=0;e<i;e++){let o=r.charCodeAt(e);switch(o){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=r.charCodeAt(e);if(s===92){e+=1;continue}if(s===o)break}break;case 40:ct[t]=41,t++;break;case 91:ct[t]=93,t++;break;case 123:break;case 93:case 125:case 41:if(t===0)return!1;t>0&&o===ct[t-1]&&t--;break;case 59:if(t===0)return!1;break}}return!0}var oi=58,Wt=45,Bt=97,qt=122;function*Gt(r,t){let i=_(r,":");if(t.theme.prefix){if(i.length===1||i[0]!==t.theme.prefix)return null;i.shift()}let e=i.pop(),o=[];for(let g=i.length-1;g>=0;--g){let h=t.parseVariant(i[g]);if(h===null)return;o.push(h)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),t.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:o,important:s,raw:r});let[l,d=null,u]=_(e,"/");if(u)return;let c=d===null?null:dt(d);if(d!==null&&c===null)return;if(l[0]==="["){if(l[l.length-1]!=="]")return;let g=l.charCodeAt(1);if(g!==Wt&&!(g>=Bt&&g<=qt))return;l=l.slice(1,-1);let h=l.indexOf(":");if(h===-1||h===0||h===l.length-1)return;let w=l.slice(0,h),v=ue(l.slice(h+1));if(!fe(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:c,variants:o,important:s,raw:r};return}let m;if(l[l.length-1]==="]"){let g=l.indexOf("-[");if(g===-1)return;let h=l.slice(0,g);if(!t.utilities.has(h,"functional"))return;let w=l.slice(g+1);m=[[h,w]]}else if(l[l.length-1]===")"){let g=l.indexOf("-(");if(g===-1)return;let h=l.slice(0,g);if(!t.utilities.has(h,"functional"))return;let w=l.slice(g+2,-1),v=_(w,":"),A=null;if(v.length===2&&(A=v[0],w=v[1]),w[0]!=="-"&&w[1]!=="-")return;m=[[h,A===null?`[var(${w})]`:`[${A}:var(${w})]`]]}else m=Ht(l,g=>t.utilities.has(g,"functional"));for(let[g,h]of m){let w={kind:"functional",root:g,modifier:c,value:null,variants:o,important:s,raw:r};if(h===null){yield w;continue}{let v=h.indexOf("[");if(v!==-1){if(h[h.length-1]!=="]")return;let k=ue(h.slice(v+1,-1));if(!fe(k))continue;let b="";for(let V=0;V<k.length;V++){let S=k.charCodeAt(V);if(S===oi){b=k.slice(0,V),k=k.slice(V+1);break}if(!(S===Wt||S>=Bt&&S<=qt))break}if(k.length===0||k.trim().length===0)continue;w.value={kind:"arbitrary",dataType:b||null,value:k}}else{let k=d===null||w.modifier?.kind==="arbitrary"?null:`${h}/${d}`;w.value={kind:"named",value:h,fraction:k}}}yield w}}function dt(r){if(r[0]==="["&&r[r.length-1]==="]"){let t=ue(r.slice(1,-1));return!fe(t)||t.length===0||t.trim().length===0?null:{kind:"arbitrary",value:t}}if(r[0]==="("&&r[r.length-1]===")"){let t=ue(r.slice(1,-1));return!fe(t)||t.length===0||t.trim().length===0||t[0]!=="-"&&t[1]!=="-"?null:{kind:"arbitrary",value:`var(${t})`}}return{kind:"named",value:r}}function Jt(r,t){if(r[0]==="["&&r[r.length-1]==="]"){if(r[1]==="@"&&r.includes("&"))return null;let i=ue(r.slice(1,-1));if(!fe(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,o]=_(r,"/");if(o)return null;let s=Ht(i,l=>t.variants.has(l));for(let[l,d]of s)switch(t.variants.kind(l)){case"static":return d!==null||e!==null?null:{kind:"static",root:l};case"functional":{let u=e===null?null:dt(e);if(e!==null&&u===null)return null;if(d===null)return{kind:"functional",root:l,modifier:u,value:null};if(d[d.length-1]==="]"){if(d[0]!=="[")continue;let c=ue(d.slice(1,-1));return!fe(c)||c.length===0||c.trim().length===0?null:{kind:"functional",root:l,modifier:u,value:{kind:"arbitrary",value:c}}}if(d[d.length-1]===")"){if(d[0]!=="(")continue;let c=ue(d.slice(1,-1));return!fe(c)||c.length===0||c.trim().length===0||c[0]!=="-"&&c[1]!=="-"?null:{kind:"functional",root:l,modifier:u,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:l,modifier:u,value:{kind:"named",value:d}}}case"compound":{if(d===null)return null;let u=t.parseVariant(d);if(u===null||!t.variants.compoundsWith(l,u))return null;let c=e===null?null:dt(e);return e!==null&&c===null?null:{kind:"compound",root:l,modifier:c,variant:u}}}}return null}function*Ht(r,t){t(r)&&(yield[r,null]);let i=r.lastIndexOf("-");for(;i>0;){let e=r.slice(0,i);if(t(e)){let o=[e,r.slice(i+1)];if(o[1]==="")break;yield o}i=r.lastIndexOf("-",i-1)}r[0]==="@"&&t("@")&&(yield["@",r.slice(1)])}function ve(r,t,i){if(r===t)return 0;let e=r.indexOf("("),o=t.indexOf("("),s=e===-1?r.replace(/[\d.]+/g,""):r.slice(0,e),l=o===-1?t.replace(/[\d.]+/g,""):t.slice(0,o),d=(s===l?0:s<l?-1:1)||(i==="asc"?parseInt(r)-parseInt(t):parseInt(t)-parseInt(r));return Number.isNaN(d)?r<t?-1:1:d}var ai=new Set(["inset","inherit","initial","revert","unset"]),Yt=/^-?(\d+|\.\d+)(.*?)$/g;function Te(r,t){return _(r,",").map(e=>{e=e.trim();let o=_(e," ").filter(c=>c.trim()!==""),s=null,l=null,d=null;for(let c of o)ai.has(c)||(Yt.test(c)?(l===null?l=c:d===null&&(d=c),Yt.lastIndex=0):s===null&&(s=c));if(l===null||d===null)return e;let u=t(s??"currentcolor");return s!==null?e.replace(s,u):`${e} ${u}`}).join(", ")}var li=/^-?[a-z][a-zA-Z0-9/%._-]*$/,si=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,Ge=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],pt=class{utilities=new q(()=>[]);completions=new Map;static(t,i){this.utilities.get(t).push({kind:"static",compileFn:i})}functional(t,i,e){this.utilities.get(t).push({kind:"functional",compileFn:i,options:e})}has(t,i){return this.utilities.has(t)&&this.utilities.get(t).some(e=>e.kind===i)}get(t){return this.utilities.has(t)?this.utilities.get(t):[]}getCompletions(t){return this.completions.get(t)?.()??[]}suggest(t,i){this.completions.set(t,i)}keys(t){let i=[];for(let[e,o]of this.utilities.entries())for(let s of o)if(s.kind===t){i.push(e);break}return i}};function C(r,t,i){return I("@property",r,[a("syntax",i?`"${i}"`:'"*"'),a("inherits","false"),...t?[a("initial-value",t)]:[]])}function J(r,t){if(t===null)return r;let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),`color-mix(in oklab, ${r} ${t}, transparent)`}function Qt(r,t){let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),`oklab(from ${r} l a b / ${t})`}function Z(r,t,i){if(!t)return r;if(t.kind==="arbitrary")return J(r,t.value);let e=i.resolve(t.value,["--opacity"]);return e?J(r,e):_e(t.value)?J(r,`${t.value}%`):null}function Q(r,t,i){let e=null;switch(r.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=t.resolve(r.value.value,i);break}}return e?Z(e,r.modifier,t):null}function Xt(r){let t=new pt;function i(n,f){let p=/(\d+)_(\d+)/g;function*x($){for(let R of r.keysInNamespaces($))yield R.replace(p,(N,T,j)=>`${T}.${j}`)}let y=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];t.suggest(n,()=>{let $=[];for(let R of f()){if(typeof R=="string"){$.push({values:[R],modifiers:[]});continue}let N=[...R.values??[],...x(R.valueThemeKeys??[])],T=[...R.modifiers??[],...x(R.modifierThemeKeys??[])];R.supportsFractions&&N.push(...y),R.hasDefaultValue&&N.unshift(null),$.push({supportsNegative:R.supportsNegative,values:N,modifiers:T})}return $})}function e(n,f){t.static(n,()=>f.map(p=>typeof p=="function"?p():a(p[0],p[1])))}function o(n,f){function p({negative:x}){return y=>{let $=null,R=null;if(y.value)if(y.value.kind==="arbitrary"){if(y.modifier)return;$=y.value.value,R=y.value.dataType}else{if($=r.resolve(y.value.fraction??y.value.value,f.themeKeys??[]),$===null&&f.supportsFractions&&y.value.fraction){let[N,T]=_(y.value.fraction,"/");if(!E(N)||!E(T))return;$=`calc(${y.value.fraction} * 100%)`}if($===null&&x&&f.handleNegativeBareValue){if($=f.handleNegativeBareValue(y.value),!$?.includes("/")&&y.modifier)return;if($!==null)return f.handle($,null)}if($===null&&f.handleBareValue&&($=f.handleBareValue(y.value),!$?.includes("/")&&y.modifier))return}else{if(y.modifier)return;$=f.defaultValue!==void 0?f.defaultValue:r.resolve(null,f.themeKeys??[])}if($!==null)return f.handle(x?`calc(${$} * -1)`:$,R)}}f.supportsNegative&&t.functional(`-${n}`,p({negative:!0})),t.functional(n,p({negative:!1})),i(n,()=>[{supportsNegative:f.supportsNegative,valueThemeKeys:f.themeKeys??[],hasDefaultValue:f.defaultValue!==void 0&&f.defaultValue!==null,supportsFractions:f.supportsFractions}])}function s(n,f){t.functional(n,p=>{if(!p.value)return;let x=null;if(p.value.kind==="arbitrary"?(x=p.value.value,x=Z(x,p.modifier,r)):x=Q(p,r,f.themeKeys),x!==null)return f.handle(x)}),i(n,()=>[{values:["current","inherit","transparent"],valueThemeKeys:f.themeKeys,modifiers:Array.from({length:21},(p,x)=>`${x*5}`)}])}function l(n,f,p,{supportsNegative:x=!1,supportsFractions:y=!1}={}){x&&t.static(`-${n}-px`,()=>p("-1px")),t.static(`${n}-px`,()=>p("1px")),o(n,{themeKeys:f,supportsFractions:y,supportsNegative:x,defaultValue:null,handleBareValue:({value:$})=>{let R=r.resolve(null,["--spacing"]);return!R||!de($)?null:`calc(${R} * ${$})`},handleNegativeBareValue:({value:$})=>{let R=r.resolve(null,["--spacing"]);return!R||!de($)?null:`calc(${R} * -${$})`},handle:p}),i(n,()=>[{values:r.get(["--spacing"])?Ge:[],supportsNegative:x,supportsFractions:y,valueThemeKeys:f}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[n,f]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${n}-auto`,[[f,"auto"]]),e(`${n}-full`,[[f,"100%"]]),e(`-${n}-full`,[[f,"-100%"]]),l(n,["--inset","--spacing"],p=>[a(f,p)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),o("z",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--z-index"],handle:n=>[a("z-index",n)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),o("order",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--order"],handle:n=>[a("order",n)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),o("col",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-column"],handle:n=>[a("grid-column",n)]}),e("col-span-full",[["grid-column","1 / -1"]]),o("col-span",{handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("grid-column",`span ${n} / span ${n}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),o("col-start",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-column-start"],handle:n=>[a("grid-column-start",n)]}),e("col-end-auto",[["grid-column-end","auto"]]),o("col-end",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-column-end"],handle:n=>[a("grid-column-end",n)]}),i("col-span",()=>[{values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),o("row",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-row"],handle:n=>[a("grid-row",n)]}),e("row-span-full",[["grid-row","1 / -1"]]),o("row-span",{themeKeys:[],handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("grid-row",`span ${n} / span ${n}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),o("row-start",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-row-start"],handle:n=>[a("grid-row-start",n)]}),e("row-end-auto",[["grid-row-end","auto"]]),o("row-end",{supportsNegative:!0,handleBareValue:({value:n})=>E(n)?n:null,themeKeys:["--grid-row-end"],handle:n=>[a("grid-row-end",n)]}),i("row-span",()=>[{values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[n,f]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${n}-auto`,[[f,"auto"]]),l(n,["--margin","--spacing"],p=>[a(f,p)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),o("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("overflow","hidden"),a("display","-webkit-box"),a("-webkit-box-orient","vertical"),a("-webkit-line-clamp",n)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),o("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:n})=>{if(n===null)return null;let[f,p]=_(n,"/");return!E(f)||!E(p)?null:n},handle:n=>[a("aspect-ratio",n)]});for(let[n,f]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${n}`,[["--tw-sort","size"],["width",f],["height",f]]),e(`w-${n}`,[["width",f]]),e(`h-${n}`,[["height",f]]),e(`min-w-${n}`,[["min-width",f]]),e(`min-h-${n}`,[["min-height",f]]),n!=="auto"&&(e(`max-w-${n}`,[["max-width",f]]),e(`max-h-${n}`,[["max-height",f]]));e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),l("size",["--size","--spacing"],n=>[a("--tw-sort","size"),a("width",n),a("height",n)],{supportsFractions:!0});for(let[n,f,p]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])l(n,f,x=>[a(p,x)],{supportsFractions:!0});t.static("container",()=>{let n=[...r.namespace("--breakpoint").values()];n.sort((p,x)=>ve(p,x,"asc"));let f=[a("--tw-sort","--tw-container-component"),a("width","100%")];for(let p of n)f.push(I("@media",`(width >= ${p})`,[a("max-width",p)]));return f}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),t.functional("flex",n=>{if(n.value){if(n.value.kind==="arbitrary")return n.modifier?void 0:[a("flex",n.value.value)];if(n.value.fraction){let[f,p]=_(n.value.fraction,"/");return!E(f)||!E(p)?void 0:[a("flex",`calc(${n.value.fraction} * 100%)`)]}if(E(n.value.value))return n.modifier?void 0:[a("flex",n.value.value)]}}),i("flex",()=>[{supportsFractions:!0}]),o("shrink",{defaultValue:"1",handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("flex-shrink",n)]}),o("grow",{defaultValue:"1",handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("flex-grow",n)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),l("basis",["--flex-basis","--spacing","--container"],n=>[a("flex-basis",n)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let d=()=>D([C("--tw-border-spacing-x","0","<length>"),C("--tw-border-spacing-y","0","<length>")]);l("border-spacing",["--border-spacing","--spacing"],n=>[d(),a("--tw-border-spacing-x",n),a("--tw-border-spacing-y",n),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-x",["--border-spacing","--spacing"],n=>[d(),a("--tw-border-spacing-x",n),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-y",["--border-spacing","--spacing"],n=>[d(),a("--tw-border-spacing-y",n),a("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),o("origin",{themeKeys:["--transform-origin"],handle:n=>[a("transform-origin",n)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),o("perspective-origin",{themeKeys:["--perspective-origin"],handle:n=>[a("perspective-origin",n)]}),e("perspective-none",[["perspective","none"]]),o("perspective",{themeKeys:["--perspective"],handle:n=>[a("perspective",n)]});let u=()=>D([C("--tw-translate-x","0"),C("--tw-translate-y","0"),C("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l("translate",["--translate","--spacing"],n=>[u(),a("--tw-translate-x",n),a("--tw-translate-y",n),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let n of["x","y"])e(`-translate-${n}-full`,[u,[`--tw-translate-${n}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${n}-full`,[u,[`--tw-translate-${n}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l(`translate-${n}`,["--translate","--spacing"],f=>[u(),a(`--tw-translate-${n}`,f),a("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});l("translate-z",["--translate","--spacing"],n=>[u(),a("--tw-translate-z",n),a("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>D([C("--tw-scale-x","1"),C("--tw-scale-y","1"),C("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function m({negative:n}){return f=>{if(!f.value||f.modifier)return;let p;return f.value.kind==="arbitrary"?(p=f.value.value,[a("scale",p)]):(p=r.resolve(f.value.value,["--scale"]),!p&&E(f.value.value)&&(p=`${f.value.value}%`),p?(p=n?`calc(${p} * -1)`:p,[c(),a("--tw-scale-x",p),a("--tw-scale-y",p),a("--tw-scale-z",p),a("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}t.functional("-scale",m({negative:!0})),t.functional("scale",m({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let n of["x","y","z"])o(`scale-${n}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:f})=>E(f)?`${f}%`:null,handle:f=>[c(),a(`--tw-scale-${n}`,f),a("scale",`var(--tw-scale-x) var(--tw-scale-y)${n==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${n}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function g({negative:n}){return f=>{if(!f.value||f.modifier)return;let p;if(f.value.kind==="arbitrary"){p=f.value.value;let x=f.value.dataType??W(p,["angle","vector"]);if(x==="vector")return[a("rotate",`${p} var(--tw-rotate)`)];if(x!=="angle")return[a("rotate",p)]}else if(p=r.resolve(f.value.value,["--rotate"]),!p&&E(f.value.value)&&(p=`${f.value.value}deg`),!p)return;return[a("rotate",n?`calc(${p} * -1)`:p)]}}t.functional("-rotate",g({negative:!0})),t.functional("rotate",g({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let n=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),f=()=>D([C("--tw-rotate-x"),C("--tw-rotate-y"),C("--tw-rotate-z"),C("--tw-skew-x"),C("--tw-skew-y")]);for(let p of["x","y","z"])o(`rotate-${p}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:x})=>E(x)?`${x}deg`:null,handle:x=>[f(),a(`--tw-rotate-${p}`,`rotate${p.toUpperCase()}(${x})`),a("transform",n)]}),i(`rotate-${p}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);o("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:p})=>E(p)?`${p}deg`:null,handle:p=>[f(),a("--tw-skew-x",`skewX(${p})`),a("--tw-skew-y",`skewY(${p})`),a("transform",n)]}),o("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:p})=>E(p)?`${p}deg`:null,handle:p=>[f(),a("--tw-skew-x",`skewX(${p})`),a("transform",n)]}),o("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:p})=>E(p)?`${p}deg`:null,handle:p=>[f(),a("--tw-skew-y",`skewY(${p})`),a("transform",n)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),t.functional("transform",p=>{if(p.modifier)return;let x=null;if(p.value?p.value.kind==="arbitrary"&&(x=p.value.value):x=n,x!==null)return[f(),a("transform",x)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",n]]),e("transform-gpu",[["transform",`translateZ(0) ${n}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let n of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${n}`,[["cursor",n]]);o("cursor",{themeKeys:["--cursor"],handle:n=>[a("cursor",n)]});for(let n of["auto","none","manipulation"])e(`touch-${n}`,[["touch-action",n]]);let h=()=>D([C("--tw-pan-x"),C("--tw-pan-y"),C("--tw-pinch-zoom")]);for(let n of["x","left","right"])e(`touch-pan-${n}`,[h,["--tw-pan-x",`pan-${n}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let n of["y","up","down"])e(`touch-pan-${n}`,[h,["--tw-pan-y",`pan-${n}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[h,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let n of["none","text","all","auto"])e(`select-${n}`,[["-webkit-user-select",n],["user-select",n]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let w=()=>D([C("--tw-scroll-snap-strictness","proximity","*")]);for(let n of["x","y","both"])e(`snap-${n}`,[w,["scroll-snap-type",`${n} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[n,f]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])l(n,["--scroll-margin","--spacing"],p=>[a(f,p)],{supportsNegative:!0});for(let[n,f]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])l(n,["--scroll-padding","--spacing"],p=>[a(f,p)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),o("list",{themeKeys:["--list-style-type"],handle:n=>[a("list-style-type",n)]}),e("list-image-none",[["list-style-image","none"]]),o("list-image",{themeKeys:["--list-style-image"],handle:n=>[a("list-style-image",n)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),o("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:n})=>E(n)?n:null,handle:n=>[a("columns",n)]}),i("columns",()=>[{values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:["--columns","--container"]}]);for(let n of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${n}`,[["break-before",n]]);for(let n of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${n}`,[["break-inside",n]]);for(let n of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${n}`,[["break-after",n]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),o("auto-cols",{themeKeys:["--grid-auto-columns"],handle:n=>[a("grid-auto-columns",n)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),o("auto-rows",{themeKeys:["--grid-auto-rows"],handle:n=>[a("grid-auto-rows",n)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),o("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:n})=>rt(n)?`repeat(${n}, minmax(0, 1fr))`:null,handle:n=>[a("grid-template-columns",n)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),o("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:n})=>rt(n)?`repeat(${n}, minmax(0, 1fr))`:null,handle:n=>[a("grid-template-rows",n)]}),i("grid-cols",()=>[{values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(n,f)=>`${f+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),l("gap",["--gap","--spacing"],n=>[a("gap",n)]),l("gap-x",["--gap","--spacing"],n=>[a("column-gap",n)]),l("gap-y",["--gap","--spacing"],n=>[a("row-gap",n)]),l("space-x",["--space","--spacing"],n=>[D([C("--tw-space-x-reverse","0")]),L(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","0"),a("margin-inline-start",`calc(${n} * var(--tw-space-x-reverse))`),a("margin-inline-end",`calc(${n} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),l("space-y",["--space","--spacing"],n=>[D([C("--tw-space-y-reverse","0")]),L(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","0"),a("margin-block-start",`calc(${n} * var(--tw-space-y-reverse))`),a("margin-block-end",`calc(${n} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>D([C("--tw-space-x-reverse","0")]),()=>L(":where(& > :not(:last-child))",[a("--tw-sort","row-gap"),a("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>D([C("--tw-space-y-reverse","0")]),()=>L(":where(& > :not(:last-child))",[a("--tw-sort","column-gap"),a("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:n=>[a("accent-color",n)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:n=>[a("caret-color",n)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:n=>[L(":where(& > :not(:last-child))",[a("--tw-sort","divide-color"),a("border-color",n)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let n of["auto","hidden","clip","visible","scroll"])e(`overflow-${n}`,[["overflow",n]]),e(`overflow-x-${n}`,[["overflow-x",n]]),e(`overflow-y-${n}`,[["overflow-y",n]]);for(let n of["auto","contain","none"])e(`overscroll-${n}`,[["overscroll-behavior",n]]),e(`overscroll-x-${n}`,[["overscroll-behavior-x",n]]),e(`overscroll-y-${n}`,[["overscroll-behavior-y",n]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[n,f]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${n}-none`,f.map(p=>[p,"0"])),e(`${n}-full`,f.map(p=>[p,"calc(infinity * 1px)"])),o(n,{themeKeys:["--radius"],handle:p=>f.map(x=>a(x,p))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let f=function(p,x){t.functional(p,y=>{if(!y.value){if(y.modifier)return;let $=r.get(["--default-border-width"])??"1px",R=x.width($);return R?[n(),...R]:void 0}if(y.value.kind==="arbitrary"){let $=y.value.value;switch(y.value.dataType??W($,["color","line-width","length"])){case"line-width":case"length":{if(y.modifier)return;let N=x.width($);return N?[n(),...N]:void 0}default:return $=Z($,y.modifier,r),$===null?void 0:x.color($)}}{let $=Q(y,r,["--border-color","--color"]);if($)return x.color($)}{if(y.modifier)return;let $=r.resolve(y.value.value,["--border-width"]);if($){let R=x.width($);return R?[n(),...R]:void 0}if(E(y.value.value)){let R=x.width(`${y.value.value}px`);return R?[n(),...R]:void 0}}}),i(p,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(y,$)=>`${$*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var H=f;let n=()=>D([C("--tw-border-style","solid")]);f("border",{width:p=>[a("border-style","var(--tw-border-style)"),a("border-width",p)],color:p=>[a("border-color",p)]}),f("border-x",{width:p=>[a("border-inline-style","var(--tw-border-style)"),a("border-inline-width",p)],color:p=>[a("border-inline-color",p)]}),f("border-y",{width:p=>[a("border-block-style","var(--tw-border-style)"),a("border-block-width",p)],color:p=>[a("border-block-color",p)]}),f("border-s",{width:p=>[a("border-inline-start-style","var(--tw-border-style)"),a("border-inline-start-width",p)],color:p=>[a("border-inline-start-color",p)]}),f("border-e",{width:p=>[a("border-inline-end-style","var(--tw-border-style)"),a("border-inline-end-width",p)],color:p=>[a("border-inline-end-color",p)]}),f("border-t",{width:p=>[a("border-top-style","var(--tw-border-style)"),a("border-top-width",p)],color:p=>[a("border-top-color",p)]}),f("border-r",{width:p=>[a("border-right-style","var(--tw-border-style)"),a("border-right-width",p)],color:p=>[a("border-right-color",p)]}),f("border-b",{width:p=>[a("border-bottom-style","var(--tw-border-style)"),a("border-bottom-width",p)],color:p=>[a("border-bottom-color",p)]}),f("border-l",{width:p=>[a("border-left-style","var(--tw-border-style)"),a("border-left-width",p)],color:p=>[a("border-left-color",p)]}),o("divide-x",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:p})=>E(p)?`${p}px`:null,handle:p=>[D([C("--tw-divide-x-reverse","0")]),L(":where(& > :not(:last-child))",[a("--tw-sort","divide-x-width"),n(),a("--tw-divide-x-reverse","0"),a("border-inline-style","var(--tw-border-style)"),a("border-inline-start-width",`calc(${p} * var(--tw-divide-x-reverse))`),a("border-inline-end-width",`calc(${p} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),o("divide-y",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:p})=>E(p)?`${p}px`:null,handle:p=>[D([C("--tw-divide-y-reverse","0")]),L(":where(& > :not(:last-child))",[a("--tw-sort","divide-y-width"),n(),a("--tw-divide-y-reverse","0"),a("border-bottom-style","var(--tw-border-style)"),a("border-top-style","var(--tw-border-style)"),a("border-top-width",`calc(${p} * var(--tw-divide-y-reverse))`),a("border-bottom-width",`calc(${p} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>D([C("--tw-divide-x-reverse","0")]),()=>L(":where(& > :not(:last-child))",[a("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>D([C("--tw-divide-y-reverse","0")]),()=>L(":where(& > :not(:last-child))",[a("--tw-divide-y-reverse","1")])]);for(let p of["solid","dashed","dotted","double","none"])e(`divide-${p}`,[()=>L(":where(& > :not(:last-child))",[a("--tw-sort","divide-style"),a("--tw-border-style",p),a("border-style",p)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),o("bg-size",{handle(n){if(n)return[a("background-size",n)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),o("bg-position",{handle(n){if(n)return[a("background-position",n)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let p=function($){let R="in oklab";if($?.kind==="named")switch($.value){case"longer":case"shorter":case"increasing":case"decreasing":R=`in oklch ${$.value} hue`;break;default:R=`in ${$.value}`}else $?.kind==="arbitrary"&&(R=$.value);return R},x=function({negative:$}){return R=>{if(!R.value)return;if(R.value.kind==="arbitrary"){if(R.modifier)return;let j=R.value.value;switch(R.value.dataType??W(j,["angle"])){case"angle":return j=$?`calc(${j} * -1)`:`${j}`,[a("--tw-gradient-position",j),a("background-image",`linear-gradient(var(--tw-gradient-stops,${j}))`)];default:return $?void 0:[a("--tw-gradient-position",j),a("background-image",`linear-gradient(var(--tw-gradient-stops,${j}))`)]}}let N=R.value.value;if(!$&&f.has(N))N=f.get(N);else if(E(N))N=$?`calc(${N}deg * -1)`:`${N}deg`;else return;let T=p(R.modifier);return[a("--tw-gradient-position",`${N}`),B("@supports (background-image: linear-gradient(in lab, red, red))",[a("--tw-gradient-position",`${N} ${T}`)]),a("background-image","linear-gradient(var(--tw-gradient-stops))")]}},y=function({negative:$}){return R=>{if(R.value?.kind==="arbitrary"){if(R.modifier)return;let j=R.value.value;return[a("--tw-gradient-position",j),a("background-image",`conic-gradient(var(--tw-gradient-stops,${j}))`)]}let N=p(R.modifier);if(!R.value)return[a("--tw-gradient-position",N),a("background-image","conic-gradient(var(--tw-gradient-stops))")];let T=R.value.value;if(E(T))return T=$?`calc(${T}deg * -1)`:`${T}deg`,[a("--tw-gradient-position",`from ${T} ${N}`),a("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var M=p,z=x,te=y;let n=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],f=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);t.functional("-bg-linear",x({negative:!0})),t.functional("bg-linear",x({negative:!1})),i("bg-linear",()=>[{values:[...f.keys()],modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),t.functional("-bg-conic",y({negative:!0})),t.functional("bg-conic",y({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),t.functional("bg-radial",$=>{if(!$.value){let R=p($.modifier);return[a("--tw-gradient-position",R),a("background-image","radial-gradient(var(--tw-gradient-stops))")]}if($.value.kind==="arbitrary"){if($.modifier)return;let R=$.value.value;return[a("--tw-gradient-position",R),a("background-image",`radial-gradient(var(--tw-gradient-stops,${R}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:n}])}t.functional("bg",n=>{if(n.value){if(n.value.kind==="arbitrary"){let f=n.value.value;switch(n.value.dataType??W(f,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return n.modifier?void 0:[a("background-position",f)];case"bg-size":case"length":case"size":return n.modifier?void 0:[a("background-size",f)];case"image":case"url":return n.modifier?void 0:[a("background-image",f)];default:return f=Z(f,n.modifier,r),f===null?void 0:[a("background-color",f)]}}{let f=Q(n,r,["--background-color","--color"]);if(f)return[a("background-color",f)]}{if(n.modifier)return;let f=r.resolve(n.value.value,["--background-image"]);if(f)return[a("background-image",f)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>D([C("--tw-gradient-position"),C("--tw-gradient-from","#0000","<color>"),C("--tw-gradient-via","#0000","<color>"),C("--tw-gradient-to","#0000","<color>"),C("--tw-gradient-stops"),C("--tw-gradient-via-stops"),C("--tw-gradient-from-position","0%","<length-percentage>"),C("--tw-gradient-via-position","50%","<length-percentage>"),C("--tw-gradient-to-position","100%","<length-percentage>")]);function A(n,f){t.functional(n,p=>{if(p.value){if(p.value.kind==="arbitrary"){let x=p.value.value;switch(p.value.dataType??W(x,["color","length","percentage"])){case"length":case"percentage":return p.modifier?void 0:f.position(x);default:return x=Z(x,p.modifier,r),x===null?void 0:f.color(x)}}{let x=Q(p,r,["--background-color","--color"]);if(x)return f.color(x)}{if(p.modifier)return;let x=r.resolve(p.value.value,["--gradient-color-stop-positions"]);if(x)return f.position(x);if(p.value.value[p.value.value.length-1]==="%"&&E(p.value.value.slice(0,-1)))return f.position(p.value.value)}}}),i(n,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(p,x)=>`${x*5}`)},{values:Array.from({length:21},(p,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}A("from",{color:n=>[v(),a("--tw-sort","--tw-gradient-from"),a("--tw-gradient-from",n),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:n=>[v(),a("--tw-gradient-from-position",n)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),A("via",{color:n=>[v(),a("--tw-sort","--tw-gradient-via"),a("--tw-gradient-via",n),a("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),a("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:n=>[v(),a("--tw-gradient-via-position",n)]}),A("to",{color:n=>[v(),a("--tw-sort","--tw-gradient-to"),a("--tw-gradient-to",n),a("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:n=>[v(),a("--tw-gradient-to-position",n)]}),e("mask-none",[["mask-image","none"]]),t.functional("mask",n=>{if(!n.value||n.modifier||n.value.kind!=="arbitrary")return;let f=n.value.value;switch(n.value.dataType??W(f,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return n.modifier?void 0:[a("mask-position",f)];case"bg-size":case"length":case"size":return[a("mask-size",f)];case"image":case"url":default:return[a("mask-image",f)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),o("mask-size",{handle(n){if(n)return[a("mask-size",n)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),o("mask-position",{handle(n){if(n)return[a("mask-position",n)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let k=()=>D([C("--tw-mask-linear","linear-gradient(#fff, #fff)"),C("--tw-mask-radial","linear-gradient(#fff, #fff)"),C("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function b(n,f){t.functional(n,p=>{if(p.value){if(p.value.kind==="arbitrary"){let x=p.value.value;switch(p.value.dataType??W(x,["length","percentage","color"])){case"color":return x=Z(x,p.modifier,r),x===null?void 0:f.color(x);case"percentage":return p.modifier||!E(x.slice(0,-1))?void 0:f.position(x);default:return p.modifier?void 0:f.position(x)}}{let x=Q(p,r,["--background-color","--color"]);if(x)return f.color(x)}{if(p.modifier)return;let x=W(p.value.value,["number","percentage"]);if(!x)return;switch(x){case"number":{let y=r.resolve(null,["--spacing"]);return!y||!de(p.value.value)?void 0:f.position(`calc(${y} * ${p.value.value})`)}case"percentage":return E(p.value.value.slice(0,-1))?f.position(p.value.value):void 0;default:return}}}}),i(n,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(p,x)=>`${x*5}`)},{values:Array.from({length:21},(p,x)=>`${x*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(n,()=>[{values:Array.from({length:21},(p,x)=>`${x*5}%`)},{values:r.get(["--spacing"])?Ge:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(p,x)=>`${x*5}`)}])}let V=()=>D([C("--tw-mask-left","linear-gradient(#fff, #fff)"),C("--tw-mask-right","linear-gradient(#fff, #fff)"),C("--tw-mask-bottom","linear-gradient(#fff, #fff)"),C("--tw-mask-top","linear-gradient(#fff, #fff)")]);function S(n,f,p){b(n,{color(x){let y=[k(),V(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let $ of["top","right","bottom","left"])p[$]&&(y.push(a(`--tw-mask-${$}`,`linear-gradient(to ${$}, var(--tw-mask-${$}-from-color) var(--tw-mask-${$}-from-position), var(--tw-mask-${$}-to-color) var(--tw-mask-${$}-to-position))`)),y.push(D([C(`--tw-mask-${$}-from-position`,"0%"),C(`--tw-mask-${$}-to-position`,"100%"),C(`--tw-mask-${$}-from-color`,"black"),C(`--tw-mask-${$}-to-color`,"transparent")])),y.push(a(`--tw-mask-${$}-${f}-color`,x)));return y},position(x){let y=[k(),V(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let $ of["top","right","bottom","left"])p[$]&&(y.push(a(`--tw-mask-${$}`,`linear-gradient(to ${$}, var(--tw-mask-${$}-from-color) var(--tw-mask-${$}-from-position), var(--tw-mask-${$}-to-color) var(--tw-mask-${$}-to-position))`)),y.push(D([C(`--tw-mask-${$}-from-position`,"0%"),C(`--tw-mask-${$}-to-position`,"100%"),C(`--tw-mask-${$}-from-color`,"black"),C(`--tw-mask-${$}-to-color`,"transparent")])),y.push(a(`--tw-mask-${$}-${f}-position`,x)));return y}})}S("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),S("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let K=()=>D([C("--tw-mask-linear-position","0deg"),C("--tw-mask-linear-from-position","0%"),C("--tw-mask-linear-to-position","100%"),C("--tw-mask-linear-from-color","black"),C("--tw-mask-linear-to-color","transparent")]);o("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(n){return E(n.value)?`calc(1deg * ${n.value})`:null},handleNegativeBareValue(n){return E(n.value)?`calc(1deg * -${n.value})`:null},handle:n=>[k(),K(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),a("--tw-mask-linear-position",n)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),b("mask-linear-from",{color:n=>[k(),K(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),a("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),a("--tw-mask-linear-from-color",n)],position:n=>[k(),K(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),a("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),a("--tw-mask-linear-from-position",n)]}),b("mask-linear-to",{color:n=>[k(),K(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),a("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),a("--tw-mask-linear-to-color",n)],position:n=>[k(),K(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),a("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),a("--tw-mask-linear-to-position",n)]});let O=()=>D([C("--tw-mask-radial-from-position","0%"),C("--tw-mask-radial-to-position","100%"),C("--tw-mask-radial-from-color","black"),C("--tw-mask-radial-to-color","transparent"),C("--tw-mask-radial-shape","ellipse"),C("--tw-mask-radial-size","farthest-corner"),C("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),o("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:n=>[a("--tw-mask-radial-position",n)]}),o("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:n=>[k(),O(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),a("--tw-mask-radial-size",n)]}),b("mask-radial-from",{color:n=>[k(),O(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),a("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),a("--tw-mask-radial-from-color",n)],position:n=>[k(),O(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),a("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),a("--tw-mask-radial-from-position",n)]}),b("mask-radial-to",{color:n=>[k(),O(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),a("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),a("--tw-mask-radial-to-color",n)],position:n=>[k(),O(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),a("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),a("--tw-mask-radial-to-position",n)]});let U=()=>D([C("--tw-mask-conic-position","0deg"),C("--tw-mask-conic-from-position","0%"),C("--tw-mask-conic-to-position","100%"),C("--tw-mask-conic-from-color","black"),C("--tw-mask-conic-to-color","transparent")]);o("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(n){return E(n.value)?`calc(1deg * ${n.value})`:null},handleNegativeBareValue(n){return E(n.value)?`calc(1deg * -${n.value})`:null},handle:n=>[k(),U(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),a("--tw-mask-conic-position",n)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),b("mask-conic-from",{color:n=>[k(),U(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),a("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),a("--tw-mask-conic-from-color",n)],position:n=>[k(),U(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),a("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),a("--tw-mask-conic-from-position",n)]}),b("mask-conic-to",{color:n=>[k(),U(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),a("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),a("--tw-mask-conic-to-color",n)],position:n=>[k(),U(),a("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),a("mask-composite","intersect"),a("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),a("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),a("--tw-mask-conic-to-position",n)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let n of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${n}`,[["background-blend-mode",n]]),e(`mix-blend-${n}`,[["mix-blend-mode",n]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),t.functional("fill",n=>{if(!n.value)return;if(n.value.kind==="arbitrary"){let p=Z(n.value.value,n.modifier,r);return p===null?void 0:[a("fill",p)]}let f=Q(n,r,["--fill","--color"]);if(f)return[a("fill",f)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)}]),e("stroke-none",[["stroke","none"]]),t.functional("stroke",n=>{if(n.value){if(n.value.kind==="arbitrary"){let f=n.value.value;switch(n.value.dataType??W(f,["color","number","length","percentage"])){case"number":case"length":case"percentage":return n.modifier?void 0:[a("stroke-width",f)];default:return f=Z(n.value.value,n.modifier,r),f===null?void 0:[a("stroke",f)]}}{let f=Q(n,r,["--stroke","--color"]);if(f)return[a("stroke",f)]}{let f=r.resolve(n.value.value,["--stroke-width"]);if(f)return[a("stroke-width",f)];if(E(n.value.value))return[a("stroke-width",n.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-top",[["object-position","top"]]),e("object-top-left",[["object-position","left top"]]),e("object-top-right",[["object-position","right top"]]),e("object-bottom",[["object-position","bottom"]]),e("object-bottom-left",[["object-position","left bottom"]]),e("object-bottom-right",[["object-position","right bottom"]]),e("object-left",[["object-position","left"]]),e("object-right",[["object-position","right"]]),e("object-center",[["object-position","center"]]),o("object",{themeKeys:["--object-position"],handle:n=>[a("object-position",n)]});for(let[n,f]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])l(n,["--padding","--spacing"],p=>[a(f,p)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),l("indent",["--text-indent","--spacing"],n=>[a("text-indent",n)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),o("align",{themeKeys:[],handle:n=>[a("vertical-align",n)]}),t.functional("font",n=>{if(!(!n.value||n.modifier)){if(n.value.kind==="arbitrary"){let f=n.value.value;switch(n.value.dataType??W(f,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[a("font-family",f)];default:return[D([C("--tw-font-weight")]),a("--tw-font-weight",f),a("font-weight",f)]}}{let f=r.resolveWith(n.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(f){let[p,x={}]=f;return[a("font-family",p),a("font-feature-settings",x["--font-feature-settings"]),a("font-variation-settings",x["--font-variation-settings"])]}}{let f=r.resolve(n.value.value,["--font-weight"]);if(f)return[D([C("--tw-font-weight")]),a("--tw-font-weight",f),a("font-weight",f)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),o("font-stretch",{handleBareValue:({value:n})=>{if(!n.endsWith("%"))return null;let f=Number(n.slice(0,-1));return!E(f)||Number.isNaN(f)||f<50||f>200?null:n},handle:n=>[a("font-stretch",n)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:n=>[L("&::placeholder",[a("--tw-sort","placeholder-color"),a("color",n)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),t.functional("decoration",n=>{if(n.value){if(n.value.kind==="arbitrary"){let f=n.value.value;switch(n.value.dataType??W(f,["color","length","percentage"])){case"length":case"percentage":return n.modifier?void 0:[a("text-decoration-thickness",f)];default:return f=Z(f,n.modifier,r),f===null?void 0:[a("text-decoration-color",f)]}}{let f=r.resolve(n.value.value,["--text-decoration-thickness"]);if(f)return n.modifier?void 0:[a("text-decoration-thickness",f)];if(E(n.value.value))return n.modifier?void 0:[a("text-decoration-thickness",`${n.value.value}px`)]}{let f=Q(n,r,["--text-decoration-color","--color"]);if(f)return[a("text-decoration-color",f)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),o("animate",{themeKeys:["--animate"],handle:n=>[a("animation",n)]});{let n=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),f=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),p=()=>D([C("--tw-blur"),C("--tw-brightness"),C("--tw-contrast"),C("--tw-grayscale"),C("--tw-hue-rotate"),C("--tw-invert"),C("--tw-opacity"),C("--tw-saturate"),C("--tw-sepia"),C("--tw-drop-shadow"),C("--tw-drop-shadow-color"),C("--tw-drop-shadow-alpha","100%","<percentage>"),C("--tw-drop-shadow-size")]),x=()=>D([C("--tw-backdrop-blur"),C("--tw-backdrop-brightness"),C("--tw-backdrop-contrast"),C("--tw-backdrop-grayscale"),C("--tw-backdrop-hue-rotate"),C("--tw-backdrop-invert"),C("--tw-backdrop-opacity"),C("--tw-backdrop-saturate"),C("--tw-backdrop-sepia")]);t.functional("filter",y=>{if(!y.modifier){if(y.value===null)return[p(),a("filter",n)];if(y.value.kind==="arbitrary")return[a("filter",y.value.value)];switch(y.value.value){case"none":return[a("filter","none")]}}}),t.functional("backdrop-filter",y=>{if(!y.modifier){if(y.value===null)return[x(),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)];if(y.value.kind==="arbitrary")return[a("-webkit-backdrop-filter",y.value.value),a("backdrop-filter",y.value.value)];switch(y.value.value){case"none":return[a("-webkit-backdrop-filter","none"),a("backdrop-filter","none")]}}}),o("blur",{themeKeys:["--blur"],handle:y=>[p(),a("--tw-blur",`blur(${y})`),a("filter",n)]}),e("blur-none",[p,["--tw-blur"," "],["filter",n]]),o("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:y=>[x(),a("--tw-backdrop-blur",`blur(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),e("backdrop-blur-none",[x,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",f],["backdrop-filter",f]]),o("brightness",{themeKeys:["--brightness"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[p(),a("--tw-brightness",`brightness(${y})`),a("filter",n)]}),o("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[x(),a("--tw-backdrop-brightness",`brightness(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),o("contrast",{themeKeys:["--contrast"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[p(),a("--tw-contrast",`contrast(${y})`),a("filter",n)]}),o("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[x(),a("--tw-backdrop-contrast",`contrast(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),o("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[p(),a("--tw-grayscale",`grayscale(${y})`),a("filter",n)]}),o("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[x(),a("--tw-backdrop-grayscale",`grayscale(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),o("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:y})=>E(y)?`${y}deg`:null,handle:y=>[p(),a("--tw-hue-rotate",`hue-rotate(${y})`),a("filter",n)]}),o("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:y})=>E(y)?`${y}deg`:null,handle:y=>[x(),a("--tw-backdrop-hue-rotate",`hue-rotate(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),o("invert",{themeKeys:["--invert"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[p(),a("--tw-invert",`invert(${y})`),a("filter",n)]}),o("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[x(),a("--tw-backdrop-invert",`invert(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),o("saturate",{themeKeys:["--saturate"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[p(),a("--tw-saturate",`saturate(${y})`),a("filter",n)]}),o("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,handle:y=>[x(),a("--tw-backdrop-saturate",`saturate(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),o("sepia",{themeKeys:["--sepia"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[p(),a("--tw-sepia",`sepia(${y})`),a("filter",n)]}),o("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:y})=>E(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[x(),a("--tw-backdrop-sepia",`sepia(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[p,["--tw-drop-shadow"," "],["filter",n]]),t.functional("drop-shadow",y=>{let $;if(y.modifier&&(y.modifier.kind==="arbitrary"?$=y.modifier.value:E(y.modifier.value)&&($=`${y.modifier.value}%`)),!y.value){let R=r.get(["--drop-shadow"]),N=r.resolve(null,["--drop-shadow"]);return R===null||N===null?void 0:[p(),a("--tw-drop-shadow-alpha",$),...qe("--tw-drop-shadow-size",R,$,T=>`var(--tw-drop-shadow-color, ${T})`),a("--tw-drop-shadow",_(N,",").map(T=>`drop-shadow(${T})`).join(" ")),a("filter",n)]}if(y.value.kind==="arbitrary"){let R=y.value.value;switch(y.value.dataType??W(R,["color"])){case"color":return R=Z(R,y.modifier,r),R===null?void 0:[p(),a("--tw-drop-shadow-color",J(R,"var(--tw-drop-shadow-alpha)")),a("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return y.modifier&&!$?void 0:[p(),a("--tw-drop-shadow-alpha",$),...qe("--tw-drop-shadow-size",R,$,T=>`var(--tw-drop-shadow-color, ${T})`),a("--tw-drop-shadow","var(--tw-drop-shadow-size)"),a("filter",n)]}}{let R=r.get([`--drop-shadow-${y.value.value}`]),N=r.resolve(y.value.value,["--drop-shadow"]);if(R&&N)return y.modifier&&!$?void 0:$?[p(),a("--tw-drop-shadow-alpha",$),...qe("--tw-drop-shadow-size",R,$,T=>`var(--tw-drop-shadow-color, ${T})`),a("--tw-drop-shadow","var(--tw-drop-shadow-size)"),a("filter",n)]:[p(),a("--tw-drop-shadow-alpha",$),...qe("--tw-drop-shadow-size",R,$,T=>`var(--tw-drop-shadow-color, ${T})`),a("--tw-drop-shadow",_(N,",").map(T=>`drop-shadow(${T})`).join(" ")),a("filter",n)]}{let R=Q(y,r,["--drop-shadow-color","--color"]);if(R)return R==="inherit"?[p(),a("--tw-drop-shadow-color","inherit"),a("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[p(),a("--tw-drop-shadow-color",J(R,"var(--tw-drop-shadow-alpha)")),a("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(y,$)=>`${$*5}`)},{valueThemeKeys:["--drop-shadow"]}]),o("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:y})=>_e(y)?`${y}%`:null,handle:y=>[x(),a("--tw-backdrop-opacity",`opacity(${y})`),a("-webkit-backdrop-filter",f),a("backdrop-filter",f)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(y,$)=>`${$*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let n=`var(--tw-ease, ${r.resolve(null,["--default-transition-timing-function"])??"ease"})`,f=`var(--tw-duration, ${r.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",n],["transition-duration",f]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",n],["transition-duration",f]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",n],["transition-duration",f]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",n],["transition-duration",f]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",n],["transition-duration",f]]),o("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:p=>[a("transition-property",p),a("transition-timing-function",n),a("transition-duration",f)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),o("delay",{handleBareValue:({value:p})=>E(p)?`${p}ms`:null,themeKeys:["--transition-delay"],handle:p=>[a("transition-delay",p)]});{let p=()=>D([C("--tw-duration")]);e("duration-initial",[p,["--tw-duration","initial"]]),t.functional("duration",x=>{if(x.modifier||!x.value)return;let y=null;if(x.value.kind==="arbitrary"?y=x.value.value:(y=r.resolve(x.value.fraction??x.value.value,["--transition-duration"]),y===null&&E(x.value.value)&&(y=`${x.value.value}ms`)),y!==null)return[p(),a("--tw-duration",y),a("transition-duration",y)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let n=()=>D([C("--tw-ease")]);e("ease-initial",[n,["--tw-ease","initial"]]),e("ease-linear",[n,["--tw-ease","linear"],["transition-timing-function","linear"]]),o("ease",{themeKeys:["--ease"],handle:f=>[n(),a("--tw-ease",f),a("transition-timing-function",f)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),o("will-change",{themeKeys:[],handle:n=>[a("will-change",n)]}),e("content-none",[["--tw-content","none"],["content","none"]]),o("content",{themeKeys:[],handle:n=>[D([C("--tw-content",'""')]),a("--tw-content",n),a("content","var(--tw-content)")]});{let n="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",f=()=>D([C("--tw-contain-size"),C("--tw-contain-layout"),C("--tw-contain-paint"),C("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[f,["--tw-contain-size","size"],["contain",n]]),e("contain-inline-size",[f,["--tw-contain-size","inline-size"],["contain",n]]),e("contain-layout",[f,["--tw-contain-layout","layout"],["contain",n]]),e("contain-paint",[f,["--tw-contain-paint","paint"],["contain",n]]),e("contain-style",[f,["--tw-contain-style","style"],["contain",n]]),o("contain",{themeKeys:[],handle:p=>[a("contain",p)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>D([C("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),l("leading",["--leading","--spacing"],n=>[D([C("--tw-leading")]),a("--tw-leading",n),a("line-height",n)]),o("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:n=>[D([C("--tw-tracking")]),a("--tw-tracking",n),a("letter-spacing",n)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let n="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",f=()=>D([C("--tw-ordinal"),C("--tw-slashed-zero"),C("--tw-numeric-figure"),C("--tw-numeric-spacing"),C("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[f,["--tw-ordinal","ordinal"],["font-variant-numeric",n]]),e("slashed-zero",[f,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",n]]),e("lining-nums",[f,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",n]]),e("oldstyle-nums",[f,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",n]]),e("proportional-nums",[f,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",n]]),e("tabular-nums",[f,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",n]]),e("diagonal-fractions",[f,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",n]]),e("stacked-fractions",[f,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",n]])}{let n=()=>D([C("--tw-outline-style","solid")]);t.static("outline-hidden",()=>[a("--tw-outline-style","none"),a("outline-style","none"),I("@media","(forced-colors: active)",[a("outline","2px solid transparent"),a("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),t.functional("outline",f=>{if(f.value===null){if(f.modifier)return;let p=r.get(["--default-outline-width"])??"1px";return[n(),a("outline-style","var(--tw-outline-style)"),a("outline-width",p)]}if(f.value.kind==="arbitrary"){let p=f.value.value;switch(f.value.dataType??W(p,["color","length","number","percentage"])){case"length":case"number":case"percentage":return f.modifier?void 0:[n(),a("outline-style","var(--tw-outline-style)"),a("outline-width",p)];default:return p=Z(p,f.modifier,r),p===null?void 0:[a("outline-color",p)]}}{let p=Q(f,r,["--outline-color","--color"]);if(p)return[a("outline-color",p)]}{if(f.modifier)return;let p=r.resolve(f.value.value,["--outline-width"]);if(p)return[n(),a("outline-style","var(--tw-outline-style)"),a("outline-width",p)];if(E(f.value.value))return[n(),a("outline-style","var(--tw-outline-style)"),a("outline-width",`${f.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(f,p)=>`${p*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),o("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:f})=>E(f)?`${f}px`:null,handle:f=>[a("outline-offset",f)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}o("opacity",{themeKeys:["--opacity"],handleBareValue:({value:n})=>_e(n)?`${n}%`:null,handle:n=>[a("opacity",n)]}),i("opacity",()=>[{values:Array.from({length:21},(n,f)=>`${f*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),o("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:n})=>E(n)?`${n}px`:null,handle:n=>[a("text-underline-offset",n)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),t.functional("text",n=>{if(n.value){if(n.value.kind==="arbitrary"){let f=n.value.value;switch(n.value.dataType??W(f,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(n.modifier){let x=n.modifier.kind==="arbitrary"?n.modifier.value:r.resolve(n.modifier.value,["--leading"]);if(!x&&de(n.modifier.value)){let y=r.resolve(null,["--spacing"]);if(!y)return null;x=`calc(${y} * ${n.modifier.value})`}return!x&&n.modifier.value==="none"&&(x="1"),x?[a("font-size",f),a("line-height",x)]:null}return[a("font-size",f)]}default:return f=Z(f,n.modifier,r),f===null?void 0:[a("color",f)]}}{let f=Q(n,r,["--text-color","--color"]);if(f)return[a("color",f)]}{let f=r.resolveWith(n.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(f){let[p,x={}]=Array.isArray(f)?f:[f];if(n.modifier){let y=n.modifier.kind==="arbitrary"?n.modifier.value:r.resolve(n.modifier.value,["--leading"]);if(!y&&de(n.modifier.value)){let R=r.resolve(null,["--spacing"]);if(!R)return null;y=`calc(${R} * ${n.modifier.value})`}if(!y&&n.modifier.value==="none"&&(y="1"),!y)return null;let $=[a("font-size",p)];return y&&$.push(a("line-height",y)),$}return typeof x=="string"?[a("font-size",p),a("line-height",x)]:[a("font-size",p),a("line-height",x["--line-height"]?`var(--tw-leading, ${x["--line-height"]})`:void 0),a("letter-spacing",x["--letter-spacing"]?`var(--tw-tracking, ${x["--letter-spacing"]})`:void 0),a("font-weight",x["--font-weight"]?`var(--tw-font-weight, ${x["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let P=()=>D([C("--tw-text-shadow-color"),C("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[P,["--tw-text-shadow-color","initial"]]),t.functional("text-shadow",n=>{let f;if(n.modifier&&(n.modifier.kind==="arbitrary"?f=n.modifier.value:E(n.modifier.value)&&(f=`${n.modifier.value}%`)),!n.value){let p=r.get(["--text-shadow"]);return p===null?void 0:[P(),a("--tw-text-shadow-alpha",f),...le("text-shadow",p,f,x=>`var(--tw-text-shadow-color, ${x})`)]}if(n.value.kind==="arbitrary"){let p=n.value.value;switch(n.value.dataType??W(p,["color"])){case"color":return p=Z(p,n.modifier,r),p===null?void 0:[P(),a("--tw-text-shadow-color",J(p,"var(--tw-text-shadow-alpha)"))];default:return[P(),a("--tw-text-shadow-alpha",f),...le("text-shadow",p,f,y=>`var(--tw-text-shadow-color, ${y})`)]}}switch(n.value.value){case"none":return n.modifier?void 0:[P(),a("text-shadow","none")];case"inherit":return n.modifier?void 0:[P(),a("--tw-text-shadow-color","inherit")]}{let p=r.get([`--text-shadow-${n.value.value}`]);if(p)return[P(),a("--tw-text-shadow-alpha",f),...le("text-shadow",p,f,x=>`var(--tw-text-shadow-color, ${x})`)]}{let p=Q(n,r,["--text-shadow-color","--color"]);if(p)return[P(),a("--tw-text-shadow-color",J(p,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`),hasDefaultValue:!0}]);{let y=function(N){return`var(--tw-ring-inset,) 0 0 0 calc(${N} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${x})`},$=function(N){return`inset 0 0 0 ${N} var(--tw-inset-ring-color, currentcolor)`};var X=y,ae=$;let n=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),f="0 0 #0000",p=()=>D([C("--tw-shadow",f),C("--tw-shadow-color"),C("--tw-shadow-alpha","100%","<percentage>"),C("--tw-inset-shadow",f),C("--tw-inset-shadow-color"),C("--tw-inset-shadow-alpha","100%","<percentage>"),C("--tw-ring-color"),C("--tw-ring-shadow",f),C("--tw-inset-ring-color"),C("--tw-inset-ring-shadow",f),C("--tw-ring-inset"),C("--tw-ring-offset-width","0px","<length>"),C("--tw-ring-offset-color","#fff"),C("--tw-ring-offset-shadow",f)]);e("shadow-initial",[p,["--tw-shadow-color","initial"]]),t.functional("shadow",N=>{let T;if(N.modifier&&(N.modifier.kind==="arbitrary"?T=N.modifier.value:E(N.modifier.value)&&(T=`${N.modifier.value}%`)),!N.value){let j=r.get(["--shadow"]);return j===null?void 0:[p(),a("--tw-shadow-alpha",T),...le("--tw-shadow",j,T,ie=>`var(--tw-shadow-color, ${ie})`),a("box-shadow",n)]}if(N.value.kind==="arbitrary"){let j=N.value.value;switch(N.value.dataType??W(j,["color"])){case"color":return j=Z(j,N.modifier,r),j===null?void 0:[p(),a("--tw-shadow-color",J(j,"var(--tw-shadow-alpha)"))];default:return[p(),a("--tw-shadow-alpha",T),...le("--tw-shadow",j,T,tt=>`var(--tw-shadow-color, ${tt})`),a("box-shadow",n)]}}switch(N.value.value){case"none":return N.modifier?void 0:[p(),a("--tw-shadow",f),a("box-shadow",n)];case"inherit":return N.modifier?void 0:[p(),a("--tw-shadow-color","inherit")]}{let j=r.get([`--shadow-${N.value.value}`]);if(j)return[p(),a("--tw-shadow-alpha",T),...le("--tw-shadow",j,T,ie=>`var(--tw-shadow-color, ${ie})`),a("box-shadow",n)]}{let j=Q(N,r,["--box-shadow-color","--color"]);if(j)return[p(),a("--tw-shadow-color",J(j,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`),hasDefaultValue:!0}]),e("inset-shadow-initial",[p,["--tw-inset-shadow-color","initial"]]),t.functional("inset-shadow",N=>{let T;if(N.modifier&&(N.modifier.kind==="arbitrary"?T=N.modifier.value:E(N.modifier.value)&&(T=`${N.modifier.value}%`)),!N.value){let j=r.get(["--inset-shadow"]);return j===null?void 0:[p(),a("--tw-inset-shadow-alpha",T),...le("--tw-inset-shadow",j,T,ie=>`var(--tw-inset-shadow-color, ${ie})`),a("box-shadow",n)]}if(N.value.kind==="arbitrary"){let j=N.value.value;switch(N.value.dataType??W(j,["color"])){case"color":return j=Z(j,N.modifier,r),j===null?void 0:[p(),a("--tw-inset-shadow-color",J(j,"var(--tw-inset-shadow-alpha)"))];default:return[p(),a("--tw-inset-shadow-alpha",T),...le("--tw-inset-shadow",j,T,tt=>`var(--tw-inset-shadow-color, ${tt})`,"inset "),a("box-shadow",n)]}}switch(N.value.value){case"none":return N.modifier?void 0:[p(),a("--tw-inset-shadow",f),a("box-shadow",n)];case"inherit":return N.modifier?void 0:[p(),a("--tw-inset-shadow-color","inherit")]}{let j=r.get([`--inset-shadow-${N.value.value}`]);if(j)return[p(),a("--tw-inset-shadow-alpha",T),...le("--tw-inset-shadow",j,T,ie=>`var(--tw-inset-shadow-color, ${ie})`),a("box-shadow",n)]}{let j=Q(N,r,["--box-shadow-color","--color"]);if(j)return[p(),a("--tw-inset-shadow-color",J(j,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`),hasDefaultValue:!0}]),e("ring-inset",[p,["--tw-ring-inset","inset"]]);let x=r.get(["--default-ring-color"])??"currentcolor";t.functional("ring",N=>{if(!N.value){if(N.modifier)return;let T=r.get(["--default-ring-width"])??"1px";return[p(),a("--tw-ring-shadow",y(T)),a("box-shadow",n)]}if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??W(T,["color","length"])){case"length":return N.modifier?void 0:[p(),a("--tw-ring-shadow",y(T)),a("box-shadow",n)];default:return T=Z(T,N.modifier,r),T===null?void 0:[a("--tw-ring-color",T)]}}{let T=Q(N,r,["--ring-color","--color"]);if(T)return[a("--tw-ring-color",T)]}{if(N.modifier)return;let T=r.resolve(N.value.value,["--ring-width"]);if(T===null&&E(N.value.value)&&(T=`${N.value.value}px`),T)return[p(),a("--tw-ring-shadow",y(T)),a("box-shadow",n)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),t.functional("inset-ring",N=>{if(!N.value)return N.modifier?void 0:[p(),a("--tw-inset-ring-shadow",$("1px")),a("box-shadow",n)];if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??W(T,["color","length"])){case"length":return N.modifier?void 0:[p(),a("--tw-inset-ring-shadow",$(T)),a("box-shadow",n)];default:return T=Z(T,N.modifier,r),T===null?void 0:[a("--tw-inset-ring-color",T)]}}{let T=Q(N,r,["--ring-color","--color"]);if(T)return[a("--tw-inset-ring-color",T)]}{if(N.modifier)return;let T=r.resolve(N.value.value,["--ring-width"]);if(T===null&&E(N.value.value)&&(T=`${N.value.value}px`),T)return[p(),a("--tw-inset-ring-shadow",$(T)),a("box-shadow",n)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let R="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";t.functional("ring-offset",N=>{if(N.value){if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??W(T,["color","length"])){case"length":return N.modifier?void 0:[a("--tw-ring-offset-width",T),a("--tw-ring-offset-shadow",R)];default:return T=Z(T,N.modifier,r),T===null?void 0:[a("--tw-ring-offset-color",T)]}}{let T=r.resolve(N.value.value,["--ring-offset-width"]);if(T)return N.modifier?void 0:[a("--tw-ring-offset-width",T),a("--tw-ring-offset-shadow",R)];if(E(N.value.value))return N.modifier?void 0:[a("--tw-ring-offset-width",`${N.value.value}px`),a("--tw-ring-offset-shadow",R)]}{let T=Q(N,r,["--ring-offset-color","--color"]);if(T)return[a("--tw-ring-offset-color",T)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(n,f)=>`${f*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),t.functional("@container",n=>{let f=null;if(n.value===null?f="inline-size":n.value.kind==="arbitrary"?f=n.value.value:n.value.kind==="named"&&n.value.value==="normal"&&(f="normal"),f!==null)return n.modifier?[a("container-type",f),a("container-name",n.modifier.value)]:[a("container-type",f)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),t}var mt=["number","integer","ratio","percentage"];function er(r){let t=r.params;return si.test(t)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};F(r.nodes,o=>{if(o.kind!=="declaration"||!o.value||!o.value.includes("--value(")&&!o.value.includes("--modifier("))return;let s=G(o.value);ee(s,l=>{if(l.kind!=="function")return;if(l.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return ee(l.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let c=u.value;for(let m of u.nodes)if(m.kind==="word"){if(m.value==="integer")e[c].usedSpacingInteger||=!0;else if(m.value==="number"&&(e[c].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(l.value!=="--value"&&l.value!=="--modifier")return;let d=_(Y(l.nodes),",");for(let[u,c]of d.entries())c=c.replace(/\\\*/g,"*"),c=c.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),c=c.replace(/\s+/g,""),c=c.replace(/(-\*){2,}/g,"-*"),c[0]==="-"&&c[1]==="-"&&!c.includes("-*")&&(c+="-*"),d[u]=c;l.nodes=G(d.join(","));for(let u of l.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let c=u.value.slice(1,-1);e[l.value].literals.add(c)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let c=u.value.replace(/-\*.*$/g,"");e[l.value].themeKeys.add(c)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!mt.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${mt.map(A=>`"${A}"`).join(", ")}.
`);let c=u.value,m=structuredClone(l),g="\xB6";ee(m.nodes,(A,{replaceWith:k})=>{A.kind==="word"&&A.value===c&&k({kind:"word",value:g})});let h="^".repeat(Y([u]).length),w=Y([m]).indexOf(g),v=["```css",Y([l])," ".repeat(w)+h,"```"].join(`
`);console.warn(v)}}),o.value=Y(s)}),i.utilities.functional(t.slice(0,-2),o=>{let s=structuredClone(r),l=o.value,d=o.modifier;if(l===null)return;let u=!1,c=!1,m=!1,g=!1,h=new Map,w=!1;if(F([s],(v,{parent:A,replaceWith:k})=>{if(A?.kind!=="rule"&&A?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let b=G(v.value);(ee(b,(S,{replaceWith:K})=>{if(S.kind==="function"){if(S.value==="--value"){u=!0;let O=Zt(l,S,i);return O?(c=!0,O.ratio?w=!0:h.set(v,A),K(O.nodes),1):(u||=!1,k([]),2)}else if(S.value==="--modifier"){if(d===null)return k([]),2;m=!0;let O=Zt(d,S,i);return O?(g=!0,K(O.nodes),1):(m||=!1,k([]),2)}}})??0)===0&&(v.value=Y(b))}),u&&!c||m&&!g||w&&g||d&&!w&&!g)return null;if(w)for(let[v,A]of h){let k=A.nodes.indexOf(v);k!==-1&&A.nodes.splice(k,1)}return s.nodes}),i.utilities.suggest(t.slice(0,-2),()=>{let o=[],s=[];for(let[l,{literals:d,usedSpacingNumber:u,usedSpacingInteger:c,themeKeys:m}]of[[o,e["--value"]],[s,e["--modifier"]]]){for(let g of d)l.push(g);if(u)l.push(...Ge);else if(c)for(let g of Ge)E(g)&&l.push(g);for(let g of i.theme.keysInNamespaces(m))l.push(g)}return[{values:o,modifiers:s}]})}:li.test(t)?i=>{i.utilities.static(t,()=>structuredClone(r.nodes))}:null}function Zt(r,t,i){for(let e of t.nodes){if(r.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===r.value)return{nodes:G(r.value)};if(r.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let o=e.value;if(o.endsWith("-*")){o=o.slice(0,-2);let s=i.theme.resolve(r.value,[o]);if(s)return{nodes:G(s)}}else{let s=o.split("-*");if(s.length<=1)continue;let l=[s.shift()],d=i.theme.resolveWith(r.value,l,s);if(d){let[,u={}]=d;{let c=u[s.pop()];if(c)return{nodes:G(c)}}}}}else if(r.kind==="named"&&e.kind==="word"){if(!mt.includes(e.value))continue;let o=e.value==="ratio"&&"fraction"in r?r.fraction:r.value;if(!o)continue;let s=W(o,[e.value]);if(s===null)continue;if(s==="ratio"){let[l,d]=_(o,"/");if(!E(l)||!E(d))continue}else{if(s==="number"&&!de(o))continue;if(s==="percentage"&&!E(o.slice(0,-1)))continue}return{nodes:G(o),ratio:s==="ratio"}}else if(r.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let o=e.value.slice(1,-1);if(o==="*")return{nodes:G(r.value)};if("dataType"in r&&r.dataType&&r.dataType!==o)continue;if("dataType"in r&&r.dataType)return{nodes:G(r.value)};if(W(r.value,[o])!==null)return{nodes:G(r.value)}}}}function le(r,t,i,e,o=""){let s=!1,l=Te(t,u=>i==null?e(u):u.startsWith("current")?e(J(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(Qt(u,i))));function d(u){return o?_(u,",").map(c=>o+c).join(","):u}return s?[a(r,d(Te(t,e))),B("@supports (color: lab(from red l a b))",[a(r,d(l))])]:[a(r,d(l))]}function qe(r,t,i,e,o=""){let s=!1,l=_(t,",").map(d=>Te(d,u=>i==null?e(u):u.startsWith("current")?e(J(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(Qt(u,i))))).map(d=>`drop-shadow(${d})`).join(" ");return s?[a(r,o+_(t,",").map(d=>`drop-shadow(${Te(d,e)})`).join(" ")),B("@supports (color: lab(from red l a b))",[a(r,o+l)])]:[a(r,o+l)]}var gt={"--alpha":ui,"--spacing":fi,"--theme":ci,theme:di};function ui(r,t,i,...e){let[o,s]=_(i,"/").map(l=>l.trim());if(!o||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${o||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${o||"var(--my-color)"} / ${s||"50%"})\``);return J(o,s)}function fi(r,t,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let o=r.theme.resolve(null,["--spacing"]);if(!o)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${o} * ${i})`}function ci(r,t,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let o=!1;i.endsWith(" inline")&&(o=!0,i=i.slice(0,-7)),t.kind==="at-rule"&&(o=!0);let s=r.resolveThemeValue(i,o);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let l=e.join(", ");if(l==="initial")return s;if(s==="initial")return l;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let d=G(s);return mi(d,l),Y(d)}return s}function di(r,t,i,...e){i=pi(i);let o=r.resolveThemeValue(i);if(!o&&e.length>0)return e.join(", ");if(!o)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return o}var tr=new RegExp(Object.keys(gt).map(r=>`${r}\\(`).join("|"));function be(r,t){let i=0;return F(r,e=>{if(e.kind==="declaration"&&e.value&&tr.test(e.value)){i|=8,e.value=rr(e.value,e,t);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&tr.test(e.params)&&(i|=8,e.params=rr(e.params,e,t))}),i}function rr(r,t,i){let e=G(r);return ee(e,(o,{replaceWith:s})=>{if(o.kind==="function"&&o.value in gt){let l=_(Y(o.nodes).trim(),",").map(u=>u.trim()),d=gt[o.value](i,t,...l);return s(G(d))}}),Y(e)}function pi(r){if(r[0]!=="'"&&r[0]!=='"')return r;let t="",i=r[0];for(let e=1;e<r.length-1;e++){let o=r[e],s=r[e+1];o==="\\"&&(s===i||s==="\\")?(t+=s,e++):t+=o}return t}function mi(r,t){ee(r,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${t}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=t)}})}function Je(r,t){let i=r.length,e=t.length,o=i<e?i:e;for(let s=0;s<o;s++){let l=r.charCodeAt(s),d=t.charCodeAt(s);if(l>=48&&l<=57&&d>=48&&d<=57){let u=s,c=s+1,m=s,g=s+1;for(l=r.charCodeAt(c);l>=48&&l<=57;)l=r.charCodeAt(++c);for(d=t.charCodeAt(g);d>=48&&d<=57;)d=t.charCodeAt(++g);let h=r.slice(u,c),w=t.slice(m,g),v=Number(h)-Number(w);if(v)return v;if(h<w)return-1;if(h>w)return 1;continue}if(l!==d)return l-d}return r.length-t.length}var gi=/^\d+\/\d+$/;function ir(r){let t=[];for(let e of r.utilities.keys("static"))t.push({name:e,utility:e,fraction:!1,modifiers:[]});for(let e of r.utilities.keys("functional")){let o=r.utilities.getCompletions(e);for(let s of o)for(let l of s.values){let d=l!==null&&gi.test(l),u=l===null?e:`${e}-${l}`;t.push({name:u,utility:e,fraction:d,modifiers:s.modifiers}),s.supportsNegative&&t.push({name:`-${u}`,utility:`-${e}`,fraction:d,modifiers:s.modifiers})}}return t.length===0?[]:(t.sort((e,o)=>Je(e.name,o.name)),hi(t))}function hi(r){let t=[],i=null,e=new Map,o=new q(()=>[]);for(let l of r){let{utility:d,fraction:u}=l;i||(i={utility:d,items:[]},e.set(d,i)),d!==i.utility&&(t.push(i),i={utility:d,items:[]},e.set(d,i)),u?o.get(d).push(l):i.items.push(l)}i&&t[t.length-1]!==i&&t.push(i);for(let[l,d]of o){let u=e.get(l);u&&u.items.push(...d)}let s=[];for(let l of t)for(let d of l.items)s.push([d.name,{modifiers:d.modifiers}]);return s}function nr(r){let t=[];for(let[e,o]of r.variants.entries()){let d=function({value:u,modifier:c}={}){let m=e;u&&(m+=s?`-${u}`:u),c&&(m+=`/${c}`);let g=r.parseVariant(m);if(!g)return[];let h=L(".__placeholder__",[]);if(xe(h,g,r.variants)===null)return[];let w=[];return We(h.nodes,(v,{path:A})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;A.sort((V,S)=>{let K=V.kind==="at-rule",O=S.kind==="at-rule";return K&&!O?-1:!K&&O?1:0});let k=A.flatMap(V=>V.kind==="rule"?V.selector==="&"?[]:[V.selector]:V.kind==="at-rule"?[`${V.name} ${V.params}`]:[]),b="";for(let V=k.length-1;V>=0;V--)b=b===""?k[V]:`${k[V]} { ${b} }`;w.push(b)}),w};var i=d;if(o.kind==="arbitrary")continue;let s=e!=="@",l=r.variants.getCompletions(e);switch(o.kind){case"static":{t.push({name:e,values:l,isArbitrary:!1,hasDash:s,selectors:d});break}case"functional":{t.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:d});break}case"compound":{t.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:d});break}}}return t}function or(r,t){let{astNodes:i,nodeSorting:e}=ce(Array.from(t),r),o=new Map(t.map(l=>[l,null])),s=0n;for(let l of i){let d=e.get(l)?.candidate;d&&o.set(d,o.get(d)??s++)}return t.map(l=>[l,o.get(l)??null])}var He=/^@?[a-zA-Z0-9_-]*$/;var ht=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(t,i,{compounds:e,order:o}={}){this.set(t,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}fromAst(t,i){let e=[];F(i,o=>{o.kind==="rule"?e.push(o.selector):o.kind==="at-rule"&&o.name!=="@slot"&&e.push(`${o.name} ${o.params}`)}),this.static(t,o=>{let s=structuredClone(i);vt(s,o.nodes),o.nodes=s},{compounds:we(e)})}functional(t,i,{compounds:e,order:o}={}){this.set(t,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}compound(t,i,e,{compounds:o,order:s}={}){this.set(t,{kind:"compound",applyFn:e,compoundsWith:i,compounds:o??2,order:s})}group(t,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),t(),this.groupOrder=null}has(t){return this.variants.has(t)}get(t){return this.variants.get(t)}kind(t){return this.variants.get(t)?.kind}compoundsWith(t,i){let e=this.variants.get(t),o=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:we([i.selector])}:this.variants.get(i.root);return!(!e||!o||e.kind!=="compound"||o.compounds===0||e.compoundsWith===0||(e.compoundsWith&o.compounds)===0)}suggest(t,i){this.completions.set(t,i)}getCompletions(t){return this.completions.get(t)?.()??[]}compare(t,i){if(t===i)return 0;if(t===null)return-1;if(i===null)return 1;if(t.kind==="arbitrary"&&i.kind==="arbitrary")return t.selector<i.selector?-1:1;if(t.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(t.root).order,o=this.variants.get(i.root).order,s=e-o;if(s!==0)return s;if(t.kind==="compound"&&i.kind==="compound"){let c=this.compare(t.variant,i.variant);return c!==0?c:t.modifier&&i.modifier?t.modifier.value<i.modifier.value?-1:1:t.modifier?1:i.modifier?-1:0}let l=this.compareFns.get(e);if(l!==void 0)return l(t,i);if(t.root!==i.root)return t.root<i.root?-1:1;let d=t.value,u=i.value;return d===null?-1:u===null||d.kind==="arbitrary"&&u.kind!=="arbitrary"?1:d.kind!=="arbitrary"&&u.kind==="arbitrary"||d.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(t,{kind:i,applyFn:e,compounds:o,compoundsWith:s,order:l}){let d=this.variants.get(t);d?Object.assign(d,{kind:i,applyFn:e,compounds:o}):(l===void 0&&(this.lastOrder=this.nextOrder(),l=this.lastOrder),this.variants.set(t,{kind:i,applyFn:e,order:l,compoundsWith:s,compounds:o}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function we(r){let t=0;for(let i of r){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;t|=1;continue}if(i.includes("::"))return 0;t|=2}return t}function lr(r){let t=new ht;function i(c,m,{compounds:g}={}){g=g??we(m),t.static(c,h=>{h.nodes=m.map(w=>B(w,h.nodes))},{compounds:g})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(c,m){return m.map(g=>{g=g.trim();let h=_(g," ");return h[0]==="not"?h.slice(1).join(" "):c==="@container"?h[0][0]==="("?`not ${g}`:h[1]==="not"?`${h[0]} ${h.slice(2).join(" ")}`:`${h[0]} not ${h.slice(1).join(" ")}`:`not ${g}`})}let o=["@media","@supports","@container"];function s(c){for(let m of o){if(m!==c.name)continue;let g=_(c.params,",");return g.length>1?null:(g=e(c.name,g),I(c.name,g.join(", ")))}return null}function l(c){return c.includes("::")?null:`&:not(${_(c,",").map(g=>(g=g.replaceAll("&","*"),g)).join(", ")})`}t.compound("not",3,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative||m.modifier)return null;let g=!1;if(F([c],(h,{path:w})=>{if(h.kind!=="rule"&&h.kind!=="at-rule")return 0;if(h.nodes.length>0)return 0;let v=[],A=[];for(let b of w)b.kind==="at-rule"?v.push(b):b.kind==="rule"&&A.push(b);if(v.length>1)return 2;if(A.length>1)return 2;let k=[];for(let b of A){let V=l(b.selector);if(!V)return g=!1,2;k.push(L(V,[]))}for(let b of v){let V=s(b);if(!V)return g=!1,2;k.push(V)}return Object.assign(c,L("&",k)),g=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!g)return null}),t.suggest("not",()=>Array.from(t.keys()).filter(c=>t.compoundsWith("not",c))),t.compound("group",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}group\\/${m.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}group)`,h=!1;if(F([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let k of v.slice(0,-1))if(k.kind==="rule")return h=!1,2;let A=w.selector.replaceAll("&",g);_(A,",").length>1&&(A=`:is(${A})`),w.selector=`&:is(${A} *)`,h=!0}),!h)return null}),t.suggest("group",()=>Array.from(t.keys()).filter(c=>t.compoundsWith("group",c))),t.compound("peer",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}peer\\/${m.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}peer)`,h=!1;if(F([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let k of v.slice(0,-1))if(k.kind==="rule")return h=!1,2;let A=w.selector.replaceAll("&",g);_(A,",").length>1&&(A=`:is(${A})`),w.selector=`&:is(${A} ~ *)`,h=!0}),!h)return null}),t.suggest("peer",()=>Array.from(t.keys()).filter(c=>t.compoundsWith("peer",c))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let c=function(){return D([I("@property","--tw-content",[a("syntax",'"*"'),a("initial-value",'""'),a("inherits","false")])])};var d=c;t.static("before",m=>{m.nodes=[L("&::before",[c(),a("content","var(--tw-content)"),...m.nodes])]},{compounds:0}),t.static("after",m=>{m.nodes=[L("&::after",[c(),a("content","var(--tw-content)"),...m.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),t.static("hover",c=>{c.nodes=[L("&:hover",[I("@media","(hover: hover)",c.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),t.compound("in",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(F([c],(h,{path:w})=>{if(h.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;h.selector=`:where(${h.selector.replaceAll("&","*")}) &`,g=!0}),!g)return null}),t.suggest("in",()=>Array.from(t.keys()).filter(c=>t.compoundsWith("in",c))),t.compound("has",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(F([c],(h,{path:w})=>{if(h.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;h.selector=`&:has(${h.selector.replaceAll("&","*")})`,g=!0}),!g)return null}),t.suggest("has",()=>Array.from(t.keys()).filter(c=>t.compoundsWith("has",c))),t.functional("aria",(c,m)=>{if(!m.value||m.modifier)return null;m.value.kind==="arbitrary"?c.nodes=[L(`&[aria-${ar(m.value.value)}]`,c.nodes)]:c.nodes=[L(`&[aria-${m.value.value}="true"]`,c.nodes)]}),t.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),t.functional("data",(c,m)=>{if(!m.value||m.modifier)return null;c.nodes=[L(`&[data-${ar(m.value.value)}]`,c.nodes)]}),t.functional("nth",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-child(${m.value.value})`,c.nodes)]}),t.functional("nth-last",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-last-child(${m.value.value})`,c.nodes)]}),t.functional("nth-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-of-type(${m.value.value})`,c.nodes)]}),t.functional("nth-last-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!E(m.value.value))return null;c.nodes=[L(`&:nth-last-of-type(${m.value.value})`,c.nodes)]}),t.functional("supports",(c,m)=>{if(!m.value||m.modifier)return null;let g=m.value.value;if(g===null)return null;if(/^[\w-]*\s*\(/.test(g)){let h=g.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[I("@supports",h,c.nodes)];return}g.includes(":")||(g=`${g}: var(--tw)`),(g[0]!=="("||g[g.length-1]!==")")&&(g=`(${g})`),c.nodes=[I("@supports",g,c.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(m,g,h,w){if(m===g)return 0;let v=w.get(m);if(v===null)return h==="asc"?-1:1;let A=w.get(g);return A===null?h==="asc"?1:-1:ve(v,A,h)};var u=c;{let m=r.namespace("--breakpoint"),g=new q(h=>{switch(h.kind){case"static":return r.resolveValue(h.root,["--breakpoint"])??null;case"functional":{if(!h.value||h.modifier)return null;let w=null;return h.value.kind==="arbitrary"?w=h.value.value:h.value.kind==="named"&&(w=r.resolveValue(h.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("max",(h,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;h.nodes=[I("@media",`(width < ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"desc",g)),t.suggest("max",()=>Array.from(m.keys()).filter(h=>h!==null)),t.group(()=>{for(let[h,w]of r.namespace("--breakpoint"))h!==null&&t.static(h,v=>{v.nodes=[I("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});t.functional("min",(h,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;h.nodes=[I("@media",`(width >= ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"asc",g)),t.suggest("min",()=>Array.from(m.keys()).filter(h=>h!==null))}{let m=r.namespace("--container"),g=new q(h=>{switch(h.kind){case"functional":{if(h.value===null)return null;let w=null;return h.value.kind==="arbitrary"?w=h.value.value:h.value.kind==="named"&&(w=r.resolveValue(h.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("@max",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"desc",g)),t.suggest("@max",()=>Array.from(m.keys()).filter(h=>h!==null)),t.group(()=>{t.functional("@",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,h.nodes)]},{compounds:1}),t.functional("@min",(h,w)=>{let v=g.get(w);if(v===null)return null;h.nodes=[I("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,h.nodes)]},{compounds:1})},(h,w)=>c(h,w,"asc",g)),t.suggest("@min",()=>Array.from(m.keys()).filter(h=>h!==null)),t.suggest("@",()=>Array.from(m.keys()).filter(h=>h!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),t}function ar(r){if(r.includes("=")){let[t,...i]=_(r,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return r;if(e.length>1){let o=e[e.length-1];if(e[e.length-2]===" "&&(o==="i"||o==="I"||o==="s"||o==="S"))return`${t}="${e.slice(0,-2)}" ${o}`}return`${t}="${e}"`}return r}function vt(r,t){F(r,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(t);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,D([I(i.name,i.params,i.nodes)])),1})}function sr(r){let t=Xt(r),i=lr(r),e=new q(u=>Jt(u,d)),o=new q(u=>Array.from(Gt(u,d))),s=new q(u=>{let c=ur(u,d);try{be(c.map(({node:m})=>m),d)}catch{return[]}return c}),l=new q(u=>{for(let c of Me(u))r.markUsedVariable(c)}),d={theme:r,utilities:t,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let c=[];for(let m of u){let g=!1,{astNodes:h}=ce([m],this,{onInvalidCandidate(){g=!0}});h=he(h,d,0),h.length===0||g?c.push(null):c.push(re(h))}return c},getClassOrder(u){return or(this,u)},getClassList(){return ir(this)},getVariants(){return nr(this)},parseCandidate(u){return o.get(u)},parseVariant(u){return e.get(u)},compileAstNodes(u){return s.get(u)},getVariantOrder(){let u=Array.from(e.values());u.sort((h,w)=>this.variants.compare(h,w));let c=new Map,m,g=0;for(let h of u)h!==null&&(m!==void 0&&this.variants.compare(m,h)!==0&&g++,c.set(h,g),m=h);return c},resolveThemeValue(u,c=!0){let m=u.lastIndexOf("/"),g=null;m!==-1&&(g=u.slice(m+1).trim(),u=u.slice(0,m).trim());let h=r.resolve(null,[u],c?1:0)??void 0;return g&&h?J(h,g):h},trackUsedVariables(u){l.get(u)}};return d}var wt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function ce(r,t,{onInvalidCandidate:i}={}){let e=new Map,o=[],s=new Map;for(let d of r){if(t.invalidCandidates.has(d)){i?.(d);continue}let u=t.parseCandidate(d);if(u.length===0){i?.(d);continue}s.set(d,u)}let l=t.getVariantOrder();for(let[d,u]of s){let c=!1;for(let m of u){let g=t.compileAstNodes(m);if(g.length!==0){c=!0;for(let{node:h,propertySort:w}of g){let v=0n;for(let A of m.variants)v|=1n<<BigInt(l.get(A));e.set(h,{properties:w,variants:v,candidate:d}),o.push(h)}}}c||i?.(d)}return o.sort((d,u)=>{let c=e.get(d),m=e.get(u);if(c.variants-m.variants!==0n)return Number(c.variants-m.variants);let g=0;for(;g<c.properties.order.length&&g<m.properties.order.length&&c.properties.order[g]===m.properties.order[g];)g+=1;return(c.properties.order[g]??1/0)-(m.properties.order[g]??1/0)||m.properties.count-c.properties.count||Je(c.candidate,m.candidate)}),{astNodes:o,nodeSorting:e}}function ur(r,t){let i=vi(r,t);if(i.length===0)return[];let e=[],o=`.${se(r.raw)}`;for(let s of i){let l=wi(s);(r.important||t.important)&&cr(s);let d={kind:"rule",selector:o,nodes:s};for(let u of r.variants)if(xe(d,u,t.variants)===null)return[];e.push({node:d,propertySort:l})}return e}function xe(r,t,i,e=0){if(t.kind==="arbitrary"){if(t.relative&&e===0)return null;r.nodes=[B(t.selector,r.nodes)];return}let{applyFn:o}=i.get(t.root);if(t.kind==="compound"){let l=I("@slot");if(xe(l,t.variant,i,e+1)===null||t.root==="not"&&l.nodes.length>1)return null;for(let u of l.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||o(u,t)===null)return null;F(l.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=r.nodes,1}),r.nodes=l.nodes;return}if(o(r,t)===null)return null}function fr(r){let t=r.options?.types??[];return t.length>1&&t.includes("any")}function vi(r,t){if(r.kind==="arbitrary"){let l=r.value;return r.modifier&&(l=Z(l,r.modifier,t.theme)),l===null?[]:[[a(r.property,l)]]}let i=t.utilities.get(r.root)??[],e=[],o=i.filter(l=>!fr(l));for(let l of o){if(l.kind!==r.kind)continue;let d=l.compileFn(r);if(d!==void 0){if(d===null)return e;e.push(d)}}if(e.length>0)return e;let s=i.filter(l=>fr(l));for(let l of s){if(l.kind!==r.kind)continue;let d=l.compileFn(r);if(d!==void 0){if(d===null)return e;e.push(d)}}return e}function cr(r){for(let t of r)t.kind!=="at-root"&&(t.kind==="declaration"?t.important=!0:(t.kind==="rule"||t.kind==="at-rule")&&cr(t.nodes))}function wi(r){let t=new Set,i=0,e=r.slice(),o=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,o))continue;if(s.property==="--tw-sort"){let d=wt.indexOf(s.value??"");if(d!==-1){t.add(d),o=!0;continue}}let l=wt.indexOf(s.property);l!==-1&&t.add(l)}else if(s.kind==="rule"||s.kind==="at-rule")for(let l of s.nodes)e.push(l)}return{order:Array.from(t).sort((s,l)=>s-l),count:i}}function Re(r,t){let i=0,e=B("&",r),o=new Set,s=new q(()=>new Set),l=new q(()=>new Set);F([e],(g,{parent:h})=>{if(g.kind==="at-rule"){if(g.name==="@keyframes")return F(g.nodes,w=>{if(w.kind==="at-rule"&&w.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(g.name==="@utility"){let w=g.params.replace(/-\*$/,"");l.get(w).add(g),F(g.nodes,v=>{if(!(v.kind!=="at-rule"||v.name!=="@apply")){o.add(g);for(let A of dr(v,t))s.get(g).add(A)}});return}if(g.name==="@apply"){if(h===null)return;i|=1,o.add(h);for(let w of dr(g,t))s.get(h).add(w)}}});let d=new Set,u=[],c=new Set;function m(g,h=[]){if(!d.has(g)){if(c.has(g)){let w=h[(h.indexOf(g)+1)%h.length];throw g.kind==="at-rule"&&g.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&F(g.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let A=v.params.split(/\s+/g);for(let k of A)for(let b of t.parseCandidate(k))switch(b.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===b.root)throw new Error(`You cannot \`@apply\` the \`${k}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${re([g])}
Relies on:

${re([w])}`)}c.add(g);for(let w of s.get(g))for(let v of l.get(w))h.push(g),m(v,h),h.pop();d.add(g),c.delete(g),u.push(g)}}for(let g of o)m(g);for(let g of u)if("nodes"in g)for(let h=0;h<g.nodes.length;h++){let w=g.nodes[h];if(w.kind!=="at-rule"||w.name!=="@apply")continue;let v=w.params.split(/\s+/g);{let A=ce(v,t,{onInvalidCandidate:b=>{throw new Error(`Cannot apply unknown utility class: ${b}`)}}).astNodes,k=[];for(let b of A)if(b.kind==="rule")for(let V of b.nodes)k.push(V);else k.push(b);g.nodes.splice(h,1,...k)}}return i}function*dr(r,t){for(let i of r.params.split(/\s+/g))for(let e of t.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function yt(r,t,i,e=0){let o=0,s=[];return F(r,(l,{replaceWith:d})=>{if(l.kind==="at-rule"&&(l.name==="@import"||l.name==="@reference")){let u=yi(G(l.params));if(u===null)return;l.name==="@reference"&&(u.media="reference"),o|=2;let{uri:c,layer:m,media:g,supports:h}=u;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let w=ne({},[]);return s.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${c}\` in \`${t}\`)`);let v=await i(c,t),A=me(v.content);await yt(A,v.base,i,e+1),w.nodes=ki([ne({base:v.base},A)],m,g,h)})()),d(w),1}}),s.length>0&&await Promise.all(s),o}function yi(r){let t,i=null,e=null,o=null;for(let s=0;s<r.length;s++){let l=r[s];if(l.kind!=="separator"){if(l.kind==="word"&&!t){if(!l.value||l.value[0]!=='"'&&l.value[0]!=="'")return null;t=l.value.slice(1,-1);continue}if(l.kind==="function"&&l.value.toLowerCase()==="url"||!t)return null;if((l.kind==="word"||l.kind==="function")&&l.value.toLowerCase()==="layer"){if(i)return null;if(o)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in l?i=Y(l.nodes):i="";continue}if(l.kind==="function"&&l.value.toLowerCase()==="supports"){if(o)return null;o=Y(l.nodes);continue}e=Y(r.slice(s));break}}return t?{uri:t,layer:i,media:e,supports:o}:null}function ki(r,t,i,e){let o=r;return t!==null&&(o=[I("@layer",t,o)]),i!==null&&(o=[I("@media",i,o)]),e!==null&&(o=[I("@supports",e[0]==="("?e:`(${e})`,o)]),o}function Ae(r,t=null){return Array.isArray(r)&&r.length===2&&typeof r[1]=="object"&&typeof r[1]!==null?t?r[1][t]??null:r[0]:Array.isArray(r)&&t===null?r.join(", "):typeof r=="string"&&t===null?r:null}function pr(r,{theme:t},i){for(let e of i){let o=Ye([e]);o&&r.theme.clearNamespace(`--${o}`,4)}for(let[e,o]of bi(t)){if(typeof o!="string"&&typeof o!="number")continue;if(typeof o=="string"&&(o=o.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof o=="number"||typeof o=="string")){let l=typeof o=="string"?parseFloat(o):o;l>=0&&l<=1&&(o=l*100+"%")}let s=Ye(e);s&&r.theme.add(`--${s}`,""+o,7)}if(Object.hasOwn(t,"fontFamily")){let e=5;{let o=Ae(t.fontFamily.sans);o&&r.theme.hasDefault("--font-sans")&&(r.theme.add("--default-font-family",o,e),r.theme.add("--default-font-feature-settings",Ae(t.fontFamily.sans,"fontFeatureSettings")??"normal",e),r.theme.add("--default-font-variation-settings",Ae(t.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let o=Ae(t.fontFamily.mono);o&&r.theme.hasDefault("--font-mono")&&(r.theme.add("--default-mono-font-family",o,e),r.theme.add("--default-mono-font-feature-settings",Ae(t.fontFamily.mono,"fontFeatureSettings")??"normal",e),r.theme.add("--default-mono-font-variation-settings",Ae(t.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return t}function bi(r){let t=[];return mr(r,[],(i,e)=>{if(Ai(i))return t.push([e,i]),1;if(Ci(i)){t.push([e,i[0]]);for(let o of Reflect.ownKeys(i[1]))t.push([[...e,`-${o}`],i[1][o]]);return 1}if(Array.isArray(i)&&i.every(o=>typeof o=="string"))return e[0]==="fontSize"?(t.push([e,i[0]]),i.length>=2&&t.push([[...e,"-line-height"],i[1]])):t.push([e,i.join(", ")]),1}),t}var xi=/^[a-zA-Z0-9-_%/\.]+$/;function Ye(r){if(r[0]==="container")return null;r=structuredClone(r),r[0]==="animation"&&(r[0]="animate"),r[0]==="aspectRatio"&&(r[0]="aspect"),r[0]==="borderRadius"&&(r[0]="radius"),r[0]==="boxShadow"&&(r[0]="shadow"),r[0]==="colors"&&(r[0]="color"),r[0]==="containers"&&(r[0]="container"),r[0]==="fontFamily"&&(r[0]="font"),r[0]==="fontSize"&&(r[0]="text"),r[0]==="letterSpacing"&&(r[0]="tracking"),r[0]==="lineHeight"&&(r[0]="leading"),r[0]==="maxWidth"&&(r[0]="container"),r[0]==="screens"&&(r[0]="breakpoint"),r[0]==="transitionTimingFunction"&&(r[0]="ease");for(let t of r)if(!xi.test(t))return null;return r.map((t,i,e)=>t==="1"&&i!==e.length-1?"":t).map(t=>t.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,o)=>`${e}-${o.toLowerCase()}`)).filter((t,i)=>t!=="DEFAULT"||i!==r.length-1).join("-")}function Ai(r){return typeof r=="number"||typeof r=="string"}function Ci(r){if(!Array.isArray(r)||r.length!==2||typeof r[0]!="string"&&typeof r[0]!="number"||r[1]===void 0||r[1]===null||typeof r[1]!="object")return!1;for(let t of Reflect.ownKeys(r[1]))if(typeof t!="string"||typeof r[1][t]!="string"&&typeof r[1][t]!="number")return!1;return!0}function mr(r,t=[],i){for(let e of Reflect.ownKeys(r)){let o=r[e];if(o==null)continue;let s=[...t,e],l=i(o,s)??0;if(l!==1){if(l===2)return 2;if(!(!Array.isArray(o)&&typeof o!="object")&&mr(o,s,i)===2)return 2}}}function Ze(r){let t=[];for(let i of _(r,".")){if(!i.includes("[")){t.push(i);continue}let e=0;for(;;){let o=i.indexOf("[",e),s=i.indexOf("]",o);if(o===-1||s===-1)break;o>e&&t.push(i.slice(e,o)),t.push(i.slice(o+1,s)),e=s+1}e<=i.length-1&&t.push(i.slice(e))}return t}function Ce(r){if(Object.prototype.toString.call(r)!=="[object Object]")return!1;let t=Object.getPrototypeOf(r);return t===null||Object.getPrototypeOf(t)===null}function Ke(r,t,i,e=[]){for(let o of t)if(o!=null)for(let s of Reflect.ownKeys(o)){e.push(s);let l=i(r[s],o[s],e);l!==void 0?r[s]=l:!Ce(r[s])||!Ce(o[s])?r[s]=o[s]:r[s]=Ke({},[r[s],o[s]],i,e),e.pop()}return r}function Qe(r,t,i){return function(o,s){let l=o.lastIndexOf("/"),d=null;l!==-1&&(d=o.slice(l+1).trim(),o=o.slice(0,l).trim());let u=(()=>{let c=Ze(o),[m,g]=$i(r.theme,c),h=i(gr(t()??{},c)??null);if(typeof h=="string"&&(h=h.replace("<alpha-value>","1")),typeof m!="object")return typeof g!="object"&&g&4?h??m:m;if(h!==null&&typeof h=="object"&&!Array.isArray(h)){let w=Ke({},[h],(v,A)=>A);if(m===null&&Object.hasOwn(h,"__CSS_VALUES__")){let v={};for(let A in h.__CSS_VALUES__)v[A]=h[A],delete w[A];m=v}for(let v in m)v!=="__CSS_VALUES__"&&(h?.__CSS_VALUES__?.[v]&4&&gr(w,v.split("-"))!==void 0||(w[ge(v)]=m[v]));return w}if(Array.isArray(m)&&Array.isArray(g)&&Array.isArray(h)){let w=m[0],v=m[1];g[0]&4&&(w=h[0]??w);for(let A of Object.keys(v))g[1][A]&4&&(v[A]=h[1][A]??v[A]);return[w,v]}return m??h})();return d&&typeof u=="string"&&(u=J(u,d)),u??s}}function $i(r,t){if(t.length===1&&t[0].startsWith("--"))return[r.get([t[0]]),r.getOptions(t[0])];let i=Ye(t),e=new Map,o=new q(()=>new Map),s=r.namespace(`--${i}`);if(s.size===0)return[null,0];let l=new Map;for(let[m,g]of s){if(!m||!m.includes("--")){e.set(m,g),l.set(m,r.getOptions(m?`--${i}-${m}`:`--${i}`));continue}let h=m.indexOf("--"),w=m.slice(0,h),v=m.slice(h+2);v=v.replace(/-([a-z])/g,(A,k)=>k.toUpperCase()),o.get(w===""?null:w).set(v,[g,r.getOptions(`--${i}${m}`)])}let d=r.getOptions(`--${i}`);for(let[m,g]of o){let h=e.get(m);if(typeof h!="string")continue;let w={},v={};for(let[A,[k,b]]of g)w[A]=k,v[A]=b;e.set(m,[h,w]),l.set(m,[d,v])}let u={},c={};for(let[m,g]of e)hr(u,[m??"DEFAULT"],g);for(let[m,g]of l)hr(c,[m??"DEFAULT"],g);return t[t.length-1]==="DEFAULT"?[u?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,c.DEFAULT??0]:(u.__CSS_VALUES__=c,[u,c])}function gr(r,t){for(let i=0;i<t.length;++i){let e=t[i];if(r?.[e]===void 0){if(t[i+1]===void 0)return;t[i+1]=`${e}-${t[i+1]}`;continue}r=r[e]}return r}function hr(r,t,i){for(let e of t.slice(0,-1))r[e]===void 0&&(r[e]={}),r=r[e];r[t[t.length-1]]=i}function Ni(r){return{kind:"combinator",value:r}}function Vi(r,t){return{kind:"function",value:r,nodes:t}}function Pe(r){return{kind:"selector",value:r}}function Ti(r){return{kind:"separator",value:r}}function Si(r){return{kind:"value",value:r}}function Oe(r,t,i=null){for(let e=0;e<r.length;e++){let o=r[e],s=!1,l=0,d=t(o,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(r.splice(e,1),l=0):u.length===1?(r[e]=u[0],l=1):(r.splice(e,1,...u),l=u.length):(r[e]=u,l=1))}})??0;if(s){d===0?e--:e+=l-1;continue}if(d===2)return 2;if(d!==1&&o.kind==="function"&&Oe(o.nodes,t,o)===2)return 2}}function je(r){let t="";for(let i of r)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{t+=i.value;break}case"function":t+=i.value+"("+je(i.nodes)+")"}return t}var vr=92,Ei=93,wr=41,Ri=58,yr=44,Ki=34,Pi=46,kr=62,br=10,Oi=35,xr=91,Ar=40,Cr=43,ji=39,$r=32,Nr=9,Vr=126;function Xe(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,o="",s;for(let l=0;l<r.length;l++){let d=r.charCodeAt(l);switch(d){case yr:case kr:case br:case $r:case Cr:case Nr:case Vr:{if(o.length>0){let h=Pe(o);e?e.nodes.push(h):t.push(h),o=""}let u=l,c=l+1;for(;c<r.length&&(s=r.charCodeAt(c),!(s!==yr&&s!==kr&&s!==br&&s!==$r&&s!==Cr&&s!==Nr&&s!==Vr));c++);l=c-1;let m=r.slice(u,c),g=m.trim()===","?Ti(m):Ni(m);e?e.nodes.push(g):t.push(g);break}case Ar:{let u=Vi(o,[]);if(o="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let c=l+1,m=0;for(let h=l+1;h<r.length;h++){if(s=r.charCodeAt(h),s===Ar){m++;continue}if(s===wr){if(m===0){l=h;break}m--}}let g=l;u.nodes.push(Si(r.slice(c,g))),o="",l=g,e?e.nodes.push(u):t.push(u);break}e?e.nodes.push(u):t.push(u),i.push(u),e=u;break}case wr:{let u=i.pop();if(o.length>0){let c=Pe(o);u.nodes.push(c),o=""}i.length>0?e=i[i.length-1]:e=null;break}case Pi:case Ri:case Oi:{if(o.length>0){let u=Pe(o);e?e.nodes.push(u):t.push(u)}o=String.fromCharCode(d);break}case xr:{if(o.length>0){let m=Pe(o);e?e.nodes.push(m):t.push(m)}o="";let u=l,c=0;for(let m=l+1;m<r.length;m++){if(s=r.charCodeAt(m),s===xr){c++;continue}if(s===Ei){if(c===0){l=m;break}c--}}o+=r.slice(u,l+1);break}case ji:case Ki:{let u=l;for(let c=l+1;c<r.length;c++)if(s=r.charCodeAt(c),s===vr)c+=1;else if(s===d){l=c;break}o+=r.slice(u,l+1);break}case vr:{let u=r.charCodeAt(l+1);o+=String.fromCharCode(d)+String.fromCharCode(u),l+=1;break}default:o+=String.fromCharCode(d)}}return o.length>0&&t.push(Pe(o)),t}var Tr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function kt({designSystem:r,ast:t,resolvedConfig:i,featuresRef:e,referenceMode:o}){let s={addBase(l){if(o)return;let d=oe(l);e.current|=be(d,r),t.push(I("@layer","base",d))},addVariant(l,d){if(!He.test(l))throw new Error(`\`addVariant('${l}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof d=="string"||Array.isArray(d)?r.variants.static(l,u=>{u.nodes=Sr(d,u.nodes)},{compounds:we(typeof d=="string"?[d]:d)}):typeof d=="object"&&r.variants.fromAst(l,oe(d))},matchVariant(l,d,u){function c(g,h,w){let v=d(g,{modifier:h?.value??null});return Sr(v,w)}let m=Object.keys(u?.values??{});r.variants.group(()=>{r.variants.functional(l,(g,h)=>{if(!h.value){if(u?.values&&"DEFAULT"in u.values){g.nodes=c(u.values.DEFAULT,h.modifier,g.nodes);return}return null}if(h.value.kind==="arbitrary")g.nodes=c(h.value.value,h.modifier,g.nodes);else if(h.value.kind==="named"&&u?.values){let w=u.values[h.value.value];if(typeof w!="string")return;g.nodes=c(w,h.modifier,g.nodes)}})},(g,h)=>{if(g.kind!=="functional"||h.kind!=="functional")return 0;let w=g.value?g.value.value:"DEFAULT",v=h.value?h.value.value:"DEFAULT",A=u?.values?.[w]??w,k=u?.values?.[v]??v;if(u&&typeof u.sort=="function")return u.sort({value:A,modifier:g.modifier?.value??null},{value:k,modifier:h.modifier?.value??null});let b=m.indexOf(w),V=m.indexOf(v);return b=b===-1?m.length:b,V=V===-1?m.length:V,b!==V?b-V:A<k?-1:1})},addUtilities(l){l=Array.isArray(l)?l:[l];let d=l.flatMap(c=>Object.entries(c));d=d.flatMap(([c,m])=>_(c,",").map(g=>[g.trim(),m]));let u=new q(()=>[]);for(let[c,m]of d){if(c.startsWith("@keyframes ")){o||t.push(B(c,oe(m)));continue}let g=Xe(c),h=!1;if(Oe(g,w=>{if(w.kind==="selector"&&w.value[0]==="."&&Tr.test(w.value.slice(1))){let v=w.value;w.value="&";let A=je(g),k=v.slice(1),b=A==="&"?oe(m):[B(A,oe(m))];u.get(k).push(...b),h=!0,w.value=v;return}if(w.kind==="function"&&w.value===":not")return 1}),!h)throw new Error(`\`addUtilities({ '${c}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[c,m]of u)r.theme.prefix&&F(m,g=>{if(g.kind==="rule"){let h=Xe(g.selector);Oe(h,w=>{w.kind==="selector"&&w.value[0]==="."&&(w.value=`.${r.theme.prefix}\\:${w.value.slice(1)}`)}),g.selector=je(h)}}),r.utilities.static(c,g=>{let h=structuredClone(m);return Er(h,c,g.raw),e.current|=Re(h,r),h})},matchUtilities(l,d){let u=d?.type?Array.isArray(d?.type)?d.type:[d.type]:["any"];for(let[m,g]of Object.entries(l)){let h=function({negative:w}){return v=>{if(v.value?.kind==="arbitrary"&&u.length>0&&!u.includes("any")&&(v.value.dataType&&!u.includes(v.value.dataType)||!v.value.dataType&&!W(v.value.value,u)))return;let A=u.includes("color"),k=null,b=!1;{let K=d?.values??{};A&&(K=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},K)),v.value?v.value.kind==="arbitrary"?k=v.value.value:v.value.fraction&&K[v.value.fraction]?(k=K[v.value.fraction],b=!0):K[v.value.value]?k=K[v.value.value]:K.__BARE_VALUE__&&(k=K.__BARE_VALUE__(v.value)??null,b=(v.value.fraction!==null&&k?.includes("/"))??!1):k=K.DEFAULT??null}if(k===null)return;let V;{let K=d?.modifiers??null;v.modifier?K==="any"||v.modifier.kind==="arbitrary"?V=v.modifier.value:K?.[v.modifier.value]?V=K[v.modifier.value]:A&&!Number.isNaN(Number(v.modifier.value))?V=`${v.modifier.value}%`:V=null:V=null}if(v.modifier&&V===null&&!b)return v.value?.kind==="arbitrary"?null:void 0;A&&V!==null&&(k=J(k,V)),w&&(k=`calc(${k} * -1)`);let S=oe(g(k,{modifier:V}));return Er(S,m,v.raw),e.current|=Re(S,r),S}};var c=h;if(!Tr.test(m))throw new Error(`\`matchUtilities({ '${m}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);d?.supportsNegativeValues&&r.utilities.functional(`-${m}`,h({negative:!0}),{types:u}),r.utilities.functional(m,h({negative:!1}),{types:u}),r.utilities.suggest(m,()=>{let w=d?.values??{},v=new Set(Object.keys(w));v.delete("__BARE_VALUE__"),v.has("DEFAULT")&&(v.delete("DEFAULT"),v.add(null));let A=d?.modifiers??{},k=A==="any"?[]:Object.keys(A);return[{supportsNegative:d?.supportsNegativeValues??!1,values:Array.from(v),modifiers:k}]})}},addComponents(l,d){this.addUtilities(l,d)},matchComponents(l,d){this.matchUtilities(l,d)},theme:Qe(r,()=>i.theme??{},l=>l),prefix(l){return l},config(l,d){let u=i;if(!l)return u;let c=Ze(l);for(let m=0;m<c.length;++m){let g=c[m];if(u[g]===void 0)return d;u=u[g]}return u??d}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function oe(r){let t=[];r=Array.isArray(r)?r:[r];let i=r.flatMap(e=>Object.entries(e));for(let[e,o]of i)if(typeof o!="object"){if(!e.startsWith("--")){if(o==="@slot"){t.push(B(e,[I("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}t.push(a(e,String(o)))}else if(Array.isArray(o))for(let s of o)typeof s=="string"?t.push(a(e,s)):t.push(B(e,oe(s)));else o!==null&&t.push(B(e,oe(o)));return t}function Sr(r,t){return(typeof r=="string"?[r]:r).flatMap(e=>{if(e.trim().endsWith("}")){let o=e.replace("}","{@slot}}"),s=me(o);return vt(s,t),s}else return B(e,t)})}function Er(r,t,i){F(r,e=>{if(e.kind==="rule"){let o=Xe(e.selector);Oe(o,s=>{s.kind==="selector"&&s.value===`.${t}`&&(s.value=`.${se(i)}`)}),e.selector=je(o)}})}function Rr(r,t,i){for(let e of Di(t))r.theme.addKeyframes(e)}function Di(r){let t=[];if("keyframes"in r.theme)for(let[i,e]of Object.entries(r.theme.keyframes))t.push(I("@keyframes",i,oe(e)));return t}function Kr(r){return{theme:{...$t,colors:({theme:t})=>t("color",{}),extend:{fontSize:({theme:t})=>({...t("text",{})}),boxShadow:({theme:t})=>({...t("shadow",{})}),animation:({theme:t})=>({...t("animate",{})}),aspectRatio:({theme:t})=>({...t("aspect",{})}),borderRadius:({theme:t})=>({...t("radius",{})}),screens:({theme:t})=>({...t("breakpoint",{})}),letterSpacing:({theme:t})=>({...t("tracking",{})}),lineHeight:({theme:t})=>({...t("leading",{})}),transitionDuration:{DEFAULT:r.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:r.get(["--default-transition-timing-function"])??null},maxWidth:({theme:t})=>({...t("container",{})})}}}}var Ui={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function xt(r,t){let i={design:r,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(Ui)};for(let o of t)bt(i,o);for(let o of i.configs)"darkMode"in o&&o.darkMode!==void 0&&(i.result.darkMode=o.darkMode??null),"prefix"in o&&o.prefix!==void 0&&(i.result.prefix=o.prefix??""),"blocklist"in o&&o.blocklist!==void 0&&(i.result.blocklist=o.blocklist??[]),"important"in o&&o.important!==void 0&&(i.result.important=o.important??!1);let e=Fi(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function Ii(r,t){if(Array.isArray(r)&&Ce(r[0]))return r.concat(t);if(Array.isArray(t)&&Ce(t[0])&&Ce(r))return[r,...t];if(Array.isArray(t))return t}function bt(r,{config:t,base:i,path:e,reference:o}){let s=[];for(let u of t.plugins??[])"__isOptionsFunction"in u?s.push({...u(),reference:o}):"handler"in u?s.push({...u,reference:o}):s.push({handler:u,reference:o});if(Array.isArray(t.presets)&&t.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let u of t.presets??[])bt(r,{path:e,base:i,config:u,reference:o});for(let u of s)r.plugins.push(u),u.config&&bt(r,{path:e,base:i,config:u.config,reference:!!u.reference});let l=t.content??[],d=Array.isArray(l)?l:l.files;for(let u of d)r.content.files.push(typeof u=="object"?u:{base:i,pattern:u});r.configs.push(t)}function Fi(r){let t=new Set,i=Qe(r.design,()=>r.theme,o),e=Object.assign(i,{theme:i,colors:Ct});function o(s){return typeof s=="function"?s(e)??null:s??null}for(let s of r.configs){let l=s.theme??{},d=l.extend??{};for(let u in l)u!=="extend"&&t.add(u);Object.assign(r.theme,l);for(let u in d)r.extend[u]??=[],r.extend[u].push(d[u])}delete r.theme.extend;for(let s in r.extend){let l=[r.theme[s],...r.extend[s]];r.theme[s]=()=>{let d=l.map(o);return Ke({},d,Ii)}}for(let s in r.theme)r.theme[s]=o(r.theme[s]);if(r.theme.screens&&typeof r.theme.screens=="object")for(let s of Object.keys(r.theme.screens)){let l=r.theme.screens[s];l&&typeof l=="object"&&("raw"in l||"max"in l||"min"in l&&(r.theme.screens[s]=l.min))}return t}function Pr(r,t){let i=r.theme.container||{};if(typeof i!="object"||i===null)return;let e=zi(i,t);e.length!==0&&t.utilities.static("container",()=>structuredClone(e))}function zi({center:r,padding:t,screens:i},e){let o=[],s=null;if(r&&o.push(a("margin-inline","auto")),(typeof t=="string"||typeof t=="object"&&t!==null&&"DEFAULT"in t)&&o.push(a("padding-inline",typeof t=="string"?t:t.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let l=Array.from(e.theme.namespace("--breakpoint").entries());if(l.sort((d,u)=>ve(d[1],u[1],"asc")),l.length>0){let[d]=l[0];o.push(I("@media",`(width >= --theme(--breakpoint-${d}))`,[a("max-width","none")]))}for(let[d,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(d,I("@media",`(width >= ${u})`,[a("max-width",u)]))}}if(typeof t=="object"&&t!==null){let l=Object.entries(t).filter(([d])=>d!=="DEFAULT").map(([d,u])=>[d,e.theme.resolveValue(d,["--breakpoint"]),u]).filter(Boolean);l.sort((d,u)=>ve(d[1],u[1],"asc"));for(let[d,,u]of l)if(s&&s.has(d))s.get(d).nodes.push(a("padding-inline",u));else{if(s)continue;o.push(I("@media",`(width >= theme(--breakpoint-${d}))`,[a("padding-inline",u)]))}}if(s)for(let[,l]of s)o.push(l);return o}function Or({addVariant:r,config:t}){let i=t("darkMode",null),[e,o=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(o)||typeof o=="function"?s=o:typeof o=="string"&&(s=[o]),Array.isArray(s))for(let l of s)l===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):l.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=s}e===null||(e==="selector"?r("dark",`&:where(${o}, ${o} *)`):e==="media"?r("dark","@media (prefers-color-scheme: dark)"):e==="variant"?r("dark",o):e==="class"&&r("dark",`&:is(${o} *)`))}function jr(r){for(let[t,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])r.utilities.static(`bg-gradient-to-${t}`,()=>[a("--tw-gradient-position",`to ${i} in oklab`),a("background-image","linear-gradient(var(--tw-gradient-stops))")]);r.utilities.static("bg-left-top",()=>[a("background-position","left top")]),r.utilities.static("bg-right-top",()=>[a("background-position","right top")]),r.utilities.static("bg-left-bottom",()=>[a("background-position","left bottom")]),r.utilities.static("bg-right-bottom",()=>[a("background-position","right bottom")]),r.utilities.static("object-left-top",()=>[a("object-position","left top")]),r.utilities.static("object-right-top",()=>[a("object-position","right top")]),r.utilities.static("object-left-bottom",()=>[a("object-position","left bottom")]),r.utilities.static("object-right-bottom",()=>[a("object-position","right bottom")]),r.utilities.functional("max-w-screen",t=>{if(!t.value||t.value.kind==="arbitrary")return;let i=r.theme.resolve(t.value.value,["--breakpoint"]);if(i)return[a("max-width",i)]}),r.utilities.static("overflow-ellipsis",()=>[a("text-overflow","ellipsis")]),r.utilities.static("decoration-slice",()=>[a("-webkit-box-decoration-break","slice"),a("box-decoration-break","slice")]),r.utilities.static("decoration-clone",()=>[a("-webkit-box-decoration-break","clone"),a("box-decoration-break","clone")]),r.utilities.functional("flex-shrink",t=>{if(!t.modifier){if(!t.value)return[a("flex-shrink","1")];if(t.value.kind==="arbitrary")return[a("flex-shrink",t.value.value)];if(E(t.value.value))return[a("flex-shrink",t.value.value)]}}),r.utilities.functional("flex-grow",t=>{if(!t.modifier){if(!t.value)return[a("flex-grow","1")];if(t.value.kind==="arbitrary")return[a("flex-grow",t.value.value)];if(E(t.value.value))return[a("flex-grow",t.value.value)]}})}function _r(r,t){let i=r.theme.screens||{},e=t.variants.get("min")?.order??0,o=[];for(let[l,d]of Object.entries(i)){let h=function(w){t.variants.static(l,v=>{v.nodes=[I("@media",g,v.nodes)]},{order:w})};var s=h;let u=t.variants.get(l),c=t.theme.resolveValue(l,["--breakpoint"]);if(u&&c&&!t.theme.hasDefault(`--breakpoint-${l}`))continue;let m=!0;typeof d=="string"&&(m=!1);let g=Li(d);m?o.push(h):h(e)}if(o.length!==0){for(let[,l]of t.variants.variants)l.order>e&&(l.order+=o.length);t.variants.compareFns=new Map(Array.from(t.variants.compareFns).map(([l,d])=>(l>e&&(l+=o.length),[l,d])));for(let[l,d]of o.entries())d(e+l+1)}}function Li(r){return(Array.isArray(r)?r:[r]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function Dr(r,t){let i=r.theme.aria||{},e=r.theme.supports||{},o=r.theme.data||{};if(Object.keys(i).length>0){let s=t.variants.get("aria"),l=s?.applyFn,d=s?.compounds;t.variants.functional("aria",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in i?l?.(u,{...c,value:{kind:"arbitrary",value:i[m.value]}}):l?.(u,c)},{compounds:d})}if(Object.keys(e).length>0){let s=t.variants.get("supports"),l=s?.applyFn,d=s?.compounds;t.variants.functional("supports",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in e?l?.(u,{...c,value:{kind:"arbitrary",value:e[m.value]}}):l?.(u,c)},{compounds:d})}if(Object.keys(o).length>0){let s=t.variants.get("data"),l=s?.applyFn,d=s?.compounds;t.variants.functional("data",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in o?l?.(u,{...c,value:{kind:"arbitrary",value:o[m.value]}}):l?.(u,c)},{compounds:d})}}var Mi=/^[a-z]+$/;async function Ir({designSystem:r,base:t,ast:i,loadModule:e,sources:o}){let s=0,l=[],d=[];F(i,(g,{parent:h,replaceWith:w,context:v})=>{if(g.kind==="at-rule"){if(g.name==="@plugin"){if(h!==null)throw new Error("`@plugin` cannot be nested.");let A=g.params.slice(1,-1);if(A.length===0)throw new Error("`@plugin` must have a path.");let k={};for(let b of g.nodes??[]){if(b.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${re([b])}

\`@plugin\` options must be a flat list of declarations.`);if(b.value===void 0)continue;let V=b.value,S=_(V,",").map(K=>{if(K=K.trim(),K==="null")return null;if(K==="true")return!0;if(K==="false")return!1;if(Number.isNaN(Number(K))){if(K[0]==='"'&&K[K.length-1]==='"'||K[0]==="'"&&K[K.length-1]==="'")return K.slice(1,-1);if(K[0]==="{"&&K[K.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${re([b]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(K);return K});k[b.property]=S.length===1?S[0]:S}l.push([{id:A,base:v.base,reference:!!v.reference},Object.keys(k).length>0?k:null]),w([]),s|=4;return}if(g.name==="@config"){if(g.nodes.length>0)throw new Error("`@config` cannot have a body.");if(h!==null)throw new Error("`@config` cannot be nested.");d.push({id:g.params.slice(1,-1),base:v.base,reference:!!v.reference}),w([]),s|=4;return}}}),jr(r);let u=r.resolveThemeValue;if(r.resolveThemeValue=function(h,w){return h.startsWith("--")?u(h,w):(s|=Ur({designSystem:r,base:t,ast:i,sources:o,configs:[],pluginDetails:[]}),r.resolveThemeValue(h,w))},!l.length&&!d.length)return 0;let[c,m]=await Promise.all([Promise.all(d.map(async({id:g,base:h,reference:w})=>{let v=await e(g,h,"config");return{path:g,base:v.base,config:v.module,reference:w}})),Promise.all(l.map(async([{id:g,base:h,reference:w},v])=>{let A=await e(g,h,"plugin");return{path:g,base:A.base,plugin:A.module,options:v,reference:w}}))]);return s|=Ur({designSystem:r,base:t,ast:i,sources:o,configs:c,pluginDetails:m}),s}function Ur({designSystem:r,base:t,ast:i,sources:e,configs:o,pluginDetails:s}){let l=0,u=[...s.map(k=>{if(!k.options)return{config:{plugins:[k.plugin]},base:k.base,reference:k.reference};if("__isOptionsFunction"in k.plugin)return{config:{plugins:[k.plugin(k.options)]},base:k.base,reference:k.reference};throw new Error(`The plugin "${k.path}" does not accept options`)}),...o],{resolvedConfig:c}=xt(r,[{config:Kr(r.theme),base:t,reference:!0},...u,{config:{plugins:[Or]},base:t,reference:!0}]),{resolvedConfig:m,replacedThemeKeys:g}=xt(r,u),h=r.resolveThemeValue;r.resolveThemeValue=function(b,V){if(b[0]==="-"&&b[1]==="-")return h(b,V);let S=v.theme(b,void 0);if(Array.isArray(S)&&S.length===2)return S[0];if(Array.isArray(S))return S.join(", ");if(typeof S=="string")return S};let w={designSystem:r,ast:i,resolvedConfig:c,featuresRef:{set current(k){l|=k}}},v=kt({...w,referenceMode:!1}),A;for(let{handler:k,reference:b}of c.plugins)b?(A||=kt({...w,referenceMode:!0}),k(A)):k(v);if(pr(r,m,g),Rr(r,m,g),Dr(m,r),_r(m,r),Pr(m,r),!r.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!Mi.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);r.theme.prefix=c.prefix}if(!r.important&&c.important===!0&&(r.important=!0),typeof c.important=="string"){let k=c.important;F(i,(b,{replaceWith:V,parent:S})=>{if(b.kind==="at-rule"&&!(b.name!=="@tailwind"||b.params!=="utilities"))return S?.kind==="rule"&&S.selector===k?2:(V(L(k,[b])),2)})}for(let k of c.blocklist)r.invalidCandidates.add(k);for(let k of c.content.files){if("raw"in k)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(k,null,2)}

This feature is not currently supported.`);let b=!1;k.pattern[0]=="!"&&(b=!0,k.pattern=k.pattern.slice(1)),e.push({...k,negated:b})}return l}var Fr=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function et(r){let t=r.indexOf("{");if(t===-1)return[r];let i=[],e=r.slice(0,t),o=r.slice(t),s=0,l=o.lastIndexOf("}");for(let g=0;g<o.length;g++){let h=o[g];if(h==="{")s++;else if(h==="}"&&(s--,s===0)){l=g;break}}if(l===-1)throw new Error(`The pattern \`${r}\` is not balanced.`);let d=o.slice(1,l),u=o.slice(l+1),c;Wi(d)?c=Bi(d):c=_(d,","),c=c.flatMap(g=>et(g));let m=et(u);for(let g of m)for(let h of c)i.push(e+h+g);return i}function Wi(r){return Fr.test(r)}function Bi(r){let t=r.match(Fr);if(!t)return[r];let[,i,e,o]=t,s=o?parseInt(o,10):void 0,l=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let d=parseInt(i,10),u=parseInt(e,10);if(s===void 0&&(s=d<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let c=d<u;c&&s<0&&(s=-s),!c&&s>0&&(s=-s);for(let m=d;c?m<=u:m>=u;m+=s)l.push(m.toString())}return l}var qi=/^[a-z]+$/,ut=(o=>(o[o.None=0]="None",o[o.AtProperty=1]="AtProperty",o[o.ColorMix=2]="ColorMix",o[o.All=3]="All",o))(ut||{});function Gi(){throw new Error("No `loadModule` function provided to `compile`")}function Ji(){throw new Error("No `loadStylesheet` function provided to `compile`")}function Hi(r){let t=0,i=null;for(let e of _(r," "))e==="reference"?t|=2:e==="inline"?t|=1:e==="default"?t|=4:e==="static"?t|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[t,i]}var Se=(d=>(d[d.None=0]="None",d[d.AtApply=1]="AtApply",d[d.AtImport=2]="AtImport",d[d.JsPluginCompat=4]="JsPluginCompat",d[d.ThemeFunction=8]="ThemeFunction",d[d.Utilities=16]="Utilities",d[d.Variants=32]="Variants",d))(Se||{});async function zr(r,{base:t="",loadModule:i=Gi,loadStylesheet:e=Ji}={}){let o=0;r=[ne({base:t},r)],o|=await yt(r,t,e);let s=null,l=new Le,d=[],u=[],c=null,m=null,g=[],h=[],w=[],v=[],A=null;F(r,(b,{parent:V,replaceWith:S,context:K})=>{if(b.kind==="at-rule"){if(b.name==="@tailwind"&&(b.params==="utilities"||b.params.startsWith("utilities"))){if(m!==null){S([]);return}let O=_(b.params," ");for(let U of O)if(U.startsWith("source(")){let P=U.slice(7,-1);if(P==="none"){A=P;continue}if(P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");A={base:K.sourceBase??K.base,pattern:P.slice(1,-1)}}m=b,o|=16}if(b.name==="@utility"){if(V!==null)throw new Error("`@utility` cannot be nested.");if(b.nodes.length===0)throw new Error(`\`@utility ${b.params}\` is empty. Utilities should include at least one property.`);let O=er(b);if(O===null)throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);u.push(O)}if(b.name==="@source"){if(b.nodes.length>0)throw new Error("`@source` cannot have a body.");if(V!==null)throw new Error("`@source` cannot be nested.");let O=!1,U=!1,P=b.params;if(P[0]==="n"&&P.startsWith("not ")&&(O=!0,P=P.slice(4)),P[0]==="i"&&P.startsWith("inline(")&&(U=!0,P=P.slice(7,-1)),P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`@source` paths must be quoted.");let H=P.slice(1,-1);if(U){let M=O?v:w,z=_(H," ");for(let te of z)for(let X of et(te))M.push(X)}else h.push({base:K.base,pattern:H,negated:O});S([]);return}if(b.name==="@variant"&&(V===null?b.nodes.length===0?b.name="@custom-variant":(F(b.nodes,O=>{if(O.kind==="at-rule"&&O.name==="@slot")return b.name="@custom-variant",2}),b.name==="@variant"&&g.push(b)):g.push(b)),b.name==="@custom-variant"){if(V!==null)throw new Error("`@custom-variant` cannot be nested.");S([]);let[O,U]=_(b.params," ");if(!He.test(O))throw new Error(`\`@custom-variant ${O}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(b.nodes.length>0&&U)throw new Error(`\`@custom-variant ${O}\` cannot have both a selector and a body.`);if(b.nodes.length===0){if(!U)throw new Error(`\`@custom-variant ${O}\` has no selector or body.`);let P=_(U.slice(1,-1),",");if(P.length===0||P.some(z=>z.trim()===""))throw new Error(`\`@custom-variant ${O} (${P.join(",")})\` selector is invalid.`);let H=[],M=[];for(let z of P)z=z.trim(),z[0]==="@"?H.push(z):M.push(z);d.push(z=>{z.variants.static(O,te=>{let X=[];M.length>0&&X.push(L(M.join(", "),te.nodes));for(let ae of H)X.push(B(ae,te.nodes));te.nodes=X},{compounds:we([...M,...H])})});return}else{d.push(P=>{P.variants.fromAst(O,b.nodes)});return}}if(b.name==="@media"){let O=_(b.params," "),U=[];for(let P of O)if(P.startsWith("source(")){let H=P.slice(7,-1);F(b.nodes,(M,{replaceWith:z})=>{if(M.kind==="at-rule"&&M.name==="@tailwind"&&M.params==="utilities")return M.params+=` source(${H})`,z([ne({sourceBase:K.base},[M])]),2})}else if(P.startsWith("theme(")){let H=P.slice(6,-1),M=H.includes("reference");F(b.nodes,z=>{if(z.kind!=="at-rule"){if(M)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(z.name==="@theme")return z.params+=" "+H,1})}else if(P.startsWith("prefix(")){let H=P.slice(7,-1);F(b.nodes,M=>{if(M.kind==="at-rule"&&M.name==="@theme")return M.params+=` prefix(${H})`,1})}else P==="important"?s=!0:P==="reference"?b.nodes=[ne({reference:!0},b.nodes)]:U.push(P);U.length>0?b.params=U.join(" "):O.length>0&&S(b.nodes)}if(b.name==="@theme"){let[O,U]=Hi(b.params);if(K.reference&&(O|=2),U){if(!qi.test(U))throw new Error(`The prefix "${U}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);l.prefix=U}return F(b.nodes,P=>{if(P.kind==="at-rule"&&P.name==="@keyframes")return l.addKeyframes(P),1;if(P.kind==="comment")return;if(P.kind==="declaration"&&P.property.startsWith("--")){l.add(ge(P.property),P.value??"",O);return}let H=re([I(b.name,b.params,[P])]).split(`
`).map((M,z,te)=>`${z===0||z>=te.length-2?" ":">"} ${M}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${H}`)}),c?S([]):(c=L(":root, :host",[]),S([c])),1}}});let k=sr(l);if(s&&(k.important=s),v.length>0)for(let b of v)k.invalidCandidates.add(b);o|=await Ir({designSystem:k,base:t,ast:r,loadModule:i,sources:h});for(let b of d)b(k);for(let b of u)b(k);if(c){let b=[];for(let[S,K]of k.theme.entries())K.options&2||b.push(a(se(S),K.value));let V=k.theme.getKeyframes();for(let S of V)r.push(ne({theme:!0},[D([S])]));c.nodes=[ne({theme:!0},b)]}if(m){let b=m;b.kind="context",b.context={}}if(g.length>0){for(let b of g){let V=L("&",b.nodes),S=b.params,K=k.parseVariant(S);if(K===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${S}`);if(xe(V,K,k.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${S}`);Object.assign(b,V)}o|=32}return o|=be(r,k),o|=Re(r,k),F(r,(b,{replaceWith:V})=>{if(b.kind==="at-rule")return b.name==="@utility"&&V([]),1}),{designSystem:k,ast:r,sources:h,root:A,utilitiesNode:m,features:o,inlineCandidates:w}}async function Yi(r,t={}){let{designSystem:i,ast:e,sources:o,root:s,utilitiesNode:l,features:d,inlineCandidates:u}=await zr(r,t);e.unshift(ze(`! tailwindcss v${Nt} | MIT License | https://tailwindcss.com `));function c(v){i.invalidCandidates.add(v)}let m=new Set,g=null,h=0,w=!1;for(let v of u)i.invalidCandidates.has(v)||(m.add(v),w=!0);return{sources:o,root:s,features:d,build(v){if(d===0)return r;if(!l)return g??=he(e,i,t.polyfills),g;let A=w,k=!1;w=!1;let b=m.size;for(let S of v)if(!i.invalidCandidates.has(S))if(S[0]==="-"&&S[1]==="-"){let K=i.theme.markUsedVariable(S);A||=K,k||=K}else m.add(S),A||=m.size!==b;if(!A)return g??=he(e,i,t.polyfills),g;let V=ce(m,i,{onInvalidCandidate:c}).astNodes;return!k&&h===V.length?(g??=he(e,i,t.polyfills),g):(h=V.length,l.nodes=V,g=he(e,i,t.polyfills),g)}}}async function Xa(r,t={}){let i=me(r),e=await Yi(i,t),o=i,s=r;return{...e,build(l){let d=e.build(l);return d===o||(s=re(d),o=d),s}}}async function el(r,t={}){return(await zr(me(r),t)).designSystem}function Zi(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}export{ut as a,Se as b,Yi as c,Xa as d,el as e,Zi as f};
