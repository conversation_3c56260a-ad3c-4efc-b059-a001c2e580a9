{"version": 3, "file": "diacritics.js", "sourceRoot": "", "sources": ["../../../../src/components/tokenizer/diacritics.ts"], "names": [], "mappings": "AAAA,MAAM,yBAAyB,GAAG,GAAG,CAAA;AACrC,MAAM,uBAAuB,GAAG,GAAG,CAAA;AAEnC,MAAM,wBAAwB,GAAG;IAC/B,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,IAAI;IACJ,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,GAAG;IACH,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,EAAE;IACF,GAAG;IACH,GAAG;CACJ,CAAA;AAED,SAAS,WAAW,CAAC,QAAgB;IACnC,IAAI,QAAQ,GAAG,yBAAyB,IAAI,QAAQ,GAAG,uBAAuB;QAAE,OAAO,QAAQ,CAAA;IAE/F,qBAAqB;IACrB,OAAO,wBAAwB,CAAC,QAAQ,GAAG,yBAAyB,CAAC,IAAI,QAAQ,CAAA;AACnF,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,GAAW;IAC3C,MAAM,cAAc,GAAa,EAAE,CAAA;IACnC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;QAC1C,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;IACxD,CAAC;IACD,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC,CAAA;AAC/C,CAAC"}