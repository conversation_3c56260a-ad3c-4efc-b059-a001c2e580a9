{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'ஒரு',\n    'என்று',\n    'மற்றும்',\n    'இந்த',\n    'இது',\n    'என்ற',\n    'கொண்டு',\n    'என்பது',\n    'பல',\n    'ஆகும்',\n    'அல்லது',\n    'அவர்',\n    'நான்',\n    'உள்ள',\n    'அந்த',\n    'இவர்',\n    'என',\n    'முதல்',\n    'என்ன',\n    'இருந்து',\n    'சில',\n    'என்',\n    'போன்ற',\n    'வேண்டும்',\n    'வந்து',\n    'இதன்',\n    'அது',\n    'அவன்',\n    'தான்',\n    'பலரும்',\n    'என்னும்',\n    'மேலும்',\n    'பின்னர்',\n    'கொண்ட',\n    'இருக்கும்',\n    'தனது',\n    'உள்ளது',\n    'போது',\n    'என்றும்',\n    'அதன்',\n    'தன்',\n    'பிறகு',\n    'அவர்கள்',\n    'வரை',\n    'அவள்',\n    'நீ',\n    'ஆகிய',\n    'இருந்தது',\n    'உள்ளன',\n    'வந்த',\n    'இருந்த',\n    'மிகவும்',\n    'இங்கு',\n    'மீது',\n    'ஓர்',\n    'இவை',\n    'இந்தக்',\n    'பற்றி',\n    'வரும்',\n    'வேறு',\n    'இரு',\n    'இதில்',\n    'போல்',\n    'இப்போது',\n    'அவரது',\n    'மட்டும்',\n    'இந்தப்',\n    'எனும்',\n    'மேல்',\n    'பின்',\n    'சேர்ந்த',\n    'ஆகியோர்',\n    'எனக்கு',\n    'இன்னும்',\n    'அந்தப்',\n    'அன்று',\n    'ஒரே',\n    'மிக',\n    'அங்கு',\n    'பல்வேறு',\n    'விட்டு',\n    'பெரும்',\n    'அதை',\n    'பற்றிய',\n    'உன்',\n    'அதிக',\n    'அந்தக்',\n    'பேர்',\n    'இதனால்',\n    'அவை',\n    'அதே',\n    'ஏன்',\n    'முறை',\n    'யார்',\n    'என்பதை',\n    'எல்லாம்',\n    'மட்டுமே',\n    'இங்கே',\n    'அங்கே',\n    'இடம்',\n    'இடத்தில்',\n    'அதில்',\n    'நாம்',\n    'அதற்கு',\n    'எனவே',\n    'பிற',\n    'சிறு',\n    'மற்ற',\n    'விட',\n    'எந்த',\n    'எனவும்',\n    'எனப்படும்',\n    'எனினும்',\n    'அடுத்த',\n    'இதனை',\n    'இதை',\n    'கொள்ள',\n    'இந்தத்',\n    'இதற்கு',\n    'அதனால்',\n    'தவிர',\n    'போல',\n    'வரையில்',\n    'சற்று',\n    'எனக்'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,MACA,QACA,UACA,OACA,MACA,OACA,SACA,SACA,KACA,QACA,SACA,OACA,OACA,OACA,OACA,OACA,KACA,QACA,OACA,UACA,MACA,MACA,QACA,WACA,QACA,OACA,MACA,OACA,OACA,SACA,UACA,SACA,UACA,QACA,YACA,OACA,SACA,OACA,UACA,OACA,MACA,QACA,UACA,MACA,OACA,KACA,OACA,WACA,QACA,OACA,SACA,UACA,QACA,OACA,MACA,MACA,SACA,QACA,QACA,OACA,MACA,QACA,OACA,UACA,QACA,UACA,SACA,QACA,OACA,OACA,UACA,UACA,SACA,UACA,SACA,QACA,MACA,MACA,QACA,UACA,SACA,SACA,MACA,SACA,MACA,OACA,SACA,OACA,SACA,MACA,MACA,MACA,OACA,OACA,SACA,UACA,UACA,QACA,QACA,OACA,WACA,QACA,OACA,SACA,OACA,MACA,OACA,OACA,MACA,OACA,SACA,YACA,UACA,SACA,OACA,MACA,QACA,SACA,SACA,SACA,OACA,MACA,UACA,QACA,OACH"}