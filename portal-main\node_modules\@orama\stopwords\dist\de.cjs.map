{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'aber',\n    'alle',\n    'allem',\n    'allen',\n    'aller',\n    'alles',\n    'als',\n    'also',\n    'am',\n    'an',\n    'ander',\n    'andere',\n    'anderem',\n    'anderen',\n    'anderer',\n    'anderes',\n    'anderm',\n    'andern',\n    'anderr',\n    'anders',\n    'auch',\n    'auf',\n    'aus',\n    'bei',\n    'bin',\n    'bis',\n    'bist',\n    'da',\n    'damit',\n    'dann',\n    'der',\n    'den',\n    'des',\n    'dem',\n    'die',\n    'das',\n    'daß',\n    'derselbe',\n    'derselben',\n    'denselben',\n    'desselben',\n    'demselben',\n    'dieselbe',\n    'dieselben',\n    'dasselbe',\n    'dazu',\n    'dein',\n    'deine',\n    'deinem',\n    'deinen',\n    'deiner',\n    'deines',\n    'denn',\n    'derer',\n    'dessen',\n    'dich',\n    'dir',\n    'du',\n    'dies',\n    'diese',\n    'diesem',\n    'diesen',\n    'dieser',\n    'dieses',\n    'doch',\n    'dort',\n    'durch',\n    'ein',\n    'eine',\n    'einem',\n    'einen',\n    'einer',\n    'eines',\n    'einig',\n    'einige',\n    'einigem',\n    'einigen',\n    'einiger',\n    'einiges',\n    'einmal',\n    'er',\n    'ihn',\n    'ihm',\n    'es',\n    'etwas',\n    'euer',\n    'eure',\n    'eurem',\n    'euren',\n    'eurer',\n    'eures',\n    'für',\n    'gegen',\n    'gewesen',\n    'hab',\n    'habe',\n    'haben',\n    'hat',\n    'hatte',\n    'hatten',\n    'hier',\n    'hin',\n    'hinter',\n    'ich',\n    'mich',\n    'mir',\n    'ihr',\n    'ihre',\n    'ihrem',\n    'ihren',\n    'ihrer',\n    'ihres',\n    'euch',\n    'im',\n    'in',\n    'indem',\n    'ins',\n    'ist',\n    'jede',\n    'jedem',\n    'jeden',\n    'jeder',\n    'jedes',\n    'jene',\n    'jenem',\n    'jenen',\n    'jener',\n    'jenes',\n    'jetzt',\n    'kann',\n    'kein',\n    'keine',\n    'keinem',\n    'keinen',\n    'keiner',\n    'keines',\n    'können',\n    'könnte',\n    'machen',\n    'man',\n    'manche',\n    'manchem',\n    'manchen',\n    'mancher',\n    'manches',\n    'mein',\n    'meine',\n    'meinem',\n    'meinen',\n    'meiner',\n    'meines',\n    'mit',\n    'muss',\n    'musste',\n    'nach',\n    'nicht',\n    'nichts',\n    'noch',\n    'nun',\n    'nur',\n    'ob',\n    'oder',\n    'ohne',\n    'sehr',\n    'sein',\n    'seine',\n    'seinem',\n    'seinen',\n    'seiner',\n    'seines',\n    'selbst',\n    'sich',\n    'sie',\n    'ihnen',\n    'sind',\n    'so',\n    'solche',\n    'solchem',\n    'solchen',\n    'solcher',\n    'solches',\n    'soll',\n    'sollte',\n    'sondern',\n    'sonst',\n    'über',\n    'um',\n    'und',\n    'uns',\n    'unse',\n    'unsem',\n    'unsen',\n    'unser',\n    'unses',\n    'unter',\n    'viel',\n    'vom',\n    'von',\n    'vor',\n    'während',\n    'war',\n    'waren',\n    'warst',\n    'was',\n    'weg',\n    'weil',\n    'weiter',\n    'welche',\n    'welchem',\n    'welchen',\n    'welcher',\n    'welches',\n    'wenn',\n    'werde',\n    'werden',\n    'wie',\n    'wieder',\n    'will',\n    'wir',\n    'wird',\n    'wirst',\n    'wo',\n    'wollen',\n    'wollte',\n    'würde',\n    'würden',\n    'zu',\n    'zum',\n    'zur',\n    'zwar',\n    'zwischen'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,OACA,OACA,QACA,QACA,QACA,QACA,MACA,OACA,KACA,KACA,QACA,SACA,UACA,UACA,UACA,UACA,SACA,SACA,SACA,SACA,OACA,MACA,MACA,MACA,MACA,MACA,OACA,KACA,QACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,SACA,WACA,YACA,YACA,YACA,YACA,WACA,YACA,WACA,OACA,OACA,QACA,SACA,SACA,SACA,SACA,OACA,QACA,SACA,OACA,MACA,KACA,OACA,QACA,SACA,SACA,SACA,SACA,OACA,OACA,QACA,MACA,OACA,QACA,QACA,QACA,QACA,QACA,SACA,UACA,UACA,UACA,UACA,SACA,KACA,MACA,MACA,KACA,QACA,OACA,OACA,QACA,QACA,QACA,QACA,SACA,QACA,UACA,MACA,OACA,QACA,MACA,QACA,SACA,OACA,MACA,SACA,MACA,OACA,MACA,MACA,OACA,QACA,QACA,QACA,QACA,OACA,KACA,KACA,QACA,MACA,MACA,OACA,QACA,QACA,QACA,QACA,OACA,QACA,QACA,QACA,QACA,QACA,OACA,OACA,QACA,SACA,SACA,SACA,SACA,YACA,YACA,SACA,MACA,SACA,UACA,UACA,UACA,UACA,OACA,QACA,SACA,SACA,SACA,SACA,MACA,OACA,SACA,OACA,QACA,SACA,OACA,MACA,MACA,KACA,OACA,OACA,OACA,OACA,QACA,SACA,SACA,SACA,SACA,SACA,OACA,MACA,QACA,OACA,KACA,SACA,UACA,UACA,UACA,UACA,OACA,SACA,UACA,QACA,UACA,KACA,MACA,MACA,OACA,QACA,QACA,QACA,QACA,QACA,OACA,MACA,MACA,MACA,aACA,MACA,QACA,QACA,MACA,MACA,OACA,SACA,SACA,UACA,UACA,UACA,UACA,OACA,QACA,SACA,MACA,SACA,OACA,MACA,OACA,QACA,KACA,SACA,SACA,WACA,YACA,KACA,MACA,MACA,OACA,WACH"}