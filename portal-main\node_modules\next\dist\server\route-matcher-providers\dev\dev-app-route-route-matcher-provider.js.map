{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './helpers/file-reader/file-reader'\nimport type { Normalizer } from '../../normalizers/normalizer'\nimport { AppRouteRouteMatcher } from '../../route-matchers/app-route-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport { DevAppNormalizers } from '../../normalizers/built/app'\nimport {\n  isMetadataRouteFile,\n  isStaticMetadataRoute,\n} from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport path from '../../../shared/lib/isomorphic/path'\n\nexport class DevAppRouteRouteMatcherProvider extends FileCacheRouteMatcherProvider<AppRouteRouteMatcher> {\n  private readonly normalizers: {\n    page: Normalizer\n    pathname: Normalizer\n    bundlePath: Normalizer\n  }\n  private readonly appDir: string\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    reader: FileReader\n  ) {\n    super(appDir, reader)\n\n    this.appDir = appDir\n    this.normalizers = new DevAppNormalizers(appDir, extensions)\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<AppRouteRouteMatcher>> {\n    const matchers: Array<AppRouteRouteMatcher> = []\n    for (const filename of files) {\n      const page = this.normalizers.page.normalize(filename)\n\n      // If the file isn't a match for this matcher, then skip it.\n      if (!isAppRouteRoute(page)) continue\n\n      // Validate that this is not an ignored page.\n      if (page.includes('/_')) continue\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n      const ext = path.extname(filename).slice(1)\n      const isEntryMetadataRouteFile = isMetadataRouteFile(\n        filename.replace(this.appDir, ''),\n        [ext],\n        true\n      )\n\n      if (isEntryMetadataRouteFile && !isStaticMetadataRoute(page)) {\n        // Matching dynamic metadata routes.\n        // Add 2 possibilities for both single and multiple routes:\n        {\n          // single:\n          // /sitemap.ts -> /sitemap.xml/route\n          // /icon.ts -> /icon/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/route.ts\n          // icon.ts -> icon/route.ts\n          const metadataPage = normalizeMetadataPageToRoute(page, false)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, false)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            false\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n        {\n          // multiple:\n          // /sitemap.ts -> /sitemap/[__metadata_id__]/route\n          // /icon.ts -> /icon/[__metadata_id__]/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/[__metadata_id__].ts\n          // icon.ts -> icon/[__metadata_id__].ts\n          const metadataPage = normalizeMetadataPageToRoute(page, true)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, true)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            true\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n      } else {\n        // Normal app routes and static metadata routes.\n        matchers.push(\n          new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page,\n            pathname,\n            bundlePath,\n            filename,\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["DevAppRouteRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "DevAppNormalizers", "transform", "files", "matchers", "filename", "page", "normalize", "isAppRouteRoute", "includes", "pathname", "bundlePath", "ext", "path", "extname", "slice", "isEntryMetadataRouteFile", "isMetadataRouteFile", "replace", "isStaticMetadataRoute", "metadataPage", "normalizeMetadataPageToRoute", "metadataPathname", "metadataBundlePath", "matcher", "AppRouteRouteMatcher", "kind", "RouteKind", "APP_ROUTE", "push"], "mappings": ";;;;+BAcaA;;;eAAAA;;;sCAZwB;2BACX;+CACoB;iCACd;qBACE;iCAI3B;kCACsC;6DAC5B;;;;;;AAEV,MAAMA,wCAAwCC,4DAA6B;IAQhFC,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACF,MAAM,GAAGA;QACd,IAAI,CAACG,WAAW,GAAG,IAAIC,sBAAiB,CAACJ,QAAQC;IACnD;IAEA,MAAgBI,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMC,YAAYF,MAAO;YAC5B,MAAMG,OAAO,IAAI,CAACN,WAAW,CAACM,IAAI,CAACC,SAAS,CAACF;YAE7C,4DAA4D;YAC5D,IAAI,CAACG,IAAAA,gCAAe,EAACF,OAAO;YAE5B,6CAA6C;YAC7C,IAAIA,KAAKG,QAAQ,CAAC,OAAO;YAEzB,MAAMC,WAAW,IAAI,CAACV,WAAW,CAACU,QAAQ,CAACH,SAAS,CAACF;YACrD,MAAMM,aAAa,IAAI,CAACX,WAAW,CAACW,UAAU,CAACJ,SAAS,CAACF;YACzD,MAAMO,MAAMC,aAAI,CAACC,OAAO,CAACT,UAAUU,KAAK,CAAC;YACzC,MAAMC,2BAA2BC,IAAAA,oCAAmB,EAClDZ,SAASa,OAAO,CAAC,IAAI,CAACrB,MAAM,EAAE,KAC9B;gBAACe;aAAI,EACL;YAGF,IAAII,4BAA4B,CAACG,IAAAA,sCAAqB,EAACb,OAAO;gBAC5D,oCAAoC;gBACpC,2DAA2D;gBAC3D;oBACE,UAAU;oBACV,oCAAoC;oBACpC,0BAA0B;oBAC1B,+CAA+C;oBAC/C,qCAAqC;oBACrC,2BAA2B;oBAC3B,MAAMc,eAAeC,IAAAA,8CAA4B,EAACf,MAAM;oBACxD,MAAMgB,mBAAmBD,IAAAA,8CAA4B,EAACX,UAAU;oBAChE,MAAMa,qBAAqBF,IAAAA,8CAA4B,EACrDV,YACA;oBAGF,MAAMa,UAAU,IAAIC,0CAAoB,CAAC;wBACvCC,MAAMC,oBAAS,CAACC,SAAS;wBACzBtB,MAAMc;wBACNV,UAAUY;wBACVX,YAAYY;wBACZlB;oBACF;oBACAD,SAASyB,IAAI,CAACL;gBAChB;gBACA;oBACE,YAAY;oBACZ,kDAAkD;oBAClD,4CAA4C;oBAC5C,+CAA+C;oBAC/C,iDAAiD;oBACjD,uCAAuC;oBACvC,MAAMJ,eAAeC,IAAAA,8CAA4B,EAACf,MAAM;oBACxD,MAAMgB,mBAAmBD,IAAAA,8CAA4B,EAACX,UAAU;oBAChE,MAAMa,qBAAqBF,IAAAA,8CAA4B,EACrDV,YACA;oBAGF,MAAMa,UAAU,IAAIC,0CAAoB,CAAC;wBACvCC,MAAMC,oBAAS,CAACC,SAAS;wBACzBtB,MAAMc;wBACNV,UAAUY;wBACVX,YAAYY;wBACZlB;oBACF;oBACAD,SAASyB,IAAI,CAACL;gBAChB;YACF,OAAO;gBACL,gDAAgD;gBAChDpB,SAASyB,IAAI,CACX,IAAIJ,0CAAoB,CAAC;oBACvBC,MAAMC,oBAAS,CAACC,SAAS;oBACzBtB;oBACAI;oBACAC;oBACAN;gBACF;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}