{"version": 3, "file": "documents-store.js", "sourceRoot": "", "sources": ["../../../src/components/documents-store.ts"], "names": [], "mappings": "AAEA,OAAO,EAIL,qBAAqB,EACtB,MAAM,iCAAiC,CAAA;AAQxC,MAAM,UAAU,MAAM,CAAqB,CAAI,EAAE,2BAAoD;IACnG,OAAO;QACL,2BAA2B;QAC3B,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,CAAC;KACT,CAAA;AACH,CAAC;AAED,MAAM,UAAU,GAAG,CACjB,KAAqB,EACrB,EAAc;IAEd,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAA;IAE/E,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;AAC/B,CAAC;AAED,MAAM,UAAU,WAAW,CACzB,KAAqB,EACrB,GAAiB;IAEjB,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAA;IAC5B,MAAM,KAAK,GAAmC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAA;IAE/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACnF,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,UAAU,MAAM,CACpB,KAAqB;IAErB,OAAO,KAAK,CAAC,IAAI,CAAA;AACnB,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,KAAqB,EAAE,EAAc,EAAE,UAA8B,EAAE,GAAgB;IAC3G,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC;QAClD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAA;IAC5B,KAAK,CAAC,KAAK,EAAE,CAAA;IAEb,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,KAAqB,EAAE,EAAc;IAC1D,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAA;IAE/E,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC;QAClD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC7B,KAAK,CAAC,KAAK,EAAE,CAAA;IAEb,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,KAAqB;IACzC,OAAO,KAAK,CAAC,KAAK,CAAA;AACpB,CAAC;AAED,MAAM,UAAU,IAAI,CAAc,2BAAoD,EAAE,GAAM;IAC5F,MAAM,WAAW,GAAG,GAAqB,CAAA;IAEzC,OAAO;QACL,IAAI,EAAE,WAAW,CAAC,IAAI;QACtB,KAAK,EAAE,WAAW,CAAC,KAAK;QACxB,2BAA2B;KAC5B,CAAA;AACH,CAAC;AAED,MAAM,UAAU,IAAI,CAAc,KAAqB;IACrD,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,KAAK,EAAE,KAAK,CAAC,KAAK;KACd,CAAA;AACR,CAAC;AAED,MAAM,UAAU,oBAAoB;IAClC,OAAO;QACL,MAAM;QACN,GAAG;QACH,WAAW;QACX,MAAM;QACN,KAAK;QACL,MAAM;QACN,KAAK;QACL,IAAI;QACJ,IAAI;KACL,CAAA;AACH,CAAC"}