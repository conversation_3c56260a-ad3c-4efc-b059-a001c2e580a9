{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'acea',\n    'aceasta',\n    'această',\n    'aceea',\n    'acei',\n    'aceia',\n    'acel',\n    'acela',\n    'acele',\n    'acelea',\n    'acest',\n    'acesta',\n    'aceste',\n    'acestea',\n    'aceşti',\n    'aceştia',\n    'acolo',\n    'acord',\n    'acum',\n    'ai',\n    'aia',\n    'aibă',\n    'aici',\n    'al',\n    'ale',\n    'alea',\n    'altceva',\n    'altcineva',\n    'am',\n    'ar',\n    'are',\n    'asemenea',\n    'asta',\n    'astea',\n    'astăzi',\n    'asupra',\n    'au',\n    'avea',\n    'avem',\n    'aveţi',\n    'azi',\n    'aş',\n    'aşadar',\n    'aţi',\n    'bine',\n    'bucur',\n    'bună',\n    'ca',\n    'care',\n    'caut',\n    'ce',\n    'cel',\n    'ceva',\n    'chiar',\n    'cinci',\n    'cine',\n    'cineva',\n    'contra',\n    'cu',\n    'cum',\n    'cumva',\n    'curând',\n    'curînd',\n    'când',\n    'cât',\n    'câte',\n    'câtva',\n    'câţi',\n    'cînd',\n    'cît',\n    'cîte',\n    'cîtva',\n    'cîţi',\n    'că',\n    'căci',\n    'cărei',\n    'căror',\n    'cărui',\n    'către',\n    'da',\n    'dacă',\n    'dar',\n    'datorită',\n    'dată',\n    'dau',\n    'de',\n    'deci',\n    'deja',\n    'deoarece',\n    'departe',\n    'deşi',\n    'din',\n    'dinaintea',\n    'dintr-',\n    'dintre',\n    'doi',\n    'doilea',\n    'două',\n    'drept',\n    'după',\n    'dă',\n    'ea',\n    'ei',\n    'el',\n    'ele',\n    'eram',\n    'este',\n    'eu',\n    'eşti',\n    'face',\n    'fata',\n    'fi',\n    'fie',\n    'fiecare',\n    'fii',\n    'fim',\n    'fiu',\n    'fiţi',\n    'frumos',\n    'fără',\n    'graţie',\n    'halbă',\n    'iar',\n    'ieri',\n    'la',\n    'le',\n    'li',\n    'lor',\n    'lui',\n    'lângă',\n    'lîngă',\n    'mai',\n    'mea',\n    'mei',\n    'mele',\n    'mereu',\n    'meu',\n    'mi',\n    'mie',\n    'mine',\n    'mult',\n    'multă',\n    'mulţi',\n    'mulţumesc',\n    'mâine',\n    'mîine',\n    'mă',\n    'ne',\n    'nevoie',\n    'nici',\n    'nicăieri',\n    'nimeni',\n    'nimeri',\n    'nimic',\n    'nişte',\n    'noastre',\n    'noastră',\n    'noi',\n    'noroc',\n    'nostru',\n    'nouă',\n    'noştri',\n    'nu',\n    'opt',\n    'ori',\n    'oricare',\n    'orice',\n    'oricine',\n    'oricum',\n    'oricând',\n    'oricât',\n    'oricînd',\n    'oricît',\n    'oriunde',\n    'patra',\n    'patru',\n    'patrulea',\n    'pe',\n    'pentru',\n    'peste',\n    'pic',\n    'poate',\n    'pot',\n    'prea',\n    'prima',\n    'primul',\n    'prin',\n    'printr-',\n    'puţin',\n    'puţina',\n    'puţină',\n    'până',\n    'pînă',\n    'rog',\n    'sa',\n    'sale',\n    'sau',\n    'se',\n    'spate',\n    'spre',\n    'sub',\n    'sunt',\n    'suntem',\n    'sunteţi',\n    'sută',\n    'sînt',\n    'sîntem',\n    'sînteţi',\n    'să',\n    'săi',\n    'său',\n    'ta',\n    'tale',\n    'te',\n    'timp',\n    'tine',\n    'toate',\n    'toată',\n    'tot',\n    'totuşi',\n    'toţi',\n    'trei',\n    'treia',\n    'treilea',\n    'tu',\n    'tăi',\n    'tău',\n    'un',\n    'una',\n    'unde',\n    'undeva',\n    'unei',\n    'uneia',\n    'unele',\n    'uneori',\n    'unii',\n    'unor',\n    'unora',\n    'unu',\n    'unui',\n    'unuia',\n    'unul',\n    'vi',\n    'voastre',\n    'voastră',\n    'voi',\n    'vostru',\n    'vouă',\n    'voştri',\n    'vreme',\n    'vreo',\n    'vreun',\n    'vă',\n    'zece',\n    'zero',\n    'zi',\n    'zice',\n    'îi',\n    'îl',\n    'îmi',\n    'împotriva',\n    'în',\n    'înainte',\n    'înaintea',\n    'încotro',\n    'încât',\n    'încît',\n    'între',\n    'întrucât',\n    'întrucît',\n    'îţi',\n    'ăla',\n    'ălea',\n    'ăsta',\n    'ăstea',\n    'ăştia',\n    'şapte',\n    'şase',\n    'şi',\n    'ştiu',\n    'ţi',\n    'ţie'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,OACA,UACA,UACA,QACA,OACA,QACA,OACA,QACA,QACA,SACA,QACA,SACA,SACA,UACA,SACA,UACA,QACA,QACA,OACA,KACA,MACA,OACA,OACA,KACA,MACA,OACA,UACA,YACA,KACA,KACA,MACA,WACA,OACA,QACA,SACA,SACA,KACA,OACA,OACA,QACA,MACA,KACA,SACA,MACA,OACA,QACA,OACA,KACA,OACA,OACA,KACA,MACA,OACA,QACA,QACA,OACA,SACA,SACA,KACA,MACA,QACA,YACA,YACA,UACA,SACA,UACA,WACA,UACA,UACA,SACA,UACA,WACA,UACA,KACA,OACA,QACA,QACA,QACA,QACA,KACA,OACA,MACA,WACA,OACA,MACA,KACA,OACA,OACA,WACA,UACA,OACA,MACA,YACA,SACA,SACA,MACA,SACA,OACA,QACA,OACA,KACA,KACA,KACA,KACA,MACA,OACA,OACA,KACA,OACA,OACA,OACA,KACA,MACA,UACA,MACA,MACA,MACA,OACA,SACA,OACA,SACA,QACA,MACA,OACA,KACA,KACA,KACA,MACA,MACA,WACA,WACA,MACA,MACA,MACA,OACA,QACA,MACA,KACA,MACA,OACA,OACA,QACA,QACA,YACA,WACA,WACA,KACA,KACA,SACA,OACA,WACA,SACA,SACA,QACA,QACA,UACA,UACA,MACA,QACA,SACA,OACA,SACA,KACA,MACA,MACA,UACA,QACA,UACA,SACA,aACA,YACA,aACA,YACA,UACA,QACA,QACA,WACA,KACA,SACA,QACA,MACA,QACA,MACA,OACA,QACA,SACA,OACA,UACA,QACA,SACA,SACA,UACA,UACA,MACA,KACA,OACA,MACA,KACA,QACA,OACA,MACA,OACA,SACA,UACA,OACA,UACA,YACA,aACA,KACA,MACA,MACA,KACA,OACA,KACA,OACA,OACA,QACA,QACA,MACA,SACA,OACA,OACA,QACA,UACA,KACA,MACA,MACA,KACA,MACA,OACA,SACA,OACA,QACA,QACA,SACA,OACA,OACA,QACA,MACA,OACA,QACA,OACA,KACA,UACA,UACA,MACA,SACA,OACA,SACA,QACA,OACA,QACA,KACA,OACA,OACA,KACA,OACA,QACA,QACA,SACA,eACA,QACA,aACA,cACA,aACA,cACA,cACA,WACA,iBACA,iBACA,SACA,MACA,OACA,OACA,QACA,QACA,QACA,OACA,KACA,OACA,KACA,MACH,AAAC"}