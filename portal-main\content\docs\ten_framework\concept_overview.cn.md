---
title: 概述
---

![TEN framework](https://ten-framework-assets.s3.amazonaws.com/doc-assets/ten_framework.png)

## TEN runtime （TEN Runtime）

TEN runtime 是专为 TEN framework 设计的应用程序运行平台。它提供了一种灵活的架构，可以无缝集成用多种编程语言开发的扩展，并处理各种类型的数据流。通过对扩展的生命周期、数据流向和线程进行统一管理，TEN runtime 使开发者能够高效构建多样化的应用和服务。

## 应用（App）

TEN framework 中的应用可以作为独立的进程运行，也可以作为现有进程中的线程运行。 这种灵活性允许根据应用程序的需求选择不同的部署选项。

## 扩展组（Extension Group）

扩展组是一种为执行指定特定线程的机制。 给定组中的所有扩展都在同一线程上执行，从而确保一致和同步的处理。

## 扩展（Extension）

扩展是 TEN framework 的基本构建块。 开发人员可以使用各种编程语言创建扩展，并将它们组合起来以构建不同的应用程序和场景。 该框架的设计支持跨语言协作，使得不同语言编写的扩展能够在同一应用程序或服务中无缝地协同工作。

例如，由于 C++ 在处理音频和视频数据方面具有性能优势，因此开发人员可能会使用 C++ 创建用于实时通信 (RTC) 的扩展，同时使用 Python 开发 AI 扩展，以利用其强大的库进行数据分析和机器学习。 然后，可以将这些扩展集成到单个应用程序中，从而利用每种语言的优势。

## 图（Graph）

在 TEN framework 中，“图” (Graph) 描绘了扩展之间的数据流转过程。它编排了数据在不同扩展之间的流动方式，明确了数据流的参与者以及它们之间的交互关系。例如，可以将语音转文本（STT）扩展的输出定向到大语言模型（LLM）扩展进行进一步处理。

TEN framework 支持扩展之间的四种主要类型的数据流：

- 命令
- 数据
- 视频帧
- 音频帧

通过在图中定义这些数据流，开发者可以创建扩展之间的通信，以及单向的数据传输通道，这在处理音频和视频数据时尤其有用。

## 应用、组和扩展之间的关系

- **应用**：

  一个应用可以执行多个图，这些图可以是静态预定义的，也可以是动态组装的。

- **图**：

  图由多个协同工作的扩展构成，旨在创建一个有意义的场景。每个图的实例在应用程序内作为一个独立的会话运行。

- **扩展组**：

  扩展组的概念类似于线程。 以相同语言编写且位于同一扩展组中的扩展在运行时在同一线程上运行。 开发人员不需要直接管理线程； 他们只需"声明"每个扩展所属的组即可。

- **扩展**：

  框架中的每个扩展都分配有一个唯一的 ID，其结构为：`app-uri/graph-name/group-name/extension-name`

![概念的层次关系](https://ten-framework-assets.s3.amazonaws.com/doc-assets/hierarchical_relationship_of_concepts.png)

## TEN 云商店

![TEN 云商店](https://ten-framework-assets.s3.amazonaws.com/doc-assets/ten_cloud_store.png)

TEN 云商店的功能类似于 Google Play 商店或 Apple 的 App Store，为扩展提供了一个市场。 开发人员可以共享他们的扩展或下载其他人创建的扩展。 这些扩展可以集成到 TEN 应用程序中，从而促进开发并扩展功能。

## TEN 管理器

TEN 管理器是一种简化扩展管理的工具。 它处理诸如上传、共享和安装扩展之类的任务，自动管理它们及其环境之间的依赖关系。 这使得扩展的安装和发布方便高效，从而简化了在 TEN framework 内的开发过程。
