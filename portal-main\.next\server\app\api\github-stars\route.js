/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/github-stars/route";
exports.ids = ["app/api/github-stars/route"];
exports.modules = {

/***/ "(rsc)/./app/api/github-stars/route.ts":
/*!***************************************!*\
  !*** ./app/api/github-stars/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// In-memory cache for last successful responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const repo = searchParams.get('repo');\n    if (!repo) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Repository parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    const cacheKey = repo;\n    const now = Date.now();\n    // Check if we have a recent cached response\n    const cached = cache.get(cacheKey);\n    if (cached && now - cached.timestamp < CACHE_DURATION) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            stargazers_count: cached.stargazers_count,\n            cached_at: new Date(cached.timestamp).toISOString(),\n            from_cache: true\n        });\n    }\n    try {\n        // Prepare headers for authenticated requests\n        const headers = {\n            'Accept': 'application/vnd.github+json',\n            'X-GitHub-Api-Version': '2022-11-28',\n            'User-Agent': 'TEN-Portal-Website'\n        };\n        // Add authentication if GitHub token is available (server-side only)\n        const githubToken = process.env.GITHUB_TOKEN;\n        if (githubToken) {\n            headers['Authorization'] = `Bearer ${githubToken}`;\n        }\n        const response = await fetch(`https://api.github.com/repos/${repo}`, {\n            headers\n        });\n        if (response.ok) {\n            const data = await response.json();\n            const starCount = data.stargazers_count;\n            // Cache the successful response\n            cache.set(cacheKey, {\n                stargazers_count: starCount,\n                timestamp: now\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                stargazers_count: starCount,\n                cached_at: new Date().toISOString(),\n                from_cache: false\n            });\n        } else if (response.status === 403) {\n            // Handle rate limit exceeded\n            const rateLimitReset = response.headers.get('X-RateLimit-Reset');\n            const resetTime = rateLimitReset ? new Date(parseInt(rateLimitReset) * 1000) : null;\n            console.warn('GitHub API rate limit exceeded.', resetTime ? `Resets at: ${resetTime.toLocaleString()}` : '');\n            // Return last successful cached data or fallback\n            const fallbackCount = cached?.stargazers_count || 7135;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                stargazers_count: fallbackCount,\n                error: 'Rate limit exceeded',\n                reset_time: resetTime?.toISOString(),\n                from_cache: !!cached\n            }, {\n                status: 429\n            });\n        } else {\n            console.error('GitHub API request failed:', response.status, response.statusText);\n            // Return last successful cached data or fallback\n            const fallbackCount = cached?.stargazers_count || 7135;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch repository data',\n                stargazers_count: fallbackCount,\n                from_cache: !!cached\n            }, {\n                status: response.status\n            });\n        }\n    } catch (error) {\n        console.error('Failed to fetch star count:', error);\n        // Return last successful cached data or fallback\n        const fallbackCount = cached?.stargazers_count || 7135;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            stargazers_count: fallbackCount,\n            from_cache: !!cached\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/github-stars/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DeepInsight_Downloads_portal_main_1_portal_main_app_api_github_stars_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/github-stars/route.ts */ \"(rsc)/./app/api/github-stars/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/github-stars/route\",\n        pathname: \"/api/github-stars\",\n        filename: \"route\",\n        bundlePath: \"app/api/github-stars/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\api\\\\github-stars\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DeepInsight_Downloads_portal_main_1_portal_main_app_api_github_stars_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();