{"version": 3, "sources": ["../../../src/shared/lib/amp.ts"], "sourcesContent": ["import React from 'react'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\n\nexport function useAmp(): boolean {\n  // Don't assign the context value to a variable to save bytes\n  return isInAmpMode(React.useContext(AmpStateContext))\n}\n"], "names": ["useAmp", "isInAmpMode", "React", "useContext", "AmpStateContext"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;;gEAJE;yCACc;yBACJ;AAErB,SAASA;IACd,6DAA6D;IAC7D,OAAOC,IAAAA,oBAAW,EAACC,cAAK,CAACC,UAAU,CAACC,wCAAe;AACrD"}