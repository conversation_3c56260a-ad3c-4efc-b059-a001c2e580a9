"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_orama-cloud-USLSOSXS_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstS0FPRU1DVEkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxmdW1hZG9jcy1jb3JlXFxkaXN0XFxjaHVuay1LQU9FTUNUSS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvcmVtb3ZlLXVuZGVmaW5lZC50c1xuZnVuY3Rpb24gcmVtb3ZlVW5kZWZpbmVkKHZhbHVlLCBkZWVwID0gZmFsc2UpIHtcbiAgY29uc3Qgb2JqID0gdmFsdWU7XG4gIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKG9iaikpIHtcbiAgICBpZiAob2JqW2tleV0gPT09IHZvaWQgMCkgZGVsZXRlIG9ialtrZXldO1xuICAgIGlmIChkZWVwICYmIHR5cGVvZiBvYmpba2V5XSA9PT0gXCJvYmplY3RcIiAmJiBvYmpba2V5XSAhPT0gbnVsbCkge1xuICAgICAgcmVtb3ZlVW5kZWZpbmVkKG9ialtrZXldLCBkZWVwKTtcbiAgICB9IGVsc2UgaWYgKGRlZXAgJiYgQXJyYXkuaXNBcnJheShvYmpba2V5XSkpIHtcbiAgICAgIG9ialtrZXldLmZvckVhY2goKHYpID0+IHJlbW92ZVVuZGVmaW5lZCh2LCBkZWVwKSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHtcbiAgcmVtb3ZlVW5kZWZpbmVkXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, tag, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {} } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js\n"));

/***/ })

}]);