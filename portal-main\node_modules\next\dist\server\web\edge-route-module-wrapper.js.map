{"version": 3, "sources": ["../../../src/server/web/edge-route-module-wrapper.ts"], "sourcesContent": ["import type { NextRequest } from './spec-extension/request'\nimport type {\n  AppRouteRouteHandlerContext,\n  AppRouteRouteModule,\n} from '../route-modules/app-route/module'\n\nimport './globals'\n\nimport { adapter, type AdapterOptions } from './adapter'\nimport { IncrementalCache } from '../lib/incremental-cache'\nimport { RouteMatcher } from '../route-matchers/route-matcher'\nimport type { NextFetchEvent } from './spec-extension/fetch-event'\nimport { internal_getCurrentFunctionWaitUntil } from './internal-edge-wait-until'\nimport { getUtils } from '../server-utils'\nimport { searchParamsToUrlQuery } from '../../shared/lib/router/utils/querystring'\nimport { CloseController, trackStreamConsumed } from './web-on-close'\nimport { getEdgePreviewProps } from './get-edge-preview-props'\nimport type { NextConfigComplete } from '../config-shared'\n\nexport interface WrapOptions {\n  nextConfig: NextConfigComplete\n}\n\n/**\n * EdgeRouteModuleWrapper is a wrapper around a route module.\n *\n * Note that this class should only be used in the edge runtime.\n */\nexport class EdgeRouteModuleWrapper {\n  private readonly matcher: RouteMatcher\n\n  /**\n   * The constructor is wrapped with private to ensure that it can only be\n   * constructed by the static wrap method.\n   *\n   * @param routeModule the route module to wrap\n   */\n  private constructor(\n    private readonly routeModule: AppRouteRouteModule,\n    private readonly nextConfig: NextConfigComplete\n  ) {\n    // TODO: (wyattjoh) possibly allow the module to define it's own matcher\n    this.matcher = new RouteMatcher(routeModule.definition)\n  }\n\n  /**\n   * This will wrap a module with the EdgeModuleWrapper and return a function\n   * that can be used as a handler for the edge runtime.\n   *\n   * @param module the module to wrap\n   * @param options any options that should be passed to the adapter and\n   *                override the ones passed from the runtime\n   * @returns a function that can be used as a handler for the edge runtime\n   */\n  public static wrap(routeModule: AppRouteRouteModule, options: WrapOptions) {\n    // Create the module wrapper.\n    const wrapper = new EdgeRouteModuleWrapper(routeModule, options.nextConfig)\n\n    // Return the wrapping function.\n    return (opts: AdapterOptions) => {\n      return adapter({\n        ...opts,\n        IncrementalCache,\n        // Bind the handler method to the wrapper so it still has context.\n        handler: wrapper.handler.bind(wrapper),\n      })\n    }\n  }\n\n  private async handler(\n    request: NextRequest,\n    evt: NextFetchEvent\n  ): Promise<Response> {\n    const utils = getUtils({\n      pageIsDynamic: this.matcher.isDynamic,\n      page: this.matcher.definition.pathname,\n      basePath: request.nextUrl.basePath,\n      // We don't need the `handleRewrite` util, so can just pass an empty object\n      rewrites: {},\n      // only used for rewrites, so setting an arbitrary default value here\n      caseSensitive: false,\n    })\n\n    const { params } = utils.normalizeDynamicRouteParams(\n      searchParamsToUrlQuery(request.nextUrl.searchParams),\n      false\n    )\n\n    const waitUntil = evt.waitUntil.bind(evt)\n    const closeController = new CloseController()\n\n    const previewProps = getEdgePreviewProps()\n\n    // Create the context for the handler. This contains the params from the\n    // match (if any).\n    const context: AppRouteRouteHandlerContext = {\n      params,\n      prerenderManifest: {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        preview: previewProps,\n        notFoundRoutes: [],\n      },\n      renderOpts: {\n        supportsDynamicResponse: true,\n        waitUntil,\n        onClose: closeController.onClose.bind(closeController),\n        onAfterTaskError: undefined,\n        experimental: {\n          dynamicIO: !!process.env.__NEXT_DYNAMIC_IO,\n          authInterrupts: !!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS,\n        },\n        cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      },\n      sharedContext: {\n        buildId: '', // TODO: Populate this properly.\n      },\n    }\n\n    // Get the response from the handler.\n    let res = await this.routeModule.handle(request, context)\n\n    const waitUntilPromises = [internal_getCurrentFunctionWaitUntil()]\n    if (context.renderOpts.pendingWaitUntil) {\n      waitUntilPromises.push(context.renderOpts.pendingWaitUntil)\n    }\n    evt.waitUntil(Promise.all(waitUntilPromises))\n\n    if (!res.body) {\n      // we can delay running it until a bit later --\n      // if it's needed, we'll have a `waitUntil` lock anyway.\n      setTimeout(() => closeController.dispatchClose(), 0)\n    } else {\n      // NOTE: if this is a streaming response, onClose may be called later,\n      // so we can't rely on `closeController.listeners` -- it might be 0 at this point.\n      const trackedBody = trackStreamConsumed(res.body, () =>\n        closeController.dispatchClose()\n      )\n      res = new Response(trackedBody, {\n        status: res.status,\n        statusText: res.statusText,\n        headers: res.headers,\n      })\n    }\n\n    return res\n  }\n}\n"], "names": ["EdgeRouteModuleWrapper", "routeModule", "nextConfig", "matcher", "RouteMatcher", "definition", "wrap", "options", "wrapper", "opts", "adapter", "IncrementalCache", "handler", "bind", "request", "evt", "utils", "getUtils", "pageIsDynamic", "isDynamic", "page", "pathname", "basePath", "nextUrl", "rewrites", "caseSensitive", "params", "normalizeDynamicRouteParams", "searchParamsToUrlQuery", "searchParams", "waitUntil", "closeController", "CloseController", "previewProps", "getEdgePreviewProps", "context", "prerenderManifest", "version", "routes", "dynamicRoutes", "preview", "notFoundRoutes", "renderOpts", "supportsDynamicResponse", "onClose", "onAfterTaskError", "undefined", "experimental", "dynamicIO", "process", "env", "__NEXT_DYNAMIC_IO", "authInterrupts", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "cacheLifeProfiles", "cacheLife", "sharedContext", "buildId", "res", "handle", "waitUntilPromises", "internal_getCurrentFunctionWaitUntil", "pendingWaitUntil", "push", "Promise", "all", "body", "setTimeout", "dispatchClose", "trackedBody", "trackStreamConsumed", "Response", "status", "statusText", "headers"], "mappings": ";;;;+BA4BaA;;;eAAAA;;;QAtBN;yBAEsC;kCACZ;8BACJ;uCAEwB;6BAC5B;6BACc;4BACc;qCACjB;AAY7B,MAAMA;IAGX;;;;;GAKC,GACD,YACE,AAAiBC,WAAgC,EACjD,AAAiBC,UAA8B,CAC/C;aAFiBD,cAAAA;aACAC,aAAAA;QAEjB,wEAAwE;QACxE,IAAI,CAACC,OAAO,GAAG,IAAIC,0BAAY,CAACH,YAAYI,UAAU;IACxD;IAEA;;;;;;;;GAQC,GACD,OAAcC,KAAKL,WAAgC,EAAEM,OAAoB,EAAE;QACzE,6BAA6B;QAC7B,MAAMC,UAAU,IAAIR,uBAAuBC,aAAaM,QAAQL,UAAU;QAE1E,gCAAgC;QAChC,OAAO,CAACO;YACN,OAAOC,IAAAA,gBAAO,EAAC;gBACb,GAAGD,IAAI;gBACPE,kBAAAA,kCAAgB;gBAChB,kEAAkE;gBAClEC,SAASJ,QAAQI,OAAO,CAACC,IAAI,CAACL;YAChC;QACF;IACF;IAEA,MAAcI,QACZE,OAAoB,EACpBC,GAAmB,EACA;QACnB,MAAMC,QAAQC,IAAAA,qBAAQ,EAAC;YACrBC,eAAe,IAAI,CAACf,OAAO,CAACgB,SAAS;YACrCC,MAAM,IAAI,CAACjB,OAAO,CAACE,UAAU,CAACgB,QAAQ;YACtCC,UAAUR,QAAQS,OAAO,CAACD,QAAQ;YAClC,2EAA2E;YAC3EE,UAAU,CAAC;YACX,qEAAqE;YACrEC,eAAe;QACjB;QAEA,MAAM,EAAEC,MAAM,EAAE,GAAGV,MAAMW,2BAA2B,CAClDC,IAAAA,mCAAsB,EAACd,QAAQS,OAAO,CAACM,YAAY,GACnD;QAGF,MAAMC,YAAYf,IAAIe,SAAS,CAACjB,IAAI,CAACE;QACrC,MAAMgB,kBAAkB,IAAIC,2BAAe;QAE3C,MAAMC,eAAeC,IAAAA,wCAAmB;QAExC,wEAAwE;QACxE,kBAAkB;QAClB,MAAMC,UAAuC;YAC3CT;YACAU,mBAAmB;gBACjBC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAASP;gBACTQ,gBAAgB,EAAE;YACpB;YACAC,YAAY;gBACVC,yBAAyB;gBACzBb;gBACAc,SAASb,gBAAgBa,OAAO,CAAC/B,IAAI,CAACkB;gBACtCc,kBAAkBC;gBAClBC,cAAc;oBACZC,WAAW,CAAC,CAACC,QAAQC,GAAG,CAACC,iBAAiB;oBAC1CC,gBAAgB,CAAC,CAACH,QAAQC,GAAG,CAACG,mCAAmC;gBACnE;gBACAC,mBAAmB,IAAI,CAACpD,UAAU,CAAC6C,YAAY,CAACQ,SAAS;YAC3D;YACAC,eAAe;gBACbC,SAAS;YACX;QACF;QAEA,qCAAqC;QACrC,IAAIC,MAAM,MAAM,IAAI,CAACzD,WAAW,CAAC0D,MAAM,CAAC7C,SAASqB;QAEjD,MAAMyB,oBAAoB;YAACC,IAAAA,2DAAoC;SAAG;QAClE,IAAI1B,QAAQO,UAAU,CAACoB,gBAAgB,EAAE;YACvCF,kBAAkBG,IAAI,CAAC5B,QAAQO,UAAU,CAACoB,gBAAgB;QAC5D;QACA/C,IAAIe,SAAS,CAACkC,QAAQC,GAAG,CAACL;QAE1B,IAAI,CAACF,IAAIQ,IAAI,EAAE;YACb,+CAA+C;YAC/C,wDAAwD;YACxDC,WAAW,IAAMpC,gBAAgBqC,aAAa,IAAI;QACpD,OAAO;YACL,sEAAsE;YACtE,kFAAkF;YAClF,MAAMC,cAAcC,IAAAA,+BAAmB,EAACZ,IAAIQ,IAAI,EAAE,IAChDnC,gBAAgBqC,aAAa;YAE/BV,MAAM,IAAIa,SAASF,aAAa;gBAC9BG,QAAQd,IAAIc,MAAM;gBAClBC,YAAYf,IAAIe,UAAU;gBAC1BC,SAAShB,IAAIgB,OAAO;YACtB;QACF;QAEA,OAAOhB;IACT;AACF"}