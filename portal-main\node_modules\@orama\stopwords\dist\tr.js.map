{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'acaba',\n    'acep',\n    'adeta',\n    'altmýþ',\n    'altmış',\n    'altý',\n    'altı',\n    'ama',\n    'ancak',\n    'arada',\n    'artýk',\n    'aslında',\n    'aynen',\n    'ayrıca',\n    'az',\n    'bana',\n    'bari',\n    'bazen',\n    'bazý',\n    'bazı',\n    'ba<PERSON>ka',\n    'belki',\n    'ben',\n    'benden',\n    'beni',\n    'benim',\n    'beri',\n    'beþ',\n    'beş',\n    'beţ',\n    'bile',\n    'bin',\n    'bir',\n    'biraz',\n    'biri',\n    'birka<PERSON>',\n    'birkez',\n    'birçok',\n    'birþey',\n    'birþeyi',\n    'bir<PERSON>ey',\n    'birşeyi',\n    'bir<PERSON>ey',\n    'biz',\n    'bizden',\n    'bize',\n    'bizi',\n    'bizim',\n    'bu',\n    'buna',\n    'bunda',\n    'bundan',\n    'bunlar',\n    'bunları',\n    'bunların',\n    'bunu',\n    'bunun',\n    'burada',\n    'böyle',\n    'b<PERSON><PERSON><PERSON>',\n    'büt<PERSON>n',\n    'da',\n    'daha',\n    'dahi',\n    'da<PERSON>',\n    'daima',\n    'dair',\n    'dayanarak',\n    'de',\n    'defa',\n    'deđil',\n    'değil',\n    'diye',\n    'diđer',\n    'diğer',\n    'doksan',\n    'dokuz',\n    'dolayı',\n    'dolayısıyla',\n    'd<PERSON>rt',\n    'edecek',\n    'eden',\n    'ederek',\n    'edilecek',\n    'ediliyor',\n    'edilmesi',\n    'ediyor',\n    'elli',\n    'en',\n    'etmesi',\n    'etti',\n    'ettiği',\n    'ettiğini',\n    'eđer',\n    'eğer',\n    'fakat',\n    'gibi',\n    'göre',\n    'halbuki',\n    'halen',\n    'hangi',\n    'hani',\n    'hariç',\n    'hatta',\n    'hele',\n    'hem',\n    'henüz',\n    'hep',\n    'hepsi',\n    'her',\n    'herhangi',\n    'herkes',\n    'herkesin',\n    'hiç',\n    'hiçbir',\n    'iken',\n    'iki',\n    'ila',\n    'ile',\n    'ilgili',\n    'ilk',\n    'illa',\n    'ise',\n    'itibaren',\n    'itibariyle',\n    'iyi',\n    'iyice',\n    'için',\n    'işte',\n    'iţte',\n    'kadar',\n    'kanýmca',\n    'karşın',\n    'katrilyon',\n    'kendi',\n    'kendilerine',\n    'kendini',\n    'kendisi',\n    'kendisine',\n    'kendisini',\n    'kere',\n    'kez',\n    'keţke',\n    'ki',\n    'kim',\n    'kimden',\n    'kime',\n    'kimi',\n    'kimse',\n    'kýrk',\n    'kýsaca',\n    'kırk',\n    'lakin',\n    'madem',\n    'međer',\n    'milyar',\n    'milyon',\n    'mu',\n    'mü',\n    'mý',\n    'mı',\n    'nasýl',\n    'nasıl',\n    'ne',\n    'neden',\n    'nedenle',\n    'nerde',\n    'nere',\n    'nerede',\n    'nereye',\n    'nitekim',\n    'niye',\n    'niçin',\n    'o',\n    'olan',\n    'olarak',\n    'oldu',\n    'olduklarını',\n    'olduğu',\n    'olduğunu',\n    'olmadı',\n    'olmadığı',\n    'olmak',\n    'olması',\n    'olmayan',\n    'olmaz',\n    'olsa',\n    'olsun',\n    'olup',\n    'olur',\n    'olursa',\n    'oluyor',\n    'on',\n    'ona',\n    'ondan',\n    'onlar',\n    'onlardan',\n    'onlari',\n    'onlarýn',\n    'onları',\n    'onların',\n    'onu',\n    'onun',\n    'otuz',\n    'oysa',\n    'pek',\n    'rağmen',\n    'sadece',\n    'sanki',\n    'sekiz',\n    'seksen',\n    'sen',\n    'senden',\n    'seni',\n    'senin',\n    'siz',\n    'sizden',\n    'sizi',\n    'sizin',\n    'sonra',\n    'tarafından',\n    'trilyon',\n    'tüm',\n    'var',\n    'vardı',\n    've',\n    'veya',\n    'veyahut',\n    'ya',\n    'yahut',\n    'yani',\n    'yapacak',\n    'yapmak',\n    'yaptı',\n    'yaptıkları',\n    'yaptığı',\n    'yaptığını',\n    'yapılan',\n    'yapılması',\n    'yapıyor',\n    'yedi',\n    'yerine',\n    'yetmiþ',\n    'yetmiş',\n    'yetmiţ',\n    'yine',\n    'yirmi',\n    'yoksa',\n    'yüz',\n    'zaten',\n    'çok',\n    'çünkü',\n    'öyle',\n    'üzere',\n    'üç',\n    'þey',\n    'þeyden',\n    'þeyi',\n    'þeyler',\n    'þu',\n    'þuna',\n    'þunda',\n    'þundan',\n    'þunu',\n    'şey',\n    'şeyden',\n    'şeyi',\n    'şeyler',\n    'şu',\n    'şuna',\n    'şunda',\n    'şundan',\n    'şunları',\n    'şunu',\n    'şöyle',\n    'ţayet',\n    'ţimdi',\n    'ţu',\n    'ţöyle'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,QACA,OACA,QACA,eACA,SACA,UACA,OACA,MACA,QACA,QACA,WACA,UACA,QACA,SACA,KACA,OACA,OACA,QACA,UACA,OACA,QACA,QACA,MACA,SACA,OACA,QACA,OACA,SACA,MACA,MACA,OACA,MACA,MACA,QACA,OACA,YACA,SACA,YACA,YACA,aACA,SACA,UACA,SACA,MACA,SACA,OACA,OACA,QACA,KACA,OACA,QACA,SACA,SACA,UACA,WACA,OACA,QACA,SACA,WACA,aACA,cACA,KACA,OACA,OACA,QACA,QACA,OACA,YACA,KACA,OACA,QACA,QACA,OACA,QACA,QACA,SACA,QACA,SACA,cACA,UACA,SACA,OACA,SACA,WACA,WACA,WACA,SACA,OACA,KACA,SACA,OACA,SACA,WACA,OACA,OACA,QACA,OACA,UACA,UACA,QACA,QACA,OACA,WACA,QACA,OACA,MACA,WACA,MACA,QACA,MACA,WACA,SACA,WACA,SACA,YACA,OACA,MACA,MACA,MACA,SACA,MACA,OACA,MACA,WACA,aACA,MACA,QACA,UACA,OACA,OACA,QACA,aACA,SACA,YACA,QACA,cACA,UACA,UACA,YACA,YACA,OACA,MACA,QACA,KACA,MACA,SACA,OACA,OACA,QACA,UACA,YACA,OACA,QACA,QACA,QACA,SACA,SACA,KACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,UACA,QACA,OACA,SACA,SACA,UACA,OACA,WACA,IACA,OACA,SACA,OACA,cACA,SACA,WACA,SACA,WACA,QACA,SACA,UACA,QACA,OACA,QACA,OACA,OACA,SACA,SACA,KACA,MACA,QACA,QACA,WACA,SACA,aACA,SACA,UACA,MACA,OACA,OACA,OACA,MACA,SACA,SACA,QACA,QACA,SACA,MACA,SACA,OACA,QACA,MACA,SACA,OACA,QACA,QACA,aACA,UACA,SACA,MACA,QACA,KACA,OACA,UACA,KACA,QACA,OACA,UACA,SACA,QACA,aACA,UACA,YACA,UACA,YACA,UACA,OACA,SACA,YACA,SACA,SACA,OACA,QACA,QACA,SACA,QACA,SACA,iBACA,UACA,WACA,WACA,SACA,YACA,UACA,YACA,QACA,UACA,WACA,YACA,UACA,MACA,SACA,OACA,SACA,KACA,OACA,QACA,SACA,UACA,OACA,WACA,QACA,QACA,KACA,WACH,AAAC"}