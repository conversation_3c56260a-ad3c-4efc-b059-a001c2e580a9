---
title: 🌟 欢迎使用 TEN
---

欢迎使用 TEN！TEN（Transformative Extensions Network）是一个用于创建对话式 AI 的语音代理框架。

TEN framework 具有以下优势：

1. **原生支持高性能实时多模态交互**

   如果您的 AI 应用涉及复杂的视听场景，TEN 是您的最佳选择。它提供高性能和低延迟，并对各种扩展之间的交互进行了广泛优化，以确保 AI 应用的高效开发。

2. **支持多种编程语言和平台**

   开发者可以使用多种编程语言（如 C++、Go 和 Python，未来还将支持 JavaScript/TypeScript）创建模块化和可重用的扩展。此外，TEN framework 可以在 Windows、Mac、Linux 和移动设备等各种平台上无缝运行。

3. **边缘云集成**

   通过 TEN framework，部署在边缘和云环境中的扩展可以轻松组合，创建多样化的应用场景。对于注重隐私的边缘部署，小型模型可以利用本地计算能力来降低成本和延迟，同时可以集成云端大型模型以实现成本和性能的最佳平衡。

4. **突破模型局限的灵活性**

   TEN framework 允许创建超越单一大型模型局限性的复杂 AI 应用。通过简单的拖放式响应式编程方法，可以轻松构建满足各种需求的代理。TEN 还便于将 AI 与视听工具、数据库、监控系统、RAG 等进行集成。

5. **实时代理状态管理**

   TEN 具备管理实时代理状态的能力，能够实现动态响应并实时调整代理行为。

---

想要看看 TEN 的实际应用？请访问 **[agent.theten.ai](https://agent.theten.ai)** 体验由 TEN framework 驱动的真实 AI 代理。

要开始您的 AI 代理开发之旅，请探索我们专门的 [**TEN Agent**](https://github.com/ten-framework/TEN-Agent) 代码仓库。它包含了帮助您快速高效入门的丰富资源。

如果您能给 TEN Agent 仓库点个星标，我们将不胜感激。您的支持将帮助我们不断成长和进步！🌟
