---
title: Setup Development Environment
---

The easiest way to get started with TEN Agent is to use the provided Docker container. This container includes all the necessary dependencies and configurations to run the agent. You can also customize the agent by creating your own extensions or modifying the existing ones.

While if you want to develop the agent, using suitable development tools can save you lots of time. This guide will contain following tutorials,

- Setup local development environment with VSCode to connect with your Docker container.
- Setup cloud development environment with Github Codespace.
