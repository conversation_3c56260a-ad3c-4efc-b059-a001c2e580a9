{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'ad',\n    'al',\n    'allo',\n    'ai',\n    'agli',\n    'all',\n    'agl',\n    'alla',\n    'alle',\n    'con',\n    'col',\n    'coi',\n    'da',\n    'dal',\n    'dallo',\n    'dai',\n    'dagli',\n    'dall',\n    'dagl',\n    'dalla',\n    'dalle',\n    'di',\n    'del',\n    'dello',\n    'dei',\n    'degli',\n    'dell',\n    'degl',\n    'della',\n    'delle',\n    'in',\n    'nel',\n    'nello',\n    'nei',\n    'negli',\n    'nell',\n    'negl',\n    'nella',\n    'nelle',\n    'su',\n    'sul',\n    'sullo',\n    'sui',\n    'sugli',\n    'sull',\n    'sugl',\n    'sulla',\n    'sulle',\n    'per',\n    'tra',\n    'contro',\n    'io',\n    'tu',\n    'lui',\n    'lei',\n    'noi',\n    'voi',\n    'loro',\n    'mio',\n    'mia',\n    'miei',\n    'mie',\n    'tuo',\n    'tua',\n    'tuoi',\n    'tue',\n    'suo',\n    'sua',\n    'suoi',\n    'sue',\n    'nostro',\n    'nostra',\n    'nostri',\n    'nostre',\n    'vostro',\n    'vostra',\n    'vostri',\n    'vostre',\n    'mi',\n    'ti',\n    'ci',\n    'vi',\n    'lo',\n    'la',\n    'li',\n    'le',\n    'gli',\n    'ne',\n    'il',\n    'un',\n    'uno',\n    'una',\n    'ma',\n    'ed',\n    'se',\n    'perché',\n    'anche',\n    'come',\n    'dov',\n    'dove',\n    'che',\n    'chi',\n    'cui',\n    'non',\n    'più',\n    'quale',\n    'quanto',\n    'quanti',\n    'quanta',\n    'quante',\n    'quello',\n    'quelli',\n    'quella',\n    'quelle',\n    'questo',\n    'questi',\n    'questa',\n    'queste',\n    'si',\n    'tutto',\n    'tutti',\n    'a',\n    'c',\n    'e',\n    'i',\n    'l',\n    'o',\n    'ho',\n    'hai',\n    'ha',\n    'abbiamo',\n    'avete',\n    'hanno',\n    'abbia',\n    'abbiate',\n    'abbiano',\n    'avrò',\n    'avrai',\n    'avrà',\n    'avremo',\n    'avrete',\n    'avranno',\n    'avrei',\n    'avresti',\n    'avrebbe',\n    'avremmo',\n    'avreste',\n    'avrebbero',\n    'avevo',\n    'avevi',\n    'aveva',\n    'avevamo',\n    'avevate',\n    'avevano',\n    'ebbi',\n    'avesti',\n    'ebbe',\n    'avemmo',\n    'aveste',\n    'ebbero',\n    'avessi',\n    'avesse',\n    'avessimo',\n    'avessero',\n    'avendo',\n    'avuto',\n    'avuta',\n    'avuti',\n    'avute',\n    'sono',\n    'sei',\n    'è',\n    'siamo',\n    'siete',\n    'sia',\n    'siate',\n    'siano',\n    'sarò',\n    'sarai',\n    'sarà',\n    'saremo',\n    'sarete',\n    'saranno',\n    'sarei',\n    'saresti',\n    'sarebbe',\n    'saremmo',\n    'sareste',\n    'sarebbero',\n    'ero',\n    'eri',\n    'era',\n    'eravamo',\n    'eravate',\n    'erano',\n    'fui',\n    'fosti',\n    'fu',\n    'fummo',\n    'foste',\n    'furono',\n    'fossi',\n    'fosse',\n    'fossimo',\n    'fossero',\n    'essendo',\n    'faccio',\n    'fai',\n    'facciamo',\n    'fanno',\n    'faccia',\n    'facciate',\n    'facciano',\n    'farò',\n    'farai',\n    'farà',\n    'faremo',\n    'farete',\n    'faranno',\n    'farei',\n    'faresti',\n    'farebbe',\n    'faremmo',\n    'fareste',\n    'farebbero',\n    'facevo',\n    'facevi',\n    'faceva',\n    'facevamo',\n    'facevate',\n    'facevano',\n    'feci',\n    'facesti',\n    'fece',\n    'facemmo',\n    'faceste',\n    'fecero',\n    'facessi',\n    'facesse',\n    'facessimo',\n    'facessero',\n    'facendo',\n    'sto',\n    'stai',\n    'sta',\n    'stiamo',\n    'stanno',\n    'stia',\n    'stiate',\n    'stiano',\n    'starò',\n    'starai',\n    'starà',\n    'staremo',\n    'starete',\n    'staranno',\n    'starei',\n    'staresti',\n    'starebbe',\n    'staremmo',\n    'stareste',\n    'starebbero',\n    'stavo',\n    'stavi',\n    'stava',\n    'stavamo',\n    'stavate',\n    'stavano',\n    'stetti',\n    'stesti',\n    'stette',\n    'stemmo',\n    'steste',\n    'stettero',\n    'stessi',\n    'stesse',\n    'stessimo',\n    'stessero',\n    'stando'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,KACA,KACA,OACA,KACA,OACA,MACA,MACA,OACA,OACA,MACA,MACA,MACA,KACA,MACA,QACA,MACA,QACA,OACA,OACA,QACA,QACA,KACA,MACA,QACA,MACA,QACA,OACA,OACA,QACA,QACA,KACA,MACA,QACA,MACA,QACA,OACA,OACA,QACA,QACA,KACA,MACA,QACA,MACA,QACA,OACA,OACA,QACA,QACA,MACA,MACA,SACA,KACA,KACA,MACA,MACA,MACA,MACA,OACA,MACA,MACA,OACA,MACA,MACA,MACA,OACA,MACA,MACA,MACA,OACA,MACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MACA,KACA,KACA,KACA,MACA,MACA,KACA,KACA,KACA,YACA,QACA,OACA,MACA,OACA,MACA,MACA,MACA,MACA,SACA,QACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,KACA,QACA,QACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,MACA,KACA,UACA,QACA,QACA,QACA,UACA,UACA,UACA,QACA,UACA,SACA,SACA,UACA,QACA,UACA,UACA,UACA,UACA,YACA,QACA,QACA,QACA,UACA,UACA,UACA,OACA,SACA,OACA,SACA,SACA,SACA,SACA,SACA,WACA,WACA,SACA,QACA,QACA,QACA,QACA,OACA,MACA,OACA,QACA,QACA,MACA,QACA,QACA,UACA,QACA,UACA,SACA,SACA,UACA,QACA,UACA,UACA,UACA,UACA,YACA,MACA,MACA,MACA,UACA,UACA,QACA,MACA,QACA,KACA,QACA,QACA,SACA,QACA,QACA,UACA,UACA,UACA,SACA,MACA,WACA,QACA,SACA,WACA,WACA,UACA,QACA,UACA,SACA,SACA,UACA,QACA,UACA,UACA,UACA,UACA,YACA,SACA,SACA,SACA,WACA,WACA,WACA,OACA,UACA,OACA,UACA,UACA,SACA,UACA,UACA,YACA,YACA,UACA,MACA,OACA,MACA,SACA,SACA,OACA,SACA,SACA,WACA,SACA,WACA,UACA,UACA,WACA,SACA,WACA,WACA,WACA,WACA,aACA,QACA,QACA,QACA,UACA,UACA,UACA,SACA,SACA,SACA,SACA,SACA,WACA,SACA,SACA,WACA,WACA,SACH"}