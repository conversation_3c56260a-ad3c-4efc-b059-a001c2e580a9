---
title: 自定义 TEN Agent
---

## 使用 Playground

一旦您在 [localhost:3000](http://localhost:3000) 上运行了 playground，您可以通过三个简单的步骤自定义您的智能体：

1. **图表类型选择**

   - 在语音智能体、实时智能体或其他类型之间选择

2. **模块选择**

   - 选择与您选择的图表类型匹配的模块

3. **扩展配置**

   - 选择扩展并配置其 API 密钥
   - 根据需要调整设置

playground 提供了一个直观的界面来连接这些组件，无需编码。

## 自己更改代码

如果您觉得可以自己编辑代码，我们非常欢迎您这样做。在 `agents/property.json` 中，找到相应的图表并操作您想要的任何值。

进行更改后，只需刷新页面并连接到智能体，更改将生效。
