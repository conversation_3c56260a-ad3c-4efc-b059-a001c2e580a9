/**
 * @param {ReadonlyArray<number | string>} ids
 *   Identifiers (example: `['list', 0]).
 * @returns {Identifier | MemberExpression}
 *   Identifier or member expression.
 */
export function toIdOrMemberExpression(ids: ReadonlyArray<number | string>): Identifier | MemberExpression;
/**
 * @param {ReadonlyArray<number | string>} ids
 *   Identifiers (example: `['list', 0]).
 * @returns {JSXIdentifier | JSXMemberExpression}
 *   Identifier or member expression.
 */
export function toJsxIdOrMemberExpression(ids: ReadonlyArray<number | string>): JSXIdentifier | JSXMemberExpression;
import type { Identifier } from 'estree-jsx';
import type { MemberExpression } from 'estree-jsx';
import type { JSXIdentifier } from 'estree-jsx';
import type { JSXMemberExpression } from 'estree-jsx';
//# sourceMappingURL=estree-util-to-id-or-member-expression.d.ts.map