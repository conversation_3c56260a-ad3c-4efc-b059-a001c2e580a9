{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'au',\n    'aux',\n    'avec',\n    'ce',\n    'ces',\n    'dans',\n    'de',\n    'des',\n    'du',\n    'elle',\n    'en',\n    'et',\n    'eux',\n    'il',\n    'je',\n    'la',\n    'le',\n    'leur',\n    'lui',\n    'ma',\n    'mais',\n    'me',\n    'même',\n    'mes',\n    'moi',\n    'mon',\n    'ne',\n    'nos',\n    'notre',\n    'nous',\n    'on',\n    'ou',\n    'par',\n    'pas',\n    'pour',\n    'qu',\n    'que',\n    'qui',\n    'sa',\n    'se',\n    'ses',\n    'son',\n    'sur',\n    'ta',\n    'te',\n    'tes',\n    'toi',\n    'ton',\n    'tu',\n    'un',\n    'une',\n    'vos',\n    'votre',\n    'vous',\n    'c',\n    'd',\n    'j',\n    'l',\n    'à',\n    'm',\n    'n',\n    's',\n    't',\n    'y',\n    '',\n    'été',\n    'étée',\n    'étées',\n    'étés',\n    'étant',\n    'suis',\n    'es',\n    'est',\n    'sommes',\n    'êtes',\n    'sont',\n    'serai',\n    'seras',\n    'sera',\n    'serons',\n    'serez',\n    'seront',\n    'serais',\n    'serait',\n    'serions',\n    'seriez',\n    'seraient',\n    'étais',\n    'était',\n    'étions',\n    'étiez',\n    'étaient',\n    'fus',\n    'fut',\n    'fûmes',\n    'fûtes',\n    'furent',\n    'sois',\n    'soit',\n    'soyons',\n    'soyez',\n    'soient',\n    'fusse',\n    'fusses',\n    'fût',\n    'fussions',\n    'fussiez',\n    'fussent',\n    'ayant',\n    'eu',\n    'eue',\n    'eues',\n    'eus',\n    'ai',\n    'as',\n    'avons',\n    'avez',\n    'ont',\n    'aurai',\n    'auras',\n    'aura',\n    'aurons',\n    'aurez',\n    'auront',\n    'aurais',\n    'aurait',\n    'aurions',\n    'auriez',\n    'auraient',\n    'avais',\n    'avait',\n    'avions',\n    'aviez',\n    'avaient',\n    'eut',\n    'eûmes',\n    'eûtes',\n    'eurent',\n    'aie',\n    'aies',\n    'ait',\n    'ayons',\n    'ayez',\n    'aient',\n    'eusse',\n    'eusses',\n    'eût',\n    'eussions',\n    'eussiez',\n    'eussent',\n    'ceci',\n    'cela',\n    'celà',\n    'cet',\n    'cette',\n    'ici',\n    'ils',\n    'les',\n    'leurs',\n    'quel',\n    'quels',\n    'quelle',\n    'quelles',\n    'sans',\n    'soi'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,KACA,MACA,OACA,KACA,MACA,OACA,KACA,MACA,KACA,OACA,KACA,KACA,MACA,KACA,KACA,KACA,KACA,OACA,MACA,KACA,OACA,KACA,UACA,MACA,MACA,MACA,KACA,MACA,QACA,OACA,KACA,KACA,MACA,MACA,OACA,KACA,MACA,MACA,KACA,KACA,MACA,MACA,MACA,KACA,KACA,MACA,MACA,MACA,KACA,KACA,MACA,MACA,QACA,OACA,IACA,IACA,IACA,IACA,OACA,IACA,IACA,IACA,IACA,IACA,GACA,YACA,aACA,cACA,aACA,WACA,OACA,KACA,MACA,SACA,UACA,OACA,QACA,QACA,OACA,SACA,QACA,SACA,SACA,SACA,UACA,SACA,WACA,WACA,WACA,YACA,WACA,aACA,MACA,MACA,WACA,WACA,SACA,OACA,OACA,SACA,QACA,SACA,QACA,SACA,SACA,WACA,UACA,UACA,QACA,KACA,MACA,OACA,MACA,KACA,KACA,QACA,OACA,MACA,QACA,QACA,OACA,SACA,QACA,SACA,SACA,SACA,UACA,SACA,WACA,QACA,QACA,SACA,QACA,UACA,MACA,WACA,WACA,SACA,MACA,OACA,MACA,QACA,OACA,QACA,QACA,SACA,SACA,WACA,UACA,UACA,OACA,OACA,UACA,MACA,QACA,MACA,MACA,MACA,QACA,OACA,QACA,SACA,UACA,OACA,MACH"}