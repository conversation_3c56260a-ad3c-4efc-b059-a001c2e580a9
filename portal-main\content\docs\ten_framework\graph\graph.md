---
title: Graph
---

In the TEN framework, there are two types of graphs:

1. Dynamic Graph
2. Predefined Graph (`predefined_graph`)

|               | Dynamic Graph                                       | Predefined Graph                                                       |
| ------------- | --------------------------------------------------- | ---------------------------------------------------------------------- |
| Startup Time  | When the TEN app receives the `start_graph` command | When the TEN app starts, or when it receives the `start_graph` command |
| Graph Content | Specified in the `start_graph` command              | Predefined in the TEN app's properties                                 |
| Graph ID      | Randomly generated UUID                             | Randomly generated UUID                                                |

![Two types of graphs](https://ten-framework-assets.s3.amazonaws.com/doc-assets/two_types_of_graph.png)

Predefined graphs have an `auto_start` property to determine whether the graph automatically starts when the TEN app launches.

Additionally, predefined graphs have a `singleton` property that indicates whether only _one_ instance of the graph can be generated within the TEN app.

## Graph ID and Graph Name

For each graph instance, the TEN app internally uses a unique UUID4 string as an identifier, called the **Graph ID**.

For predefined graphs, you can assign a meaningful and easy-to-remember name, called the **Graph Name**. When you need to specify a particular predefined graph, you can directly use its graph name. If a predefined graph has the `singleton` property, it means that only one instance of this predefined graph can exist within the TEN app. In this case, the TEN runtime uses the graph name to uniquely identify the single instance generated from that predefined graph.

## Dynamic Graph

When a TEN app receives a `start_graph` command and creates a dynamic graph, the system assigns a random UUID as the graph's ID. If other clients obtain this graph ID, they can also connect to this graph.

Dynamic Graph ID example:

`123e4567-e89b-12d3-a456-************`

## Predefined Graph

Predefined graphs work similarly to dynamic graphs, with the main difference being how the content is defined. The content of a dynamic graph is included in the `start_graph` command, while the content of a predefined graph is defined in advance by the TEN app. Clients only need to specify the name of the predefined graph in the `start_graph` command to start it.

The main advantage of predefined graphs is simplifying usage and protecting sensitive information. With predefined graphs, clients don't need to know the detailed structure of the graph, which both improves usability and avoids exposing potentially sensitive information contained in the graph.

Predefined Graph name example:

`http_server`

When a TEN app starts, all predefined graphs with the `auto_start` property set will automatically start.

## Graph Definition

Whether it's a dynamic graph or a predefined graph, the definition structure is the same:

```json
{
  "nodes": [
    // Definition of nodes
  ],
  "connections": [
    // Definition of communication links
  ]
}
```

Key points:

1. If there is only one TEN app, you can omit the `app` field. If there are multiple apps, you must explicitly specify the `app` field.

2. The `nodes` field defines the nodes in the graph, such as various extensions.

3. Each node can appear only once in the `nodes` field. If the same node appears multiple times, the TEN framework will report an error during validation.

4. The way to define an extension in the `nodes` field is as follows, where the `property` field is optional:

   ```json
   {
     "type": "extension",
     "name": "simple_http_server_cpp",
     "addon": "simple_http_server_cpp",
     "extension_group": "default_extension_group",
     "app": "msgpack://127.0.0.1:8001/",
     "property": {
       "root_key": "player",
       "extra_keys": ["playerName"]
     }
   }
   ```

   The `addon` field indicates that the extension is an instance generated by the corresponding plugin.

5. The `connections` field defines the communication links between nodes in the graph, where the `extension` value represents the name of the corresponding node.

Complete example:

```json
{
  "nodes": [
    {
      "type": "extension",
      "app": "msgpack://127.0.0.1:8001/",
      "name": "simple_http_server_cpp",
      "addon": "simple_http_server_cpp",
      "extension_group": "default_extension_group",
      "property": {
        "root_key": "player",
        "extra_keys": ["playerName"]
      }
    }
  ],
  "connections": [
    {
      "app": "msgpack://127.0.0.1:8001/",
      "extension": "simple_http_server_cpp",
      "cmd": [
        {
          "name": "start",
          "dest": [
            {
              "app": "msgpack://127.0.0.1:8001/",
              "extension": "gateway"
            }
          ]
        },
        {
          "name": "stop",
          "dest": [
            {
              "app": "msgpack://127.0.0.1:8001/",
              "extension": "gateway"
            }
          ]
        }
      ]
    },
    {
      "app": "msgpack://127.0.0.1:8001/",
      "extension": "gateway",
      "cmd": [
        {
          "name": "push_status_online",
          "dest": [
            {
              "app": "msgpack://127.0.0.1:8001/",
              "extension": "uap"
            }
          ]
        }
      ]
    }
  ]
}
```

## Predefined Graph Definition

Predefined graphs are defined under the `predefined_graphs` field in the TEN app's configuration file (`property.json`), including properties such as `name`, `auto_start`, and `singleton`:

```json
{
  "ten": {
    "predefined_graphs": [
      {
        "name": "default",
        "auto_start": true,
        "singleton": true,
        // Complete graph definition
      }
    ]
  }
}
```

Complete example:

```json
{
  "ten": {
    "predefined_graphs": [
      {
        "name": "default",
        "auto_start": true,
        "singleton": true,
        "nodes": [
          {
            "type": "extension",
            "name": "simple_http_server_cpp",
            "addon": "simple_http_server_cpp",
            "extension_group": "default_extension_group",
            "property": {
              "root_key": "player",
              "extra_keys": [
                "playerName"
              ]
            }
          }
        ],
        "connections": [
          {
            "app": "msgpack://127.0.0.1:8001/",
            "extension": "simple_http_server_cpp",
            "cmd": [
              {
                "name": "start",
                "dest": [
                  {
                    "app": "msgpack://127.0.0.1:8001/",
                    "extension": "gateway"
                  }
                ]
              },
              {
                "name": "stop",
                "dest": [
                  {
                    "app": "msgpack://127.0.0.1:8001/",
                    "extension": "gateway"
                  }
                ]
              }
            ]
          },
          {
            "app": "msgpack://127.0.0.1:8001/",
            "extension": "gateway",
            "cmd": [
              {
                "name": "push_status_online",
                "dest": [
                  {
                    "app": "msgpack://127.0.0.1:8001/",
                    "extension": "uap"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## `start_graph` Command Definition

Dynamic graphs are created by sending a `start_graph` command to the TEN app, placing the graph definition in the command's `ten` field:

```json
{
  "ten": {
    "type": "start_graph",
    // Complete graph definition
  }
}
```

Complete example:

```json
{
  "ten": {
    "type": "start_graph",
    "nodes": [
      {
        "type": "extension",
        "name": "simple_http_server_cpp",
        "addon": "simple_http_server_cpp",
        "extension_group": "default_extension_group",
        "property": {
          "root_key": "player",
          "extra_keys": ["playerName"]
        }
      }
    ],
    "connections": [
      {
        "app": "msgpack://127.0.0.1:8001/",
        "extension": "simple_http_server_cpp",
        "cmd": [
          {
            "name": "start",
            "dest": [
              {
                "app": "msgpack://127.0.0.1:8001/",
                "extension": "gateway"
              }
            ]
          },
          {
            "name": "stop",
            "dest": [
              {
                "app": "msgpack://127.0.0.1:8001/",
                "extension": "gateway"
              }
            ]
          }
        ]
      },
      {
        "extension": "gateway",
        "cmd": [
          {
            "name": "push_status_online",
            "dest": [
              {
                "extension": "uap"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### Using `import_uri` to Reference Graph Definitions

When defining predefined graphs, you can use the `import_uri` field to reference external graph definition files:

```json
{
  "ten": {
    "uri": "http://localhost:8001",
    "predefined_graphs": [
      {
        "name": "default",
        "auto_start": false,
        "import_uri": "../graph.json"
      }
    ]
  }
}
```

`import_uri` can be a relative path, absolute path, or URL:

- Relative path: relative to the directory containing the `property.json` file
- Absolute path: relative to the root directory of the environment where the TEN app is located
- URL: treated directly as a URL path

## Graph Definition Specifications

- **`nodes` Field**: The `nodes` array must be provided in the graph definition. In contrast, while the `connections` array is optional, it is recommended to provide it to define communication between nodes.

- **Node `app` Field**: The `app` field cannot be set to `localhost`. In single-app graphs, the `app` URI should be omitted; in multi-app graphs, the `app` field value must match the `ten.uri` value in each app's `property.json`.

- **Node Uniqueness**: Each node in the `nodes` array represents a specific extension instance and must be uniquely identified by the combination of `app` and `name`. Duplicate definitions are not allowed, as in the following invalid example:

  ```json
  {
    "nodes": [
      {
        "type": "extension",
        "name": "some_ext",
        "addon": "addon_1",
        "extension_group": "test"
      },
      {
        "type": "extension",
        "name": "some_ext",
        "addon": "addon_2",
        "extension_group": "test"
      }
    ]
  }
  ```

- **Connection Definition Consistency**: All extension instances referenced in `connections` must be explicitly defined in `nodes`. The following example is invalid because `ext_2` is not defined in `nodes`:

  ```json
  {
    "nodes": [
      {
        "type": "extension",
        "name": "ext_1",
        "addon": "addon_1",
        "extension_group": "some_group"
      }
    ],
    "connections": [
      {
        "extension": "ext_1",
        "cmd": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          }
        ]
      }
    ]
  }
  ```

- **Communication Link Consolidation**: All messages from the same source extension should be grouped in a single section, not scattered across multiple definitions. The following example is incorrect:

  ```json
  {
    "connections": [
      {
        "extension": "ext_1",
        "cmd": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          }
        ]
      },
      {
        "extension": "ext_1",
        "data": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          }
        ]
      }
    ]
  }
  ```

  The correct approach is to consolidate all messages from the same source extension together:

  ```json
  {
    "connections": [
      {
        "extension": "ext_1",
        "cmd": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          }
        ],
        "data": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          }
        ]
      }
    ]
  }
  ```

- **Message Destination Consolidation**: For each specific type of message, all destination extensions should be grouped under a single entry. The following example is incorrect:

  ```json
  {
    "connections": [
      {
        "extension": "ext_1",
        "cmd": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              }
            ]
          },
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_3"
              }
            ]
          }
        ]
      }
    ]
  }
  ```

  The correct approach is to consolidate all destinations for the same message together:

  ```json
  {
    "connections": [
      {
        "extension": "ext_1",
        "cmd": [
          {
            "name": "hello",
            "dest": [
              {
                "extension": "ext_2"
              },
              {
                "extension": "ext_3"
              }
            ]
          }
        ]
      }
    ]
  }
  ```

  Note that messages with the same name can coexist in different types (such as `cmd` and `data`) without causing conflicts.

## Subgraph

The core mechanism of the TEN framework is based on graph structures, which consist of nodes and connections. Subgraphs are a powerful reuse mechanism that allows complex graph structures to be split into reusable modules, thereby improving code organization and maintainability.

### Subgraph Design Philosophy

The essence of a graph is to define how data flows between extensions. Subgraphs do not change this fundamental principle in their implementation, but serve as syntactic sugar that is ultimately flattened into the parent graph and started using the same mechanisms as regular graphs. This design simplifies the development of complex systems while not adding runtime complexity.

#### Design Principles

Subgraph design follows these core principles:

1. **Independence**: Any graph is a complete graph that can run independently or be embedded as a component into other graphs.

2. **Tool-Friendly**: Subgraphs provide additional information to help development tools better understand graph structures, improving the development experience while not adding runtime complexity.

3. **Flattening Mechanism**: Subgraphs are ultimately flattened into standard graph structures to ensure performance and compatibility.

4. **Simplicity**: Subgraph design aims to simplify the development process rather than add complexity, avoiding increased JSON structure complexity.

The purpose of subgraph design is to allow developers to use a graph as a black box without needing to understand its internal complexity. Therefore, at the point of referencing subgraphs, no special patching mechanisms are provided to adjust the internal state of subgraphs, to reduce complexity. If subgraph definitions need to be modified, the original definitions should be modified directly rather than patching at the reference point. This is done to avoid complexity spread and increased maintenance costs. For example, when introducing subgraph A into large graph B, if you need to adjust the extension property schema definitions inside subgraph A, you should directly modify the schema definitions of that extension rather than providing modification mechanisms at the reference point, because this might lead to adverse chain reactions, only modifying the extension's schema definitions without patching other places, thus causing errors elsewhere.

In other words, subgraphs primarily provide a mapping mechanism to expose internal elements of subgraphs to the outside, rather than providing mechanisms to patch internal elements of subgraphs.

### Subgraph Implementation Mechanism

Next, we will detail the implementation mechanism of subgraphs, including subgraph definition, reference methods, and the flattening process.

#### Subgraph Definition Example

The following is a `subgraph.json` example that can be used as an independent graph or as a subgraph referenced by other graphs:

```json
{
  "nodes": [
    {
      // Define extension node named ext_c
      "type": "extension",
      "name": "ext_c",
      "addon": "extension_c"
    },
    {
      // Define extension node named ext_d
      "type": "extension",
      "name": "ext_d",
      "addon": "extension_d"
    }
  ],
  "connections": [
    {
      // ext_c sends B command to ext_d
      "extension": "ext_c",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              "extension": "ext_d"
            }
          ]
        }
      ]
    }
  ],
  "exposed_messages": [
    // Represents message interfaces exposed by the graph to the outside, mainly for development tools to provide intelligent hints
    {
      // ext_d's B command exposed to the outside
      "extension": "ext_d",
      "type": "cmd_in",
      "name": "B"
    }
  ],
  "exposed_properties": [
    // Represents properties exposed by the graph to the outside
    {
      "extension": "ext_c",
      "name": "a",
      "alias": "a1"
    }
  ]
}
```

#### Subgraph Reference Example

Example of `graph.json` referencing subgraphs:

```json
{
  "nodes": [
    {
      // Define extension node named ext_a
      "type": "extension",
      "name": "ext_a",
      "addon": "extension_a"
    },
    {
      // Define extension node named ext_b
      "type": "extension",
      "name": "ext_b",
      "addon": "extension_b"
    },
    {
      // Reference subgraph, named subgraph_1
      "type": "subgraph",
      "name": "subgraph_1",
      "import_uri": "./ten_packages/extension/aaa/subgraph.json",
      "property": {
        // Properties written in this property field must exist in the subgraph's exposed_properties field,
        // otherwise it will be an error condition
        "app_id": "${env:AGORA_APP_ID}",
        "token": "<agora_token>",
        "channel": "ten_agent_test"
      }
    },
    {
      // Reference subgraph, named subgraph_2
      "type": "subgraph",
      "name": "subgraph_2",
      "import_uri": "./ten_packages/extension/bbb/subgraph.json"
    }
  ],
  "connections": [
    {
      "extension": "ext_a",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              // First target is ext_b
              "extension": "ext_b"
            },
            {
              // Second target is ext_d in the subgraph, this is advanced usage
              "extension": "subgraph_1:ext_d"
            },
            {
              // Third target is the subgraph, but this requires the subgraph to explicitly expose the B cmd_in interface,
              // otherwise it will be treated as an error condition
              "subgraph": "subgraph_2"
            }
          ]
        }
      ]
    },
    {
      // ext_c in the subgraph sends cmd H to ext_a, this is advanced usage
      "extension": "subgraph_1:ext_c",
      "cmd": [
        {
          "name": "H",
          "dest": [
            {
              "extension": "ext_a"
            }
          ]
        }
      ]
    },
    {
      // Subgraph subgraph_2 sends cmd H to ext_a, but this requires the subgraph to expose the H
      // cmd_out interface, otherwise it will be treated as an error condition
      "subgraph": "subgraph_2",
      "cmd": [
        {
          "name": "H",
          "dest": [
            {
              "extension": "ext_a"
            }
          ]
        }
      ]
    }
  ]
}
```

#### Key Concepts

The subgraph mechanism introduces three key concepts:

1. **Message Exposure** (exposed_messages):
   - Subgraphs declare externally exposed interfaces through the `exposed_messages` field
   - Mainly used by development tools to implement intelligent hints and checks
   - Hides internal details of subgraphs, improving development experience

2. **Property Exposure** (exposed_properties):
   - Subgraphs declare externally exposed properties through the `exposed_properties` field
   - Mainly used by development tools to implement intelligent hints and checks
   - Hides internal details of subgraphs, improving development experience

3. **Subgraph Reference and Naming**:
   - Reference other graph files through `"type": "subgraph"`
   - Each subgraph has a unique identifier name serving as a namespace
   - Prevents conflicts between elements with the same name in different subgraphs

4. **Cross-Graph Connections**:
   - Reference elements within subgraphs through namespace syntax (such as `subgraph_1:ext_d`), or reference subgraphs through the `subgraph` field
   - Makes elements within subgraphs interact with the main graph
   - Builds complex cross-graph message flows

In summary, the `exposed_messages` and `exposed_properties` fields describe how messages flow into the graph from the graph boundary, and development tools use these fields to provide intelligent hints, making it convenient for developers to specify how messages flow to the graph boundary.

#### Flattening Mechanism

Ultimately, graphs referencing subgraphs are flattened into regular graph structures, ensuring runtime uniformity and efficiency. However, this flattening result is not seen by users, but is flattened into memory for use in the TEN runtime or TEN manager. Note that in the flattened graph definition, there are the original extensions and the connections between these extensions.

```json
{
  "nodes": [
    {
      "type": "extension",
      "name": "ext_a",
      "addon": "extension_a"
    },
    {
      "type": "extension",
      "name": "ext_b",
      "addon": "extension_b"
    },
    {
      // ext_c from subgraph is flattened with subgraph name as prefix
      "type": "extension",
      "name": "subgraph_1_ext_c",
      "addon": "extension_c"
    },
    {
      // ext_d from subgraph is flattened with subgraph name as prefix
      "type": "extension",
      "name": "subgraph_1_ext_d",
      "addon": "extension_d",
      "property": {
        // The property field defined when referencing the subgraph will be flattened into the corresponding extension's property field
        "app_id": "${env:AGORA_APP_ID}"
      }
    },
    {
      // ext_e from subgraph is flattened with subgraph name as prefix
      "type": "extension",
      "name": "subgraph_2_ext_e",
      "addon": "extension_e"
    }
  ],
  "connections": [
    {
      "extension": "ext_a",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              "extension": "ext_b"
            },
            {
              "extension": "subgraph_1_ext_d"
            },
            {
              "extension": "subgraph_2_ext_e"
            }
          ]
        }
      ]
    },
    {
      "extension": "subgraph_1_ext_c",
      "cmd": [
        {
          "name": "H",
          "dest": [
            {
              "extension": "ext_a"
            }
          ]
        }
      ]
    },
    {
      // Internal connections within subgraphs are also flattened and included
      "extension": "subgraph_1_ext_c",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              "extension": "subgraph_1_ext_d"
            }
          ]
        }
      ]
    }
  ]
  // exposed_messages and exposed_properties fields are discarded during flattening
}
```

The subgraph flattening mechanism follows these rules:

1. Before flattening, the colon (`:`) symbol indicates that an element is located within a subgraph (such as `subgraph_1:ext_d`).

2. After flattening, elements within subgraphs have the subgraph name added as a prefix to their names (such as `subgraph_1_ext_c`), ensuring global uniqueness.

3. The flattened graph definition no longer contains colon symbols, distinguishing between pre-flattening and post-flattening states.

4. Internal connections within subgraphs are preserved and included in the flattened graph, ensuring functional completeness.

### Advanced Features and Applications

As project complexity increases, advanced features of subgraphs can help better organize and manage systems.

#### Message Conversion and Subgraphs

Subgraphs fully support the message conversion (msg_conversion) mechanism for handling message format conversion between different interfaces:

```json
{
  "nodes": [
    {
      "type": "extension",
      "name": "ext_a",
      "addon": "addon_a"
    },
    {
      "type": "extension",
      "name": "ext_b",
      "addon": "addon_b"
    },
    {
      "type": "subgraph",
      "name": "subgraph_1",
      "import_uri": "http://a.b.c.d/subgraph.json"
    }
  ],
  "connections": [
    {
      "extension": "ext_a",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              "extension": "ext_b",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            },
            {
              "extension": "subgraph_1:ext_d",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            },
            {
              "subgraph": "subgraph_2",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            }
          ]
        }
      ]
    }
  ]
}
```

Development tools can use `exposed_messages` information to suggest compatibility and provide message conversion configuration interfaces. After configuration, conversion rules are automatically written into the graph definition, simplifying the development process.

After flattening, message conversion rules are correctly preserved, ensuring runtime behavior matches design intent:

```json
{
  "nodes": [
    {
      "type": "extension",
      "name": "ext_a",
      "addon": "addon_a"
    },
    {
      "type": "extension",
      "name": "ext_b",
      "addon": "addon_b"
    },
    {
      "type": "extension",
      "name": "subgraph_1_ext_c",
      "addon": "addon_c"
    },
    {
      "type": "extension",
      "name": "subgraph_1_ext_d",
      "addon": "addon_d"
    },
    {
      "type": "extension",
      "name": "subgraph_2_ext_e",
      "addon": "addon_e"
    }
  ],
  "connections": [
    {
      "extension": "ext_a",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              "extension": "ext_b",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            },
            {
              "extension": "subgraph_1_ext_d",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            },
            {
              "extension": "subgraph_2_ext_e",
              "msg_conversion": {
                "type": "per_property",
                "rules": [
                  {
                    "path": "extra_data",
                    "conversion_mode": "fixed_value",
                    "value": "tool_call"
                  }
                ],
                "keep_original": true
              }
            }
          ]
        }
      ]
    }
  ]
}
```

Through this approach, developers can flexibly handle message format differences between different components without needing to understand underlying implementation details.

## Meaning of "subgraph_1:ext_c"

In the previous examples, `subgraph_1` in `subgraph_1:ext_c` does not represent the subgraph itself, but references an element in the `nodes` field. In subgraph applications, `subgraph_1` can be viewed as a namespace.

Through this mechanism, you can specify extensions at different locations as sources or targets in graph connections, enabling connections across multiple graphs.

The core concept is: graph connections are essentially connections between extensions. Through this mechanism, you can specify elements of different types in the `nodes` field as sources or targets of connections, enabling connections across multiple subgraphs or even multiple graphs.

## Connections Across Multiple Graphs

In addition to subgraphs, you can also specify connections to other graphs within a single graph through similar mechanisms.

### Connecting to Predefined Graphs in the Same TEN App

Example: How to connect to predefined graphs within the same TEN app in a graph:

```json
{
  "nodes": [
    {
      // Define extension named ext_a
      "type": "extension",
      "name": "ext_a",
      "addon": "extension_a"
    },
    {
      // Define extension named ext_b
      "type": "extension",
      "name": "ext_b",
      "addon": "extension_b"
    },
    {
      // Reference other graph, graph ID is default
      "type": "graph",
      "graph_id": "default"
    }
  ],
  "connections": [
    {
      "extension": "ext_a",
      "cmd": [
        {
          "name": "B",
          "dest": [
            {
              // First target is ext_b
              "extension": "ext_b"
            },
            {
              // Second target is ext_d in another graph
              "extension": "default:ext_d"
            }
          ]
        }
      ]
    },
    {
      // ext_c in another graph sends cmd H to ext_a
      "extension": "default:ext_c",
      "cmd": [
        {
          "name": "H",
          "dest": [
            {
              "extension": "ext_a"
            }
          ]
        }
      ]
    },
    {
      // Another graph sends cmd X to ext_a, but this requires the other graph to expose the X cmd_out interface,
      // otherwise it will be treated as an error condition. This error condition can be reported during static
      // checking if it can be statically detected, or at runtime.
      "graph": "default",
      "cmd": [
        {
          "name": "X",
          "dest": [
            {
              "extension": "ext_a"
            }
          ]
        }
      ]
    }
  ]
}
```

### Types of Graph Nodes

1. Subgraph

   ```json
   {
     "type": "subgraph",
     "name": "subgraph_1", // Meaning of namespace
     "import_uri": "http://a.b.c.d/subgraph.json"
   }
   ```

2. Local graph

   ```json
   {
     "type": "graph",
     "graph_name": "default",
     "singleton": true
   }
   ```

   ```json
   {
     "type": "graph",
     "graph_name": "default",
     "singleton": false
   }
   ```

3. Remote graph

   ```json
   {
     "type": "graph",
     "graph_name": "default",
     "singleton": true,
     "app": "msgpack://127.0.0.1:8002/"
   }
   ```

   ```json
   {
     "type": "graph",
     "graph_name": "default",
     "singleton": false,
     "app": "msgpack://127.0.0.1:8002/"
   }
   ```
