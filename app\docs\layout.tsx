import { DocsLayout } from 'fumadocs-ui/layouts/notebook';
import type { ReactNode } from 'react';
import { baseOptions } from '@/app/layout.config';
import { Metadata } from 'next';

import { source } from '@/lib/source';

export const metadata: Metadata = {
  title: 'Help Center | DeepBI',
  description: 'DeepBI帮助中心文档',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
  },
};

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <DocsLayout tree={source.pageTree} {...baseOptions} tabMode="sidebar">
      {children}
    </DocsLayout>
  );
}
