"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-mdx";
exports.ids = ["vendor-chunks/fumadocs-mdx"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* binding */ _runtime),\n/* harmony export */   createMDXSource: () => (/* binding */ createMDXSource),\n/* harmony export */   resolveFiles: () => (/* binding */ resolveFiles)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n// src/runtime/index.ts\n\nvar _runtime = {\n  doc(files) {\n    return files.map((file) => {\n      const { default: body, frontmatter, ...exports } = file.data;\n      let cachedContent;\n      return {\n        body,\n        ...exports,\n        ...frontmatter,\n        get content() {\n          cachedContent ??= fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(file.info.absolutePath).toString();\n          return cachedContent;\n        },\n        _exports: file.data,\n        _file: file.info\n      };\n    });\n  },\n  meta(files) {\n    return files.map((file) => {\n      return {\n        ...file.data,\n        _file: file.info\n      };\n    });\n  },\n  docs(docs, metas) {\n    const parsedDocs = this.doc(docs);\n    const parsedMetas = this.meta(metas);\n    return {\n      docs: parsedDocs,\n      meta: parsedMetas,\n      toFumadocsSource() {\n        return createMDXSource(parsedDocs, parsedMetas);\n      }\n    };\n  }\n};\nfunction createMDXSource(docs, meta = []) {\n  return {\n    files: () => resolveFiles({\n      docs,\n      meta\n    })\n  };\n}\nfunction resolveFiles({ docs, meta }) {\n  const outputs = [];\n  for (const entry of docs) {\n    outputs.push({\n      type: \"page\",\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  for (const entry of meta) {\n    outputs.push({\n      type: \"meta\",\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  return outputs;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__._runtime),\n/* harmony export */   createMDXSource: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__.createMDXSource),\n/* harmony export */   resolveFiles: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__.resolveFiles)\n/* harmony export */ });\n/* harmony import */ var _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-7SSA5RCV.js */ \"(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtbWR4L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUk2QjtBQUszQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXGZ1bWFkb2NzLW1keFxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgX3J1bnRpbWUsXG4gIGNyZWF0ZU1EWFNvdXJjZSxcbiAgcmVzb2x2ZUZpbGVzXG59IGZyb20gXCIuL2NodW5rLTdTU0E1UkNWLmpzXCI7XG5leHBvcnQge1xuICBfcnVudGltZSxcbiAgY3JlYXRlTURYU291cmNlLFxuICByZXNvbHZlRmlsZXNcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/index.js\n");

/***/ })

};
;