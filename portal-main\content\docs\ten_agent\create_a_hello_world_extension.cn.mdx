---
title: 创建 Hello World 扩展
---

在本章中，我们将逐步创建一个 “Hello World” 扩展，该扩展将以 Python、Go 和 C++ 呈现。您可以随意选择您喜欢的任何语言。准备好了吗？

## 前提条件

在深入本章之前，您需要熟悉[前面介绍的基础知识](./getting_started)。特别是，请确保您了解如何使用 `docker compose up` 并了解后台运行的服务。

## 1. 启动服务器

首先，让我们先启动服务器。运行以下命令：

<Callout>
  如果标题显示“终端”，则表示您正在本地运行命令。如果标题显示“Bash”，则表示您正在
  Docker 容器中运行命令。
</Callout>

```bash title="终端"
docker compose up
```

输入命令后，您应该看到类似于以下内容的输出：

```bash title="终端"
    .... Attaching to ten_agent_dev, ten_agent_playground ten_agent_dev | cd
    agents && tman designer ten_agent_dev | :-) Starting server at
    http://0.0.0.0:49483 ten_agent_playground | ▲ Next.js 14.2.4
    ten_agent_playground | - Local: http://localhost:3000 ten_agent_playground |
    - Network: http://0.0.0.0:3000 ten_agent_playground | ten_agent_playground |
    ✓ Starting... ten_agent_playground | ✓ Ready in 429ms ...
```

现在，我们正在运行以下服务：

• `ten_agent_dev`，地址为 `http://0.0.0.0:49483`（开发服务器）

• `ten_agent_playground`，地址为 `http://localhost:3000` (TEN Agent playground)

## 2. 进入 Docker 容器

要在隔离的环境中工作，请运行以下命令：

```bash title="终端"
docker exec -it ten_agent_dev bash
```

## 3. 创建 Hello World 扩展

通过运行以下命令，将在 Python、Go 或 C++ 中创建一个名为 `hello_world` 的扩展。

import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

<Tabs items={['Python', 'Go', 'C++']} defaultIndex={0}>
  <Tab>

```bash title="终端"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_async_extension_python@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
  <Tab>

```bash title="终端"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_extension_go@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
  <Tab>

```bash title="终端"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_extension_cpp@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
</Tabs>

运行命令后，日志将显示如下内容：

```bash title="终端"
Package 'extension:hello_world' created successfully in '/app' in 3 seconds.
```

## 4. 向扩展添加 API

导航到 `hello_world` 目录并打开 manifest.json。添加带有 `data_in` 和 `cmd_out` 的 API 对象：

```json title="./hello_world/manifest.json"
{
  "type": "extension",
  "name": "hello_world",
  "version": "0.3.1",
  "dependencies": [
    {
      "type": "system",
      "name": "ten_runtime_python",
      "version": "0.3.1"
    }
  ],
  "package": {
    "include": [
      "manifest.json",
      "property.json",
      "BUILD.gn",
      "**.tent",
      "**.py",
      "README.md",
      "tests/**"
    ]
  },
  "api": {
    "data_in": [ // [!code highlight]
      { // [!code highlight]
        "name": "text_data", // [!code highlight]
        "property": { // [!code highlight]
          "text": { // [!code highlight]
            "type": "string" // [!code highlight]
          }, // [!code highlight]
          "is_final": { // [!code highlight]
            "type": "bool" // [!code highlight]
          } // [!code highlight]
        } // [!code highlight]
      } // [!code highlight]
    ], // [!code highlight]
    "cmd_out": [ // [!code highlight]
      { // [!code highlight]
        "name": "flush" // [!code highlight]
      } // [!code highlight]
    ] // [!code highlight]
  }
```

## 5. 构建扩展

让我们使用 `cd /app` 命令返回到项目的根目录，并运行 `make build` 来构建扩展。

```bash title="终端"
cd /app

task use
```

## 6. 验证扩展

恭喜！您已成功创建了第一个 `hello_world` 扩展。
