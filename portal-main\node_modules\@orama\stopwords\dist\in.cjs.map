{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'पर',\n    'इन',\n    'वह',\n    'यिह',\n    'वुह',\n    'जिन्हें',\n    'जिन्हों',\n    'तिन्हें',\n    'तिन्हों',\n    'किन्हों',\n    'किन्हें',\n    'इत्यादि',\n    'द्वारा',\n    'इन्हें',\n    'इन्हों',\n    'उन्हों',\n    'बिलकुल',\n    'निहायत',\n    'ऱ्वासा',\n    'इन्हीं',\n    'उन्हीं',\n    'उन्हें',\n    'इसमें',\n    'जितना',\n    'दुसरा',\n    'कितना',\n    'दबारा',\n    'साबुत',\n    'वग़ैरह',\n    'दूसरे',\n    'कौनसा',\n    'लेकिन',\n    'होता',\n    'करने',\n    'किया',\n    'लिये',\n    'अपने',\n    'नहीं',\n    'दिया',\n    'इसका',\n    'करना',\n    'वाले',\n    'सकते',\n    'इसके',\n    'सबसे',\n    'होने',\n    'करते',\n    'बहुत',\n    'वर्ग',\n    'करें',\n    'होती',\n    'अपनी',\n    'उनके',\n    'कहते',\n    'होते',\n    'करता',\n    'उनकी',\n    'इसकी',\n    'सकता',\n    'रखें',\n    'अपना',\n    'उसके',\n    'जिसे',\n    'तिसे',\n    'किसे',\n    'किसी',\n    'काफ़ी',\n    'पहले',\n    'नीचे',\n    'बाला',\n    'यहाँ',\n    'जैसा',\n    'जैसे',\n    'मानो',\n    'अंदर',\n    'भीतर',\n    'पूरा',\n    'सारा',\n    'होना',\n    'उनको',\n    'वहाँ',\n    'वहीं',\n    'जहाँ',\n    'जीधर',\n    'उनका',\n    'इनका',\n    '﻿के',\n    'हैं',\n    'गया',\n    'बनी',\n    'एवं',\n    'हुआ',\n    'साथ',\n    'बाद',\n    'लिए',\n    'कुछ',\n    'कहा',\n    'यदि',\n    'हुई',\n    'इसे',\n    'हुए',\n    'अभी',\n    'सभी',\n    'कुल',\n    'रहा',\n    'रहे',\n    'इसी',\n    'उसे',\n    'जिस',\n    'जिन',\n    'तिस',\n    'तिन',\n    'कौन',\n    'किस',\n    'कोई',\n    'ऐसे',\n    'तरह',\n    'किर',\n    'साभ',\n    'संग',\n    'यही',\n    'बही',\n    'उसी',\n    'फिर',\n    'मगर',\n    'का',\n    'एक',\n    'यह',\n    'से',\n    'को',\n    'इस',\n    'कि',\n    'जो',\n    'कर',\n    'मे',\n    'ने',\n    'तो',\n    'ही',\n    'या',\n    'हो',\n    'था',\n    'तक',\n    'आप',\n    'ये',\n    'थे',\n    'दो',\n    'वे',\n    'थी',\n    'जा',\n    'ना',\n    'उस',\n    'एस',\n    'पे',\n    'उन',\n    'सो',\n    'भी',\n    'और',\n    'घर',\n    'तब',\n    'जब',\n    'अत',\n    'व',\n    'न'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,KACA,KACA,KACA,MACA,MACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,WACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA,IACH"}