---
title: 核心概念
---

## 模块化

1. **明确的模块边界**：框架支持清晰定义控制平面和数据平面的边界，这些边界可以静态声明。这便于使用离线工具进行检查和后处理。

2. **动态服务组合**：进程内的模块可以在运行时动态组合，无需静态编译，实现灵活的服务配置。

3. **简化的模块集成**：在多模块组合场景中，只需要声明即可，无需修改任何模块代码或编写胶水代码。

4. **去中心化的模块耦合**：架构允许不同团队独立开发模块，模块间无耦合，支持动态替换。

5. **灵活的模块接口通信**：支持两种类型的接口通信——一种要求双方参数一致，另一种允许参数差异并由平台提供桥接机制。这种灵活性对于独立的模块开发和部署至关重要。

## 编排

1. **有向循环图（DCG）拓扑**：支持复杂的图结构进行模块组合，在控制平面和数据平面都能实现 1 对 n、n 对 1 和 n 对 m 的交互关系。

2. **跨进程和线程编排**：支持跨不同进程和线程的模块实例协调，形成完整的服务场景。

   ![跨进程和线程编排](https://ten-framework-assets.s3.amazonaws.com/doc-assets/cross_process_and_thread_orchestration.png)

3. **动态编排配置**：编排可以在运行时动态配置，也可以在服务执行前预配置。

## 灵活的配置设置

1. **层级配置支持**：不同模块实例可以有不同的配置可能性，能够适应不同的层级上下文。

2. **多样化配置能力**：配置可以预设、在运行时动态更改或作为更新分发。

## 多样的发布能力

1. **支持二进制和源码发布**：模块可以以源码或二进制形式分发，同一模块可以同时支持这两种模式。

2. **云市场集成**：无论是源码还是二进制形式的模块，都可以发布到云市场，便于下载和集成到项目中。

## 多实例能力

1. **平台多实例**：平台避免使用全局变量，支持在单个业务进程中运行多个实例。

2. **模块多实例**：模块开发时不依赖全局变量，允许同一模块的多个实例无冲突运行。

3. **编排流水线多实例**：支持在单个进程中并发或顺序执行多个编排流水线。

## 多样的分发能力

1. **分发配置动态切换**：模块可以跨进程和线程分发，以实现高可用、高并发和便捷的热更新。

2. **分发配置解耦**：分发配置与模块代码和编译/构建过程解耦，简化部署。

## 灵活的输入/输出支持

1. **支持控制平面和数据平面**：框架支持模块间的命令调用和原始数据（音频/视频/数据）传输。

2. **支持跨进程和线程通信**：模块可以同时处理跨进程和线程的外部输入/输出操作。

3. **RPC 特性**：外部输入/输出操作具有 RPC 特性，实现跨进程模块的无缝集成。

4. **触发多个下游组件**：单个动作可以触发多个下游模块，并将聚合响应返回给原始模块。

5. **声明式外部输入/输出**：接口可以静态声明，支持离线工具进行静态检查。

6. **简化的外部输入/输出代码**：函数调用接口（理想情况下自动生成）简化了模块开发者的编码工作。

7. **支持非序列化数据**：架构支持传输非序列化数据，工具可自动识别和标记此类用法。

8. **透明的跨进程通信**：平台抽象了底层传输逻辑，简化跨进程交互。

9. **支持同步和异步控制平面操作**：控制平面支持同步和异步交互，允许双向命令和响应流。

10. **支持受限执行上下文**：支持同一线程内模块间的同步交互。

## 模块开发自由度

1. **使用第三方库的自由**：模块可以自由使用第三方库来实现所需功能。

2. **使用操作系统特性的自由**：开发者可以在模块内自由创建和终止原生线程，提供最大的开发灵活性。

## 编程语言支持

1. **支持多种编程语言**：框架支持使用多种语言（如 C/C++、Go、Java、Python）编写模块，实现多样化的开发环境。

   ![编程语言支持](https://ten-framework-assets.s3.amazonaws.com/doc-assets/various_language_support.png)

2. **跨语言模块集成**：使用不同语言编写的模块可以在同一进程中运行。

3. **跨语言统一交互**：模块在不同语言中具有一致的接口和行为。

4. **跨语言统一数据类型**：平台提供统一的数据类型，确保不同编程语言间的数据交换一致性。

## 丰富的外部交互能力

1. **UI 提供者**：架构支持后端服务为前端提供 UI 组件。

2. **RESTful 接口**：后端服务可以暴露 RESTful 接口用于客户端交互。

3. **无侵入式集成现有服务**：框架可以集成到现有服务中，无需完全重写。

4. **单线程运行**：框架可以作为独立进程运行，或作为现有服务中的一个线程运行，避免与现有业务逻辑冲突。

## 测试友好

1. **多样化测试机制**：框架支持多种测试策略，包括单元测试、集成测试、模块测试和黑盒测试。

2. **独立模块测试**：模块可以独立测试，便于进行全面有效的测试。

## 离线处理能力

1. **包管理器**：框架包含 CLI 和包管理器，用于管理模块及其组合。

2. **编排工具**：提供 GUI 工具用于离线开发和编排，提升开发者体验。

3. **可视化调试工具**：架构支持可视化调试工具，帮助可视化模块交互。

## 健壮性

1. **编排流水线高并发**：平台支持单进程内至少 100 个编排流水线的并发实例。

2. **RESTful 接口高并发**：平台可在单进程中支持超过 20,000 个 RESTful 客户端。

3. **非 RESTful 接口高并发**：平台可在单进程中支持超过 1,000 个非 RESTful 客户端。
