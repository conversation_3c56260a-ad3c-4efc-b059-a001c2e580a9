{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'ahaṃ',\n    'tava',\n    'tvayi',\n    'svayam',\n    'vayam',\n    'asmān',\n    'nas',\n    'yat',\n    'yaḥ',\n    'kiṃ',\n    'kaḥ',\n    'saḥ',\n    'taḥ',\n    'tasya',\n    'tasmai',\n    'asya',\n    'tat',\n    'tad',\n    'tatra',\n    'katham',\n    'yadi',\n    'vā',\n    'athavā',\n    'evaṃ',\n    'na',\n    'api',\n    'atha',\n    'sama',\n    'santu',\n    'antaḥ',\n    'antar',\n    'ubhau'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,OACA,OACA,QACA,SACA,QACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,SACA,OACA,MACA,MACA,QACA,SACA,OACA,KACA,SACA,OACA,KACA,MACA,OACA,OACA,QACA,QACA,QACA,QACH,AAAC"}