---
title: Overview
---

In the TEN ecosystem, almost everything is a component. For example, a TEN app is a type of component, a TEN extension is another type, and a TEN protocol is yet another. To assist TEN developers in managing all operations related to these components, there is a crucial tool called the TEN Manager.

<Callout type="info">`tman` is the abbreviation for TEN Manager.</Callout>

The TEN Manager includes a TEN package manager, designed to simplify the management of TEN packages. This package manager handles tasks such as determining which dependency packages are necessary, fetching those dependencies, and installing them. In short, the TEN Manager streamlines the process of creating new TEN packages from scratch and makes it easier to install TEN packages from the TEN Cloud Store.
