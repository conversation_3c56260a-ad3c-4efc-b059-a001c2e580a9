{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'αλλα',\n    'αν',\n    'αντι',\n    'απο',\n    'αυτα',\n    'αυτεσ',\n    'αυτη',\n    'αυτο',\n    'αυτοι',\n    'αυτοσ',\n    'αυτουσ',\n    'αυτων',\n    'για',\n    'δε',\n    'δεν',\n    'εαν',\n    'ειμαι',\n    'ειμαστε',\n    'ειναι',\n    'εισαι',\n    'ειστε',\n    'εκεινα',\n    'εκεινεσ',\n    'εκεινη',\n    'εκεινο',\n    'εκεινοι',\n    'εκεινοσ',\n    'εκεινουσ',\n    'εκεινων',\n    'ενω',\n    'επι',\n    'η',\n    'θα',\n    'ισωσ',\n    'κ',\n    'και',\n    'κατα',\n    'κι',\n    'μα',\n    'με',\n    'μετα',\n    'μη',\n    'μην',\n    'να',\n    'ο',\n    'οι',\n    'ομωσ',\n    'οπωσ',\n    'οσο',\n    'οτι',\n    'παρα',\n    'ποια',\n    'ποιεσ',\n    'ποιο',\n    'ποιοι',\n    'ποιοσ',\n    'ποιουσ',\n    'ποιων',\n    'που',\n    'προσ',\n    'πωσ',\n    'σε',\n    'στη',\n    'στην',\n    'στο',\n    'στον',\n    'τα',\n    'την',\n    'τησ',\n    'το',\n    'τον',\n    'τοτε',\n    'του',\n    'των',\n    'ωσ'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,OACA,KACA,OACA,MACA,OACA,QACA,OACA,OACA,QACA,QACA,SACA,QACA,MACA,KACA,MACA,MACA,QACA,UACA,QACA,QACA,QACA,SACA,UACA,SACA,SACA,UACA,UACA,WACA,UACA,MACA,MACA,IACA,KACA,OACA,IACA,MACA,OACA,KACA,KACA,KACA,OACA,KACA,MACA,KACA,IACA,KACA,OACA,OACA,MACA,MACA,OACA,OACA,QACA,OACA,QACA,QACA,SACA,QACA,MACA,OACA,MACA,KACA,MACA,OACA,MACA,OACA,KACA,MACA,MACA,KACA,MACA,OACA,MACA,MACA,KACH"}