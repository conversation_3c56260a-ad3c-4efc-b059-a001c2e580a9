{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'og',\n    'i',\n    'jeg',\n    'det',\n    'at',\n    'en',\n    'et',\n    'den',\n    'til',\n    'er',\n    'som',\n    'på',\n    'de',\n    'med',\n    'han',\n    'av',\n    'ikke',\n    'ikkje',\n    'der',\n    'så',\n    'var',\n    'meg',\n    'seg',\n    'men',\n    'ett',\n    'har',\n    'om',\n    'vi',\n    'min',\n    'mitt',\n    'ha',\n    'hadde',\n    'hun',\n    'nå',\n    'over',\n    'da',\n    'ved',\n    'fra',\n    'du',\n    'ut',\n    'sin',\n    'dem',\n    'oss',\n    'opp',\n    'man',\n    'kan',\n    'hans',\n    'hvor',\n    'eller',\n    'hva',\n    'skal',\n    'selv',\n    'sjøl',\n    'her',\n    'alle',\n    'vil',\n    'bli',\n    'ble',\n    'blei',\n    'blitt',\n    'kunne',\n    'inn',\n    'når',\n    'være',\n    'kom',\n    'noen',\n    'noe',\n    'ville',\n    'dere',\n    'som',\n    'deres',\n    'kun',\n    'ja',\n    'etter',\n    'ned',\n    'skulle',\n    'denne',\n    'for',\n    'deg',\n    'si',\n    'sine',\n    'sitt',\n    'mot',\n    'å',\n    'meget',\n    'hvorfor',\n    'dette',\n    'disse',\n    'uten',\n    'hvordan',\n    'ingen',\n    'din',\n    'ditt',\n    'blir',\n    'samme',\n    'hvilken',\n    'hvilke',\n    'sånn',\n    'inni',\n    'mellom',\n    'vår',\n    'hver',\n    'hvem',\n    'vors',\n    'hvis',\n    'både',\n    'bare',\n    'enn',\n    'fordi',\n    'før',\n    'mange',\n    'også',\n    'slik',\n    'vært',\n    'være',\n    'båe',\n    'begge',\n    'siden',\n    'dykk',\n    'dykkar',\n    'dei',\n    'deira',\n    'deires',\n    'deim',\n    'di',\n    'då',\n    'eg',\n    'ein',\n    'eit',\n    'eitt',\n    'elles',\n    'honom',\n    'hjå',\n    'ho',\n    'hoe',\n    'henne',\n    'hennar',\n    'hennes',\n    'hoss',\n    'hossen',\n    'ikkje',\n    'ingi',\n    'inkje',\n    'korleis',\n    'korso',\n    'kva',\n    'kvar',\n    'kvarhelst',\n    'kven',\n    'kvi',\n    'kvifor',\n    'me',\n    'medan',\n    'mi',\n    'mine',\n    'mykje',\n    'no',\n    'nokon',\n    'noka',\n    'nokor',\n    'noko',\n    'nokre',\n    'si',\n    'sia',\n    'sidan',\n    'so',\n    'somt',\n    'somme',\n    'um',\n    'upp',\n    'vere',\n    'vore',\n    'verte',\n    'vort',\n    'varte',\n    'vart'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,KACA,IACA,MACA,MACA,KACA,KACA,KACA,MACA,MACA,KACA,MACA,QACA,KACA,MACA,MACA,KACA,OACA,QACA,MACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,KACA,KACA,MACA,OACA,KACA,QACA,MACA,QACA,OACA,KACA,MACA,MACA,KACA,KACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,QACA,MACA,OACA,OACA,UACA,MACA,OACA,MACA,MACA,MACA,OACA,QACA,QACA,MACA,SACA,UACA,MACA,OACA,MACA,QACA,OACA,MACA,QACA,MACA,KACA,QACA,MACA,SACA,QACA,MACA,MACA,KACA,OACA,OACA,MACA,OACA,QACA,UACA,QACA,QACA,OACA,UACA,QACA,MACA,OACA,OACA,QACA,UACA,SACA,UACA,OACA,SACA,SACA,OACA,OACA,OACA,OACA,UACA,OACA,MACA,QACA,SACA,QACA,UACA,OACA,UACA,UACA,SACA,QACA,QACA,OACA,SACA,MACA,QACA,SACA,OACA,KACA,QACA,KACA,MACA,MACA,OACA,QACA,QACA,SACA,KACA,MACA,QACA,SACA,SACA,OACA,SACA,QACA,OACA,QACA,UACA,QACA,MACA,OACA,YACA,OACA,MACA,SACA,KACA,QACA,KACA,OACA,QACA,KACA,QACA,OACA,QACA,OACA,QACA,KACA,MACA,QACA,KACA,OACA,QACA,KACA,MACA,OACA,OACA,QACA,OACA,QACA,OACH,AAAC"}