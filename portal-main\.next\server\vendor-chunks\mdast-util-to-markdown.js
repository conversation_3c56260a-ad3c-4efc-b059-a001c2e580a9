"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/configure.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configure: () => (/* binding */ configure)\n/* harmony export */ });\n/**\n * @import {Options, State} from './types.js'\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nfunction configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'extensions': {\n          // Empty.\n          break\n        }\n\n        /* c8 ignore next 4 */\n        case 'unsafe': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'join': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'handlers': {\n          map(base[key], extension[key])\n          break\n        }\n\n        default: {\n          // @ts-expect-error: matches.\n          base.options[key] = extension[key]\n        }\n      }\n    }\n  }\n\n  return base\n}\n\n/**\n * @template T\n * @param {Array<T>} left\n * @param {Array<T> | null | undefined} right\n */\nfunction list(left, right) {\n  if (right) {\n    left.push(...right)\n  }\n}\n\n/**\n * @template T\n * @param {Record<string, T>} left\n * @param {Record<string, T> | null | undefined} right\n */\nfunction map(left, right) {\n  if (right) {\n    Object.assign(left, right)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2Jsb2NrcXVvdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxxQkFBcUI7QUFDakMsWUFBWSxrQkFBa0I7QUFDOUI7O0FBRUE7QUFDQSxXQUFXLFlBQVk7QUFDdkIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxLQUFLO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFxibG9ja3F1b3RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QmxvY2txdW90ZSwgUGFyZW50c30gZnJvbSAnbWRhc3QnXG4gKiBAaW1wb3J0IHtJbmZvLCBNYXAsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtCbG9ja3F1b3RlfSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBibG9ja3F1b3RlKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcignYmxvY2txdW90ZScpXG4gIGNvbnN0IHRyYWNrZXIgPSBzdGF0ZS5jcmVhdGVUcmFja2VyKGluZm8pXG4gIHRyYWNrZXIubW92ZSgnPiAnKVxuICB0cmFja2VyLnNoaWZ0KDIpXG4gIGNvbnN0IHZhbHVlID0gc3RhdGUuaW5kZW50TGluZXMoXG4gICAgc3RhdGUuY29udGFpbmVyRmxvdyhub2RlLCB0cmFja2VyLmN1cnJlbnQoKSksXG4gICAgbWFwXG4gIClcbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKiogQHR5cGUge01hcH0gKi9cbmZ1bmN0aW9uIG1hcChsaW5lLCBfLCBibGFuaykge1xuICByZXR1cm4gJz4nICsgKGJsYW5rID8gJycgOiAnICcpICsgbGluZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1QixZQUFZLGFBQWE7QUFDekI7O0FBRTBEOztBQUUxRDtBQUNBLFdBQVcsT0FBTztBQUNsQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0seUVBQWM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFxicmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0JyZWFrLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbmltcG9ydCB7cGF0dGVybkluU2NvcGV9IGZyb20gJy4uL3V0aWwvcGF0dGVybi1pbi1zY29wZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFyZEJyZWFrKF8sIF8xLCBzdGF0ZSwgaW5mbykge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgc3RhdGUudW5zYWZlLmxlbmd0aCkge1xuICAgIC8vIElmIHdlIGNhbuKAmXQgcHV0IGVvbHMgaW4gdGhpcyBjb25zdHJ1Y3QgKHNldGV4dCBoZWFkaW5ncywgdGFibGVzKSwgdXNlIGFcbiAgICAvLyBzcGFjZSBpbnN0ZWFkLlxuICAgIGlmIChcbiAgICAgIHN0YXRlLnVuc2FmZVtpbmRleF0uY2hhcmFjdGVyID09PSAnXFxuJyAmJlxuICAgICAgcGF0dGVybkluU2NvcGUoc3RhdGUuc3RhY2ssIHN0YXRlLnVuc2FmZVtpbmRleF0pXG4gICAgKSB7XG4gICAgICByZXR1cm4gL1sgXFx0XS8udGVzdChpbmZvLmJlZm9yZSkgPyAnJyA6ICcgJ1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAnXFxcXFxcbidcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(rsc)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\n\n\n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction code(node, _, state, info) {\n  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\n\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction definition(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\n\n\n\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction emphasis(node, _, state, info) {\n  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nfunction html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxNQUFNO0FBQ2xCOztBQUVBOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFxodG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaHRtbC5wZWVrID0gaHRtbFBlZWtcblxuLyoqXG4gKiBAcGFyYW0ge0h0bWx9IG5vZGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBodG1sKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUudmFsdWUgfHwgJydcbn1cblxuLyoqXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5mdW5jdGlvbiBodG1sUGVlaygpIHtcbiAgcmV0dXJuICc8J1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\n\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction image(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */\nconst handle = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\n\n\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction link(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\n\n\n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGFBQWE7QUFDekIsWUFBWSxvQkFBb0I7QUFDaEM7O0FBRUE7QUFDQSxXQUFXLFdBQVc7QUFDdEIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxoYW5kbGVcXHBhcmFncmFwaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyYWdyYXBoLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7UGFyYWdyYXBofSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdwYXJhZ3JhcGgnKVxuICBjb25zdCBzdWJleGl0ID0gc3RhdGUuZW50ZXIoJ3BocmFzaW5nJylcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5jb250YWluZXJQaHJhc2luZyhub2RlLCBpbmZvKVxuICBzdWJleGl0KClcbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(rsc)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\n\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6QixZQUFZLGVBQWU7QUFDM0I7O0FBRTRDOztBQUU1QztBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBLFdBQVcsNkRBQVE7QUFDbkIsR0FBRzs7QUFFSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxccm9vdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgUm9vdH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtwaHJhc2luZ30gZnJvbSAnbWRhc3QtdXRpbC1waHJhc2luZydcblxuLyoqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgLy8gTm90ZTogYGh0bWxgIG5vZGVzIGFyZSBhbWJpZ3VvdXMuXG4gIGNvbnN0IGhhc1BocmFzaW5nID0gbm9kZS5jaGlsZHJlbi5zb21lKGZ1bmN0aW9uIChkKSB7XG4gICAgcmV0dXJuIHBocmFzaW5nKGQpXG4gIH0pXG5cbiAgY29uc3QgY29udGFpbmVyID0gaGFzUGhyYXNpbmcgPyBzdGF0ZS5jb250YWluZXJQaHJhc2luZyA6IHN0YXRlLmNvbnRhaW5lckZsb3dcbiAgcmV0dXJuIGNvbnRhaW5lci5jYWxsKHN0YXRlLCBub2RlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\n\n\n\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction strong(node, _, state, info) {\n  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxhQUFhO0FBQ3pCLFlBQVksZUFBZTtBQUMzQjs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFx0ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBUZXh0fSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7VGV4dH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICByZXR1cm4gc3RhdGUuc2FmZShub2RlLnZhbHVlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction thematicBreak(_, _1, state) {\n  const value = (\n    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksd0JBQXdCO0FBQ3BDOztBQUVvRTtBQUNyQjs7QUFFL0M7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSxJQUFJLDhEQUFTO0FBQ2IsV0FBVyxtRkFBbUI7O0FBRTlCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxcdGhlbWF0aWMtYnJlYWsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFRoZW1hdGljQnJlYWt9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7Y2hlY2tSdWxlUmVwZXRpdGlvbn0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMnXG5pbXBvcnQge2NoZWNrUnVsZX0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gX1xuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aGVtYXRpY0JyZWFrKF8sIF8xLCBzdGF0ZSkge1xuICBjb25zdCB2YWx1ZSA9IChcbiAgICBjaGVja1J1bGUoc3RhdGUpICsgKHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/ICcgJyA6ICcnKVxuICApLnJlcGVhdChjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSlcblxuICByZXR1cm4gc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gdmFsdWUuc2xpY2UoMCwgLTEpIDogdmFsdWVcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toMarkdown: () => (/* binding */ toMarkdown)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zwitch */ \"(rsc)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _configure_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./configure.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handle/index.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/* harmony import */ var _join_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./join.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js\");\n/* harmony import */ var _unsafe_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./unsafe.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\");\n/* harmony import */ var _util_association_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/association.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js\");\n/* harmony import */ var _util_compile_pattern_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/compile-pattern.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js\");\n/* harmony import */ var _util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./util/container-phrasing.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var _util_container_flow_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./util/container-flow.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\");\n/* harmony import */ var _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util/indent-lines.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\");\n/* harmony import */ var _util_safe_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/safe.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\");\n/* harmony import */ var _util_track_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/track.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @import {Info, Join, Options, SafeConfig, State} from 'mdast-util-to-markdown'\n * @import {Nodes} from 'mdast'\n * @import {Enter, FlowParents, PhrasingParents, TrackFields} from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Turn an mdast syntax tree into markdown.\n *\n * @param {Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized markdown representing `tree`.\n */\nfunction toMarkdown(tree, options) {\n  const settings = options || {}\n  /** @type {State} */\n  const state = {\n    associationId: _util_association_js__WEBPACK_IMPORTED_MODULE_0__.association,\n    containerPhrasing: containerPhrasingBound,\n    containerFlow: containerFlowBound,\n    createTracker: _util_track_js__WEBPACK_IMPORTED_MODULE_1__.track,\n    compilePattern: _util_compile_pattern_js__WEBPACK_IMPORTED_MODULE_2__.compilePattern,\n    enter,\n    // @ts-expect-error: GFM / frontmatter are typed in `mdast` but not defined\n    // here.\n    handlers: {..._handle_index_js__WEBPACK_IMPORTED_MODULE_3__.handle},\n    // @ts-expect-error: add `handle` in a second.\n    handle: undefined,\n    indentLines: _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__.indentLines,\n    indexStack: [],\n    join: [..._join_js__WEBPACK_IMPORTED_MODULE_5__.join],\n    options: {},\n    safe: safeBound,\n    stack: [],\n    unsafe: [..._unsafe_js__WEBPACK_IMPORTED_MODULE_6__.unsafe]\n  }\n\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_7__.configure)(state, settings)\n\n  if (state.options.tightDefinitions) {\n    state.join.push(joinDefinition)\n  }\n\n  state.handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_8__.zwitch)('type', {\n    invalid,\n    unknown,\n    handlers: state.handlers\n  })\n\n  let result = state.handle(tree, undefined, state, {\n    before: '\\n',\n    after: '\\n',\n    now: {line: 1, column: 1},\n    lineShift: 0\n  })\n\n  if (\n    result &&\n    result.charCodeAt(result.length - 1) !== 10 &&\n    result.charCodeAt(result.length - 1) !== 13\n  ) {\n    result += '\\n'\n  }\n\n  return result\n\n  /** @type {Enter} */\n  function enter(name) {\n    state.stack.push(name)\n    return exit\n\n    /**\n     * @returns {undefined}\n     */\n    function exit() {\n      state.stack.pop()\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction unknown(value) {\n  // Always a node.\n  const node = /** @type {Nodes} */ (value)\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/** @type {Join} */\nfunction joinDefinition(left, right) {\n  // No blank line between adjacent definitions.\n  if (left.type === 'definition' && left.type === right.type) {\n    return 0\n  }\n}\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasingBound(parent, info) {\n  return (0,_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_9__.containerPhrasing)(parent, this, info)\n}\n\n/**\n * Serialize the children of a parent that contains flow children.\n *\n * These children will typically be joined by blank lines.\n * What they are joined by exactly is defined by `Join` functions.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlowBound(parent, info) {\n  return (0,_util_container_flow_js__WEBPACK_IMPORTED_MODULE_10__.containerFlow)(parent, this, info)\n}\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} value\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safeBound(value, config) {\n  return (0,_util_safe_js__WEBPACK_IMPORTED_MODULE_11__.safe)(this, value, config)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0EsWUFBWSx3Q0FBd0M7QUFDcEQsWUFBWSxPQUFPO0FBQ25CLFlBQVksa0RBQWtEO0FBQzlEOztBQUU2QjtBQUNXO0FBQ1k7QUFDdEI7QUFDSTtBQUNlO0FBQ087QUFDTTtBQUNSO0FBQ0o7QUFDZjtBQUNFOztBQUVyQztBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLDRCQUE0QjtBQUN2QztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBLG1CQUFtQiw2REFBVztBQUM5QjtBQUNBO0FBQ0EsbUJBQW1CLGlEQUFLO0FBQ3hCLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQSxlQUFlLEdBQUcsb0RBQVEsQ0FBQztBQUMzQjtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0EsY0FBYywwQ0FBSTtBQUNsQixlQUFlO0FBQ2Y7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBTTtBQUN0Qjs7QUFFQSxFQUFFLHlEQUFTOztBQUVYO0FBQ0E7QUFDQTs7QUFFQSxpQkFBaUIsOENBQU07QUFDdkI7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQSxVQUFVLG1CQUFtQjtBQUM3QjtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsYUFBYSxPQUFPO0FBQ3BCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsT0FBTztBQUNqQztBQUNBOztBQUVBLFdBQVcsTUFBTTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFdBQVcsaUJBQWlCO0FBQzVCO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOEVBQWlCO0FBQzFCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFdBQVcsYUFBYTtBQUN4QjtBQUNBLFdBQVcsYUFBYTtBQUN4QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTLHVFQUFhO0FBQ3RCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsV0FBVywyQkFBMkI7QUFDdEM7QUFDQSxXQUFXLFlBQVk7QUFDdkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsU0FBUyxvREFBSTtBQUNiIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIEpvaW4sIE9wdGlvbnMsIFNhZmVDb25maWcsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7Tm9kZXN9IGZyb20gJ21kYXN0J1xuICogQGltcG9ydCB7RW50ZXIsIEZsb3dQYXJlbnRzLCBQaHJhc2luZ1BhcmVudHMsIFRyYWNrRmllbGRzfSBmcm9tICcuL3R5cGVzLmpzJ1xuICovXG5cbmltcG9ydCB7endpdGNofSBmcm9tICd6d2l0Y2gnXG5pbXBvcnQge2NvbmZpZ3VyZX0gZnJvbSAnLi9jb25maWd1cmUuanMnXG5pbXBvcnQge2hhbmRsZSBhcyBoYW5kbGVyc30gZnJvbSAnLi9oYW5kbGUvaW5kZXguanMnXG5pbXBvcnQge2pvaW59IGZyb20gJy4vam9pbi5qcydcbmltcG9ydCB7dW5zYWZlfSBmcm9tICcuL3Vuc2FmZS5qcydcbmltcG9ydCB7YXNzb2NpYXRpb259IGZyb20gJy4vdXRpbC9hc3NvY2lhdGlvbi5qcydcbmltcG9ydCB7Y29tcGlsZVBhdHRlcm59IGZyb20gJy4vdXRpbC9jb21waWxlLXBhdHRlcm4uanMnXG5pbXBvcnQge2NvbnRhaW5lclBocmFzaW5nfSBmcm9tICcuL3V0aWwvY29udGFpbmVyLXBocmFzaW5nLmpzJ1xuaW1wb3J0IHtjb250YWluZXJGbG93fSBmcm9tICcuL3V0aWwvY29udGFpbmVyLWZsb3cuanMnXG5pbXBvcnQge2luZGVudExpbmVzfSBmcm9tICcuL3V0aWwvaW5kZW50LWxpbmVzLmpzJ1xuaW1wb3J0IHtzYWZlfSBmcm9tICcuL3V0aWwvc2FmZS5qcydcbmltcG9ydCB7dHJhY2t9IGZyb20gJy4vdXRpbC90cmFjay5qcydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IHN5bnRheCB0cmVlIGludG8gbWFya2Rvd24uXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gdHJlZVxuICogICBUcmVlIHRvIHNlcmlhbGl6ZS5cbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG1hcmtkb3duIHJlcHJlc2VudGluZyBgdHJlZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b01hcmtkb3duKHRyZWUsIG9wdGlvbnMpIHtcbiAgY29uc3Qgc2V0dGluZ3MgPSBvcHRpb25zIHx8IHt9XG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGNvbnN0IHN0YXRlID0ge1xuICAgIGFzc29jaWF0aW9uSWQ6IGFzc29jaWF0aW9uLFxuICAgIGNvbnRhaW5lclBocmFzaW5nOiBjb250YWluZXJQaHJhc2luZ0JvdW5kLFxuICAgIGNvbnRhaW5lckZsb3c6IGNvbnRhaW5lckZsb3dCb3VuZCxcbiAgICBjcmVhdGVUcmFja2VyOiB0cmFjayxcbiAgICBjb21waWxlUGF0dGVybixcbiAgICBlbnRlcixcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBHRk0gLyBmcm9udG1hdHRlciBhcmUgdHlwZWQgaW4gYG1kYXN0YCBidXQgbm90IGRlZmluZWRcbiAgICAvLyBoZXJlLlxuICAgIGhhbmRsZXJzOiB7Li4uaGFuZGxlcnN9LFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IGFkZCBgaGFuZGxlYCBpbiBhIHNlY29uZC5cbiAgICBoYW5kbGU6IHVuZGVmaW5lZCxcbiAgICBpbmRlbnRMaW5lcyxcbiAgICBpbmRleFN0YWNrOiBbXSxcbiAgICBqb2luOiBbLi4uam9pbl0sXG4gICAgb3B0aW9uczoge30sXG4gICAgc2FmZTogc2FmZUJvdW5kLFxuICAgIHN0YWNrOiBbXSxcbiAgICB1bnNhZmU6IFsuLi51bnNhZmVdXG4gIH1cblxuICBjb25maWd1cmUoc3RhdGUsIHNldHRpbmdzKVxuXG4gIGlmIChzdGF0ZS5vcHRpb25zLnRpZ2h0RGVmaW5pdGlvbnMpIHtcbiAgICBzdGF0ZS5qb2luLnB1c2goam9pbkRlZmluaXRpb24pXG4gIH1cblxuICBzdGF0ZS5oYW5kbGUgPSB6d2l0Y2goJ3R5cGUnLCB7XG4gICAgaW52YWxpZCxcbiAgICB1bmtub3duLFxuICAgIGhhbmRsZXJzOiBzdGF0ZS5oYW5kbGVyc1xuICB9KVxuXG4gIGxldCByZXN1bHQgPSBzdGF0ZS5oYW5kbGUodHJlZSwgdW5kZWZpbmVkLCBzdGF0ZSwge1xuICAgIGJlZm9yZTogJ1xcbicsXG4gICAgYWZ0ZXI6ICdcXG4nLFxuICAgIG5vdzoge2xpbmU6IDEsIGNvbHVtbjogMX0sXG4gICAgbGluZVNoaWZ0OiAwXG4gIH0pXG5cbiAgaWYgKFxuICAgIHJlc3VsdCAmJlxuICAgIHJlc3VsdC5jaGFyQ29kZUF0KHJlc3VsdC5sZW5ndGggLSAxKSAhPT0gMTAgJiZcbiAgICByZXN1bHQuY2hhckNvZGVBdChyZXN1bHQubGVuZ3RoIC0gMSkgIT09IDEzXG4gICkge1xuICAgIHJlc3VsdCArPSAnXFxuJ1xuICB9XG5cbiAgcmV0dXJuIHJlc3VsdFxuXG4gIC8qKiBAdHlwZSB7RW50ZXJ9ICovXG4gIGZ1bmN0aW9uIGVudGVyKG5hbWUpIHtcbiAgICBzdGF0ZS5zdGFjay5wdXNoKG5hbWUpXG4gICAgcmV0dXJuIGV4aXRcblxuICAgIC8qKlxuICAgICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAgICovXG4gICAgZnVuY3Rpb24gZXhpdCgpIHtcbiAgICAgIHN0YXRlLnN0YWNrLnBvcCgpXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQHBhcmFtIHt1bmtub3dufSB2YWx1ZVxuICogQHJldHVybnMge25ldmVyfVxuICovXG5mdW5jdGlvbiBpbnZhbGlkKHZhbHVlKSB7XG4gIHRocm93IG5ldyBFcnJvcignQ2Fubm90IGhhbmRsZSB2YWx1ZSBgJyArIHZhbHVlICsgJ2AsIGV4cGVjdGVkIG5vZGUnKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7dW5rbm93bn0gdmFsdWVcbiAqIEByZXR1cm5zIHtuZXZlcn1cbiAqL1xuZnVuY3Rpb24gdW5rbm93bih2YWx1ZSkge1xuICAvLyBBbHdheXMgYSBub2RlLlxuICBjb25zdCBub2RlID0gLyoqIEB0eXBlIHtOb2Rlc30gKi8gKHZhbHVlKVxuICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBoYW5kbGUgdW5rbm93biBub2RlIGAnICsgbm9kZS50eXBlICsgJ2AnKVxufVxuXG4vKiogQHR5cGUge0pvaW59ICovXG5mdW5jdGlvbiBqb2luRGVmaW5pdGlvbihsZWZ0LCByaWdodCkge1xuICAvLyBObyBibGFuayBsaW5lIGJldHdlZW4gYWRqYWNlbnQgZGVmaW5pdGlvbnMuXG4gIGlmIChsZWZ0LnR5cGUgPT09ICdkZWZpbml0aW9uJyAmJiBsZWZ0LnR5cGUgPT09IHJpZ2h0LnR5cGUpIHtcbiAgICByZXR1cm4gMFxuICB9XG59XG5cbi8qKlxuICogU2VyaWFsaXplIHRoZSBjaGlsZHJlbiBvZiBhIHBhcmVudCB0aGF0IGNvbnRhaW5zIHBocmFzaW5nIGNoaWxkcmVuLlxuICpcbiAqIFRoZXNlIGNoaWxkcmVuIHdpbGwgYmUgam9pbmVkIGZsdXNoIHRvZ2V0aGVyLlxuICpcbiAqIEB0aGlzIHtTdGF0ZX1cbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHBhcmFtIHtQaHJhc2luZ1BhcmVudHN9IHBhcmVudFxuICogICBQYXJlbnQgb2YgZmxvdyBub2Rlcy5cbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogICBJbmZvIG9uIHdoZXJlIHdlIGFyZSBpbiB0aGUgZG9jdW1lbnQgd2UgYXJlIGdlbmVyYXRpbmcuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIGNoaWxkcmVuLCBqb2luZWQgdG9nZXRoZXIuXG4gKi9cbmZ1bmN0aW9uIGNvbnRhaW5lclBocmFzaW5nQm91bmQocGFyZW50LCBpbmZvKSB7XG4gIHJldHVybiBjb250YWluZXJQaHJhc2luZyhwYXJlbnQsIHRoaXMsIGluZm8pXG59XG5cbi8qKlxuICogU2VyaWFsaXplIHRoZSBjaGlsZHJlbiBvZiBhIHBhcmVudCB0aGF0IGNvbnRhaW5zIGZsb3cgY2hpbGRyZW4uXG4gKlxuICogVGhlc2UgY2hpbGRyZW4gd2lsbCB0eXBpY2FsbHkgYmUgam9pbmVkIGJ5IGJsYW5rIGxpbmVzLlxuICogV2hhdCB0aGV5IGFyZSBqb2luZWQgYnkgZXhhY3RseSBpcyBkZWZpbmVkIGJ5IGBKb2luYCBmdW5jdGlvbnMuXG4gKlxuICogQHRoaXMge1N0YXRlfVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcGFyYW0ge0Zsb3dQYXJlbnRzfSBwYXJlbnRcbiAqICAgUGFyZW50IG9mIGZsb3cgbm9kZXMuXG4gKiBAcGFyYW0ge1RyYWNrRmllbGRzfSBpbmZvXG4gKiAgIEluZm8gb24gd2hlcmUgd2UgYXJlIGluIHRoZSBkb2N1bWVudCB3ZSBhcmUgZ2VuZXJhdGluZy5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgY2hpbGRyZW4sIGpvaW5lZCBieSAoYmxhbmspIGxpbmVzLlxuICovXG5mdW5jdGlvbiBjb250YWluZXJGbG93Qm91bmQocGFyZW50LCBpbmZvKSB7XG4gIHJldHVybiBjb250YWluZXJGbG93KHBhcmVudCwgdGhpcywgaW5mbylcbn1cblxuLyoqXG4gKiBNYWtlIGEgc3RyaW5nIHNhZmUgZm9yIGVtYmVkZGluZyBpbiBtYXJrZG93biBjb25zdHJ1Y3RzLlxuICpcbiAqIEluIG1hcmtkb3duLCBhbG1vc3QgYWxsIHB1bmN0dWF0aW9uIGNoYXJhY3RlcnMgY2FuLCBpbiBjZXJ0YWluIGNhc2VzLFxuICogcmVzdWx0IGluIHNvbWV0aGluZy5cbiAqIFdoZXRoZXIgdGhleSBkbyBpcyBoaWdobHkgc3ViamVjdGl2ZSB0byB3aGVyZSB0aGV5IGhhcHBlbiBhbmQgaW4gd2hhdFxuICogdGhleSBoYXBwZW4uXG4gKlxuICogVG8gc29sdmUgdGhpcywgYG1kYXN0LXV0aWwtdG8tbWFya2Rvd25gIHRyYWNrczpcbiAqXG4gKiAqIENoYXJhY3RlcnMgYmVmb3JlIGFuZCBhZnRlciBzb21ldGhpbmc7XG4gKiAqIFdoYXQg4oCcY29uc3RydWN0c+KAnSB3ZSBhcmUgaW4uXG4gKlxuICogVGhpcyBpbmZvcm1hdGlvbiBpcyB0aGVuIHVzZWQgYnkgdGhpcyBmdW5jdGlvbiB0byBlc2NhcGUgb3IgZW5jb2RlXG4gKiBzcGVjaWFsIGNoYXJhY3RlcnMuXG4gKlxuICogQHRoaXMge1N0YXRlfVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcGFyYW0ge3N0cmluZyB8IG51bGwgfCB1bmRlZmluZWR9IHZhbHVlXG4gKiAgIFJhdyB2YWx1ZSB0byBtYWtlIHNhZmUuXG4gKiBAcGFyYW0ge1NhZmVDb25maWd9IGNvbmZpZ1xuICogICBDb25maWd1cmF0aW9uLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBtYXJrZG93biBzYWZlIGZvciBlbWJlZGRpbmcuXG4gKi9cbmZ1bmN0aW9uIHNhZmVCb3VuZCh2YWx1ZSwgY29uZmlnKSB7XG4gIHJldHVybiBzYWZlKHRoaXMsIHZhbHVlLCBjb25maWcpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js":
/*!*********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/join.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   join: () => (/* binding */ join)\n/* harmony export */ });\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/format-code-as-indented.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-heading-as-setext.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Join} from 'mdast-util-to-markdown'\n */\n\n\n\n\n/** @type {Array<Join>} */\nconst join = [joinDefaults]\n\n/** @type {Join} */\nfunction joinDefaults(left, right, parent, state) {\n  // Indented code after list or another indented code.\n  if (\n    right.type === 'code' &&\n    (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(right, state) &&\n    (left.type === 'list' ||\n      (left.type === right.type && (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(left, state)))\n  ) {\n    return false\n  }\n\n  // Join children of a list or an item.\n  // In which case, `parent` has a `spread` field.\n  if ('spread' in parent && typeof parent.spread === 'boolean') {\n    if (\n      left.type === 'paragraph' &&\n      // Two paragraphs.\n      (left.type === right.type ||\n        right.type === 'definition' ||\n        // Paragraph followed by a setext heading.\n        (right.type === 'heading' && (0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__.formatHeadingAsSetext)(right, state)))\n    ) {\n      return\n    }\n\n    return parent.spread ? 1 : 0\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/unsafe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsafe: () => (/* binding */ unsafe)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain things like attention (emphasis, strong), images, or links.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * @type {Array<ConstructName>}\n */\nconst fullPhrasingSpans = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\n/** @type {Array<Unsafe>} */\nconst unsafe = [\n  {character: '\\t', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: '\\t', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: '\\t',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  {\n    character: '\\r',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {\n    character: '\\n',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {character: ' ', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: ' ', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: ' ',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  // An exclamation mark can start an image, if it is followed by a link or\n  // a link reference.\n  {\n    character: '!',\n    after: '\\\\[',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A quote can break out of a title.\n  {character: '\"', inConstruct: 'titleQuote'},\n  // A number sign could start an ATX heading if it starts a line.\n  {atBreak: true, character: '#'},\n  {character: '#', inConstruct: 'headingAtx', after: '(?:[\\r\\n]|$)'},\n  // Dollar sign and percentage are not used in markdown.\n  // An ampersand could start a character reference.\n  {character: '&', after: '[#A-Za-z]', inConstruct: 'phrasing'},\n  // An apostrophe can break out of a title.\n  {character: \"'\", inConstruct: 'titleApostrophe'},\n  // A left paren could break out of a destination raw.\n  {character: '(', inConstruct: 'destinationRaw'},\n  // A left paren followed by `]` could make something into a link or image.\n  {\n    before: '\\\\]',\n    character: '(',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A right paren could start a list item or break out of a destination\n  // raw.\n  {atBreak: true, before: '\\\\d+', character: ')'},\n  {character: ')', inConstruct: 'destinationRaw'},\n  // An asterisk can start thematic breaks, list items, emphasis, strong.\n  {atBreak: true, character: '*', after: '(?:[ \\t\\r\\n*])'},\n  {character: '*', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A plus sign could start a list item.\n  {atBreak: true, character: '+', after: '(?:[ \\t\\r\\n])'},\n  // A dash can start thematic breaks, list items, and setext heading\n  // underlines.\n  {atBreak: true, character: '-', after: '(?:[ \\t\\r\\n-])'},\n  // A dot could start a list item.\n  {atBreak: true, before: '\\\\d+', character: '.', after: '(?:[ \\t\\r\\n]|$)'},\n  // Slash, colon, and semicolon are not used in markdown for constructs.\n  // A less than can start html (flow or text) or an autolink.\n  // HTML could start with an exclamation mark (declaration, cdata, comment),\n  // slash (closing tag), question mark (instruction), or a letter (tag).\n  // An autolink also starts with a letter.\n  // Finally, it could break out of a destination literal.\n  {atBreak: true, character: '<', after: '[!/?A-Za-z]'},\n  {\n    character: '<',\n    after: '[!/?A-Za-z]',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  {character: '<', inConstruct: 'destinationLiteral'},\n  // An equals to can start setext heading underlines.\n  {atBreak: true, character: '='},\n  // A greater than can start block quotes and it can break out of a\n  // destination literal.\n  {atBreak: true, character: '>'},\n  {character: '>', inConstruct: 'destinationLiteral'},\n  // Question mark and at sign are not used in markdown for constructs.\n  // A left bracket can start definitions, references, labels,\n  {atBreak: true, character: '['},\n  {character: '[', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  {character: '[', inConstruct: ['label', 'reference']},\n  // A backslash can start an escape (when followed by punctuation) or a\n  // hard break (when followed by an eol).\n  // Note: typical escapes are handled in `safe`!\n  {character: '\\\\', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  // A right bracket can exit labels.\n  {character: ']', inConstruct: ['label', 'reference']},\n  // Caret is not used in markdown for constructs.\n  // An underscore can start emphasis, strong, or a thematic break.\n  {atBreak: true, character: '_'},\n  {character: '_', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A grave accent can start code (fenced or text), or it can break out of\n  // a grave accent code fence.\n  {atBreak: true, character: '`'},\n  {\n    character: '`',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedMetaGraveAccent']\n  },\n  {character: '`', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // Left brace, vertical bar, right brace are not used in markdown for\n  // constructs.\n  // A tilde can start code (fenced).\n  {atBreak: true, character: '~'}\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/association.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   association: () => (/* binding */ association)\n/* harmony export */ });\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-decode-string */ \"(rsc)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/**\n * @import {AssociationId} from '../types.js'\n */\n\n\n\n/**\n * Get an identifier from an association to match it to others.\n *\n * Associations are nodes that match to something else through an ID:\n * <https://github.com/syntax-tree/mdast#association>.\n *\n * The `label` of an association is the string value: character escapes and\n * references work, and casing is intact.\n * The `identifier` is used to match one association to another:\n * controversially, character escapes and references don’t work in this\n * matching: `&copy;` does not match `©`, and `\\+` does not match `+`.\n *\n * But casing is ignored (and whitespace) is trimmed and collapsed: ` A\\nb`\n * matches `a b`.\n * So, we do prefer the label when figuring out how we’re going to serialize:\n * it has whitespace, casing, and we can ignore most useless character\n * escapes and all character references.\n *\n * @type {AssociationId}\n */\nfunction association(node) {\n  if (node.label || !node.identifier) {\n    return node.label || ''\n  }\n\n  return (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__.decodeString)(node.identifier)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9hc3NvY2lhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsWUFBWSxlQUFlO0FBQzNCOztBQUV5RDs7QUFFekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTs7QUFFQSxTQUFTLDBFQUFZO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxhc3NvY2lhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0Fzc29jaWF0aW9uSWR9IGZyb20gJy4uL3R5cGVzLmpzJ1xuICovXG5cbmltcG9ydCB7ZGVjb2RlU3RyaW5nfSBmcm9tICdtaWNyb21hcmstdXRpbC1kZWNvZGUtc3RyaW5nJ1xuXG4vKipcbiAqIEdldCBhbiBpZGVudGlmaWVyIGZyb20gYW4gYXNzb2NpYXRpb24gdG8gbWF0Y2ggaXQgdG8gb3RoZXJzLlxuICpcbiAqIEFzc29jaWF0aW9ucyBhcmUgbm9kZXMgdGhhdCBtYXRjaCB0byBzb21ldGhpbmcgZWxzZSB0aHJvdWdoIGFuIElEOlxuICogPGh0dHBzOi8vZ2l0aHViLmNvbS9zeW50YXgtdHJlZS9tZGFzdCNhc3NvY2lhdGlvbj4uXG4gKlxuICogVGhlIGBsYWJlbGAgb2YgYW4gYXNzb2NpYXRpb24gaXMgdGhlIHN0cmluZyB2YWx1ZTogY2hhcmFjdGVyIGVzY2FwZXMgYW5kXG4gKiByZWZlcmVuY2VzIHdvcmssIGFuZCBjYXNpbmcgaXMgaW50YWN0LlxuICogVGhlIGBpZGVudGlmaWVyYCBpcyB1c2VkIHRvIG1hdGNoIG9uZSBhc3NvY2lhdGlvbiB0byBhbm90aGVyOlxuICogY29udHJvdmVyc2lhbGx5LCBjaGFyYWN0ZXIgZXNjYXBlcyBhbmQgcmVmZXJlbmNlcyBkb27igJl0IHdvcmsgaW4gdGhpc1xuICogbWF0Y2hpbmc6IGAmY29weTtgIGRvZXMgbm90IG1hdGNoIGDCqWAsIGFuZCBgXFwrYCBkb2VzIG5vdCBtYXRjaCBgK2AuXG4gKlxuICogQnV0IGNhc2luZyBpcyBpZ25vcmVkIChhbmQgd2hpdGVzcGFjZSkgaXMgdHJpbW1lZCBhbmQgY29sbGFwc2VkOiBgIEFcXG5iYFxuICogbWF0Y2hlcyBgYSBiYC5cbiAqIFNvLCB3ZSBkbyBwcmVmZXIgdGhlIGxhYmVsIHdoZW4gZmlndXJpbmcgb3V0IGhvdyB3ZeKAmXJlIGdvaW5nIHRvIHNlcmlhbGl6ZTpcbiAqIGl0IGhhcyB3aGl0ZXNwYWNlLCBjYXNpbmcsIGFuZCB3ZSBjYW4gaWdub3JlIG1vc3QgdXNlbGVzcyBjaGFyYWN0ZXJcbiAqIGVzY2FwZXMgYW5kIGFsbCBjaGFyYWN0ZXIgcmVmZXJlbmNlcy5cbiAqXG4gKiBAdHlwZSB7QXNzb2NpYXRpb25JZH1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGFzc29jaWF0aW9uKG5vZGUpIHtcbiAgaWYgKG5vZGUubGFiZWwgfHwgIW5vZGUuaWRlbnRpZmllcikge1xuICAgIHJldHVybiBub2RlLmxhYmVsIHx8ICcnXG4gIH1cblxuICByZXR1cm4gZGVjb2RlU3RyaW5nKG5vZGUuaWRlbnRpZmllcilcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWJ1bGxldC1vcmRlcmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0T3JkZXJlZCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0T3JkZXJlZChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldE9yZGVyZWQgfHwgJy4nXG5cbiAgaWYgKG1hcmtlciAhPT0gJy4nICYmIG1hcmtlciAhPT0gJyknKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRPcmRlcmVkYCwgZXhwZWN0ZWQgYC5gIG9yIGApYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBulletOther(state) {\n  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3RoZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUU2Qzs7QUFFN0M7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUCxpQkFBaUIsNkRBQVc7QUFDNUI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1idWxsZXQtb3RoZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG5pbXBvcnQge2NoZWNrQnVsbGV0fSBmcm9tICcuL2NoZWNrLWJ1bGxldC5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXRPdGhlcihzdGF0ZSkge1xuICBjb25zdCBidWxsZXQgPSBjaGVja0J1bGxldChzdGF0ZSlcbiAgY29uc3QgYnVsbGV0T3RoZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldE90aGVyXG5cbiAgaWYgKCFidWxsZXRPdGhlcikge1xuICAgIHJldHVybiBidWxsZXQgPT09ICcqJyA/ICctJyA6ICcqJ1xuICB9XG5cbiAgaWYgKGJ1bGxldE90aGVyICE9PSAnKicgJiYgYnVsbGV0T3RoZXIgIT09ICcrJyAmJiBidWxsZXRPdGhlciAhPT0gJy0nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIGJ1bGxldE90aGVyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldE90aGVyYCwgZXhwZWN0ZWQgYCpgLCBgK2AsIG9yIGAtYCdcbiAgICApXG4gIH1cblxuICBpZiAoYnVsbGV0T3RoZXIgPT09IGJ1bGxldCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdFeHBlY3RlZCBgYnVsbGV0YCAoYCcgK1xuICAgICAgICBidWxsZXQgK1xuICAgICAgICAnYCkgYW5kIGBidWxsZXRPdGhlcmAgKGAnICtcbiAgICAgICAgYnVsbGV0T3RoZXIgK1xuICAgICAgICAnYCkgdG8gYmUgZGlmZmVyZW50J1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBidWxsZXRPdGhlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1idWxsZXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldCB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnKycgJiYgbWFya2VyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldGAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nfunction checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWVtcGhhc2lzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZW1waGFzaXMnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0VtcGhhc2lzKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZW1waGFzaXMgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgZW1waGFzaXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5lbXBoYXNpc2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nfunction checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWZlbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZmVuY2UnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0ZlbmNlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZmVuY2UgfHwgJ2AnXG5cbiAgaWYgKG1hcmtlciAhPT0gJ2AnICYmIG1hcmtlciAhPT0gJ34nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgY29kZSB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmZlbmNlYCwgZXhwZWN0ZWQgYGAgYCBgYCBvciBgfmAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stbGlzdC1pdGVtLWluZGVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2xpc3RJdGVtSW5kZW50J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tMaXN0SXRlbUluZGVudChzdGF0ZSkge1xuICBjb25zdCBzdHlsZSA9IHN0YXRlLm9wdGlvbnMubGlzdEl0ZW1JbmRlbnQgfHwgJ29uZSdcblxuICBpZiAoc3R5bGUgIT09ICd0YWInICYmIHN0eWxlICE9PSAnb25lJyAmJiBzdHlsZSAhPT0gJ21peGVkJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBzdHlsZSArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5saXN0SXRlbUluZGVudGAsIGV4cGVjdGVkIGB0YWJgLCBgb25lYCwgb3IgYG1peGVkYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gc3R5bGVcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nfunction checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLXF1b3RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncXVvdGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1F1b3RlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMucXVvdGUgfHwgJ1wiJ1xuXG4gIGlmIChtYXJrZXIgIT09ICdcIicgJiYgbWFya2VyICE9PSBcIidcIikge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHRpdGxlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucXVvdGVgLCBleHBlY3RlZCBgXCJgLCBvciBgXFwnYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nfunction checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1ydWxlLXJlcGV0aXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydydWxlUmVwZXRpdGlvbiddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZVJlcGV0aXRpb24oc3RhdGUpIHtcbiAgY29uc3QgcmVwZXRpdGlvbiA9IHN0YXRlLm9wdGlvbnMucnVsZVJlcGV0aXRpb24gfHwgM1xuXG4gIGlmIChyZXBldGl0aW9uIDwgMykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggcmVwZXRpdGlvbiBgJyArXG4gICAgICAgIHJlcGV0aXRpb24gK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZVJlcGV0aXRpb25gLCBleHBlY3RlZCBgM2Agb3IgbW9yZSdcbiAgICApXG4gIH1cblxuICByZXR1cm4gcmVwZXRpdGlvblxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nfunction checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stcnVsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1J1bGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5ydWxlIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICctJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZWAsIGV4cGVjdGVkIGAqYCwgYC1gLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nfunction checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1zdHJvbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydzdHJvbmcnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1N0cm9uZyhzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnN0cm9uZyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBzdHJvbmcgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5zdHJvbmdgLCBleHBlY3RlZCBgKmAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compilePattern: () => (/* binding */ compilePattern)\n/* harmony export */ });\n/**\n * @import {CompilePattern} from '../types.js'\n */\n\n/**\n * @type {CompilePattern}\n */\nfunction compilePattern(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jb21waWxlLXBhdHRlcm4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjb21waWxlLXBhdHRlcm4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtDb21waWxlUGF0dGVybn0gZnJvbSAnLi4vdHlwZXMuanMnXG4gKi9cblxuLyoqXG4gKiBAdHlwZSB7Q29tcGlsZVBhdHRlcm59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21waWxlUGF0dGVybihwYXR0ZXJuKSB7XG4gIGlmICghcGF0dGVybi5fY29tcGlsZWQpIHtcbiAgICBjb25zdCBiZWZvcmUgPVxuICAgICAgKHBhdHRlcm4uYXRCcmVhayA/ICdbXFxcXHJcXFxcbl1bXFxcXHQgXSonIDogJycpICtcbiAgICAgIChwYXR0ZXJuLmJlZm9yZSA/ICcoPzonICsgcGF0dGVybi5iZWZvcmUgKyAnKScgOiAnJylcblxuICAgIHBhdHRlcm4uX2NvbXBpbGVkID0gbmV3IFJlZ0V4cChcbiAgICAgIChiZWZvcmUgPyAnKCcgKyBiZWZvcmUgKyAnKScgOiAnJykgK1xuICAgICAgICAoL1t8XFxcXHt9KClbXFxdXiQrKj8uLV0vLnRlc3QocGF0dGVybi5jaGFyYWN0ZXIpID8gJ1xcXFwnIDogJycpICtcbiAgICAgICAgcGF0dGVybi5jaGFyYWN0ZXIgK1xuICAgICAgICAocGF0dGVybi5hZnRlciA/ICcoPzonICsgcGF0dGVybi5hZnRlciArICcpJyA6ICcnKSxcbiAgICAgICdnJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBwYXR0ZXJuLl9jb21waWxlZFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-flow.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerFlow: () => (/* binding */ containerFlow)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {FlowChildren, FlowParents, TrackFields} from '../types.js'\n */\n\n/**\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  const tracker = state.createTracker(info)\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          before: '\\n',\n          after: '\\n',\n          ...tracker.current()\n        })\n      )\n    )\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(\n        tracker.move(between(child, children[index + 1], parent, state))\n      )\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {FlowChildren} left\n * @param {FlowChildren} right\n * @param {FlowParents} parent\n * @param {State} state\n * @returns {string}\n */\nfunction between(left, right, parent, state) {\n  let index = state.join.length\n\n  while (index--) {\n    const result = state.join[index](left, right, parent, state)\n\n    if (result === true || result === 1) {\n      break\n    }\n\n    if (typeof result === 'number') {\n      return '\\n'.repeat(1 + result)\n    }\n\n    if (result === false) {\n      return '\\n\\n<!---->\\n\\n'\n    }\n  }\n\n  return '\\n\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerPhrasing: () => (/* binding */ containerPhrasing)\n/* harmony export */ });\n/* harmony import */ var _encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/**\n * @import {Handle, Info, State} from 'mdast-util-to-markdown'\n * @import {PhrasingParents} from '../types.js'\n */\n\n\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n  /** @type {string | undefined} */\n  let encodeAfter\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    let value = state.handle(child, parent, state, {\n      ...tracker.current(),\n      after,\n      before\n    })\n\n    // If we had to encode the first character after the previous node and it’s\n    // still the same character,\n    // encode it.\n    if (encodeAfter && encodeAfter === value.slice(0, 1)) {\n      value =\n        (0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__.encodeCharacterReference)(encodeAfter.charCodeAt(0)) + value.slice(1)\n    }\n\n    const encodingInfo = state.attentionEncodeSurroundingInfo\n    state.attentionEncodeSurroundingInfo = undefined\n    encodeAfter = undefined\n\n    // If we have to encode the first character before the current node and\n    // it’s still the same character,\n    // encode it.\n    if (encodingInfo) {\n      if (\n        results.length > 0 &&\n        encodingInfo.before &&\n        before === results[results.length - 1].slice(-1)\n      ) {\n        results[results.length - 1] =\n          results[results.length - 1].slice(0, -1) +\n          (0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__.encodeCharacterReference)(before.charCodeAt(0))\n      }\n\n      if (encodingInfo.after) encodeAfter = after\n    }\n\n    tracker.move(value)\n    results.push(value)\n    before = value.slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nfunction encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLHFEQUFxRDtBQUNyRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcZW5jb2RlLWNoYXJhY3Rlci1yZWZlcmVuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbmNvZGUgYSBjb2RlIHBvaW50IGFzIGEgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogICBDb2RlIHBvaW50IHRvIGVuY29kZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIEVuY29kZWQgY2hhcmFjdGVyIHJlZmVyZW5jZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZUNoYXJhY3RlclJlZmVyZW5jZShjb2RlKSB7XG4gIHJldHVybiAnJiN4JyArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCkgKyAnOydcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(rsc)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */\n\n\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n  const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside)\n  const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxNQUFNO0FBQ2xCOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGZvcm1hdC1jb2RlLWFzLWluZGVudGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtDb2RlfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7Q29kZX0gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q29kZUFzSW5kZW50ZWQobm9kZSwgc3RhdGUpIHtcbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgc3RhdGUub3B0aW9ucy5mZW5jZXMgPT09IGZhbHNlICYmXG4gICAgICBub2RlLnZhbHVlICYmXG4gICAgICAvLyBJZiB0aGVyZeKAmXMgbm8gaW5mb+KAplxuICAgICAgIW5vZGUubGFuZyAmJlxuICAgICAgLy8gQW5kIHRoZXJl4oCZcyBhIG5vbi13aGl0ZXNwYWNlIGNoYXJhY3RlcuKAplxuICAgICAgL1teIFxcclxcbl0vLnRlc3Qobm9kZS52YWx1ZSkgJiZcbiAgICAgIC8vIEFuZCB0aGUgdmFsdWUgZG9lc27igJl0IHN0YXJ0IG9yIGVuZCBpbiBhIGJsYW5r4oCmXG4gICAgICAhL15bXFx0IF0qKD86W1xcclxcbl18JCl8KD86XnxbXFxyXFxuXSlbXFx0IF0qJC8udGVzdChub2RlLnZhbHVlKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(rsc)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtaGVhZGluZy1hcy1zZXRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjs7QUFFNEM7QUFDQzs7QUFFN0M7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEVBQUUsd0RBQUs7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxrREFBSTtBQUNqQjtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLE1BQU0sOERBQVE7QUFDZDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGZvcm1hdC1oZWFkaW5nLWFzLXNldGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7SGVhZGluZ30gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtFWElULCB2aXNpdH0gZnJvbSAndW5pc3QtdXRpbC12aXNpdCdcbmltcG9ydCB7dG9TdHJpbmd9IGZyb20gJ21kYXN0LXV0aWwtdG8tc3RyaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7SGVhZGluZ30gbm9kZVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0SGVhZGluZ0FzU2V0ZXh0KG5vZGUsIHN0YXRlKSB7XG4gIGxldCBsaXRlcmFsV2l0aEJyZWFrID0gZmFsc2VcblxuICAvLyBMb29rIGZvciBsaXRlcmFscyB3aXRoIGEgbGluZSBicmVhay5cbiAgLy8gTm90ZSB0aGF0IHRoaXMgYWxzb1xuICB2aXNpdChub2RlLCBmdW5jdGlvbiAobm9kZSkge1xuICAgIGlmIChcbiAgICAgICgndmFsdWUnIGluIG5vZGUgJiYgL1xccj9cXG58XFxyLy50ZXN0KG5vZGUudmFsdWUpKSB8fFxuICAgICAgbm9kZS50eXBlID09PSAnYnJlYWsnXG4gICAgKSB7XG4gICAgICBsaXRlcmFsV2l0aEJyZWFrID0gdHJ1ZVxuICAgICAgcmV0dXJuIEVYSVRcbiAgICB9XG4gIH0pXG5cbiAgcmV0dXJuIEJvb2xlYW4oXG4gICAgKCFub2RlLmRlcHRoIHx8IG5vZGUuZGVwdGggPCAzKSAmJlxuICAgICAgdG9TdHJpbmcobm9kZSkgJiZcbiAgICAgIChzdGF0ZS5vcHRpb25zLnNldGV4dCB8fCBsaXRlcmFsV2l0aEJyZWFrKVxuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(rsc)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\n\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatLinkAsAutolink(node, state) {\n  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indentLines: () => (/* binding */ indentLines)\n/* harmony export */ });\n/**\n * @import {IndentLines} from '../types.js'\n */\n\nconst eol = /\\r?\\n|\\r/g\n\n/**\n * @type {IndentLines}\n */\nfunction indentLines(value, map) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  let line = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  /**\n   * @param {string} value\n   */\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9pbmRlbnQtbGluZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxhQUFhO0FBQ3pCOztBQUVBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUCxhQUFhLGVBQWU7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGluZGVudC1saW5lcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZGVudExpbmVzfSBmcm9tICcuLi90eXBlcy5qcydcbiAqL1xuXG5jb25zdCBlb2wgPSAvXFxyP1xcbnxcXHIvZ1xuXG4vKipcbiAqIEB0eXBlIHtJbmRlbnRMaW5lc31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluZGVudExpbmVzKHZhbHVlLCBtYXApIHtcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCByZXN1bHQgPSBbXVxuICBsZXQgc3RhcnQgPSAwXG4gIGxldCBsaW5lID0gMFxuICAvKiogQHR5cGUge1JlZ0V4cEV4ZWNBcnJheSB8IG51bGx9ICovXG4gIGxldCBtYXRjaFxuXG4gIHdoaWxlICgobWF0Y2ggPSBlb2wuZXhlYyh2YWx1ZSkpKSB7XG4gICAgb25lKHZhbHVlLnNsaWNlKHN0YXJ0LCBtYXRjaC5pbmRleCkpXG4gICAgcmVzdWx0LnB1c2gobWF0Y2hbMF0pXG4gICAgc3RhcnQgPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aFxuICAgIGxpbmUrK1xuICB9XG5cbiAgb25lKHZhbHVlLnNsaWNlKHN0YXJ0KSlcblxuICByZXR1cm4gcmVzdWx0LmpvaW4oJycpXG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICAgKi9cbiAgZnVuY3Rpb24gb25lKHZhbHVlKSB7XG4gICAgcmVzdWx0LnB1c2gobWFwKHZhbHVlLCBsaW5lLCAhdmFsdWUpKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nfunction patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksdUJBQXVCO0FBQ25DOztBQUVBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDLFdBQVcsdUJBQXVCO0FBQ2xDLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxwYXR0ZXJuLWluLXNjb3BlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Q29uc3RydWN0TmFtZSwgVW5zYWZlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxDb25zdHJ1Y3ROYW1lPn0gc3RhY2tcbiAqIEBwYXJhbSB7VW5zYWZlfSBwYXR0ZXJuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhdHRlcm5JblNjb3BlKHN0YWNrLCBwYXR0ZXJuKSB7XG4gIHJldHVybiAoXG4gICAgbGlzdEluU2NvcGUoc3RhY2ssIHBhdHRlcm4uaW5Db25zdHJ1Y3QsIHRydWUpICYmXG4gICAgIWxpc3RJblNjb3BlKHN0YWNrLCBwYXR0ZXJuLm5vdEluQ29uc3RydWN0LCBmYWxzZSlcbiAgKVxufVxuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8Q29uc3RydWN0TmFtZT59IHN0YWNrXG4gKiBAcGFyYW0ge1Vuc2FmZVsnaW5Db25zdHJ1Y3QnXX0gbGlzdFxuICogQHBhcmFtIHtib29sZWFufSBub25lXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gbGlzdEluU2NvcGUoc3RhY2ssIGxpc3QsIG5vbmUpIHtcbiAgaWYgKHR5cGVvZiBsaXN0ID09PSAnc3RyaW5nJykge1xuICAgIGxpc3QgPSBbbGlzdF1cbiAgfVxuXG4gIGlmICghbGlzdCB8fCBsaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBub25lXG4gIH1cblxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICBpZiAoc3RhY2suaW5jbHVkZXMobGlzdFtpbmRleF0pKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/safe.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pattern-in-scope.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {SafeConfig, State} from 'mdast-util-to-markdown'\n */\n\n\n\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {string | null | undefined} input\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safe(state, input, config) {\n  const value = (config.before || '') + (input || '') + (config.after || '')\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {Record<number, {before: boolean, after: boolean}>} */\n  const infos = {}\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n\n    if (!(0,_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, pattern)) {\n      continue\n    }\n\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    while ((match = expression.exec(value))) {\n      const before = 'before' in pattern || Boolean(pattern.atBreak)\n      const after = 'after' in pattern\n      const position = match.index + (before ? match[1].length : 0)\n\n      if (positions.includes(position)) {\n        if (infos[position].before && !before) {\n          infos[position].before = false\n        }\n\n        if (infos[position].after && !after) {\n          infos[position].after = false\n        }\n      } else {\n        positions.push(position)\n        infos[position] = {before, after}\n      }\n    }\n  }\n\n  positions.sort(numerical)\n\n  let start = config.before ? config.before.length : 0\n  const end = value.length - (config.after ? config.after.length : 0)\n  index = -1\n\n  while (++index < positions.length) {\n    const position = positions[index]\n\n    // Character before or after matched:\n    if (position < start || position >= end) {\n      continue\n    }\n\n    // If this character is supposed to be escaped because it has a condition on\n    // the next character, and the next character is definitly being escaped,\n    // then skip this escape.\n    if (\n      (position + 1 < end &&\n        positions[index + 1] === position + 1 &&\n        infos[position].after &&\n        !infos[position + 1].before &&\n        !infos[position + 1].after) ||\n      (positions[index - 1] === position - 1 &&\n        infos[position].before &&\n        !infos[position - 1].before &&\n        !infos[position - 1].after)\n    ) {\n      continue\n    }\n\n    if (start !== position) {\n      // If we have to use a character reference, an ampersand would be more\n      // correct, but as backslashes only care about punctuation, either will\n      // do the trick\n      result.push(escapeBackslashes(value.slice(start, position), '\\\\'))\n    }\n\n    start = position\n\n    if (\n      /[!-/:-@[-`{-~]/.test(value.charAt(position)) &&\n      (!config.encode || !config.encode.includes(value.charAt(position)))\n    ) {\n      // Character escape.\n      result.push('\\\\')\n    } else {\n      // Character reference.\n      result.push((0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(position)))\n      start++\n    }\n  }\n\n  result.push(escapeBackslashes(value.slice(start, end), config.after))\n\n  return result.join('')\n}\n\n/**\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction numerical(a, b) {\n  return a - b\n}\n\n/**\n * @param {string} value\n * @param {string} after\n * @returns {string}\n */\nfunction escapeBackslashes(value, after) {\n  const expression = /\\\\(?=[!-/:-@[-`{-~])/g\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const results = []\n  const whole = value + after\n  let index = -1\n  let start = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = expression.exec(whole))) {\n    positions.push(match.index)\n  }\n\n  while (++index < positions.length) {\n    if (start !== positions[index]) {\n      results.push(value.slice(start, positions[index]))\n    }\n\n    results.push('\\\\')\n    start = positions[index]\n  }\n\n  results.push(value.slice(start))\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/track.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/**\n * @import {CreateTracker, TrackCurrent, TrackMove, TrackShift} from '../types.js'\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nfunction track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC90cmFjay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLG9EQUFvRDtBQUNoRTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsVUFBVTs7QUFFVjtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFlBQVksTUFBTSxhQUFhO0FBQy9COztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFx0cmFjay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0NyZWF0ZVRyYWNrZXIsIFRyYWNrQ3VycmVudCwgVHJhY2tNb3ZlLCBUcmFja1NoaWZ0fSBmcm9tICcuLi90eXBlcy5qcydcbiAqL1xuXG4vKipcbiAqIFRyYWNrIHBvc2l0aW9uYWwgaW5mbyBpbiB0aGUgb3V0cHV0LlxuICpcbiAqIEB0eXBlIHtDcmVhdGVUcmFja2VyfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdHJhY2soY29uZmlnKSB7XG4gIC8vIERlZmF1bHRzIGFyZSB1c2VkIHRvIHByZXZlbnQgY3Jhc2hlcyB3aGVuIG9sZGVyIHV0aWxpdGllcyBzb21laG93IGFjdGl2YXRlXG4gIC8vIHRoaXMgY29kZS5cbiAgLyogYzggaWdub3JlIG5leHQgNSAqL1xuICBjb25zdCBvcHRpb25zID0gY29uZmlnIHx8IHt9XG4gIGNvbnN0IG5vdyA9IG9wdGlvbnMubm93IHx8IHt9XG4gIGxldCBsaW5lU2hpZnQgPSBvcHRpb25zLmxpbmVTaGlmdCB8fCAwXG4gIGxldCBsaW5lID0gbm93LmxpbmUgfHwgMVxuICBsZXQgY29sdW1uID0gbm93LmNvbHVtbiB8fCAxXG5cbiAgcmV0dXJuIHttb3ZlLCBjdXJyZW50LCBzaGlmdH1cblxuICAvKipcbiAgICogR2V0IHRoZSBjdXJyZW50IHRyYWNrZWQgaW5mby5cbiAgICpcbiAgICogQHR5cGUge1RyYWNrQ3VycmVudH1cbiAgICovXG4gIGZ1bmN0aW9uIGN1cnJlbnQoKSB7XG4gICAgcmV0dXJuIHtub3c6IHtsaW5lLCBjb2x1bW59LCBsaW5lU2hpZnR9XG4gIH1cblxuICAvKipcbiAgICogRGVmaW5lIGFuIGluY3JlYXNlZCBsaW5lIHNoaWZ0ICh0aGUgdHlwaWNhbCBpbmRlbnQgZm9yIGxpbmVzKS5cbiAgICpcbiAgICogQHR5cGUge1RyYWNrU2hpZnR9XG4gICAqL1xuICBmdW5jdGlvbiBzaGlmdCh2YWx1ZSkge1xuICAgIGxpbmVTaGlmdCArPSB2YWx1ZVxuICB9XG5cbiAgLyoqXG4gICAqIE1vdmUgcGFzdCBzb21lIGdlbmVyYXRlZCBtYXJrZG93bi5cbiAgICpcbiAgICogQHR5cGUge1RyYWNrTW92ZX1cbiAgICovXG4gIGZ1bmN0aW9uIG1vdmUoaW5wdXQpIHtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgdW5pY29ybi9wcmVmZXItZGVmYXVsdC1wYXJhbWV0ZXJzXG4gICAgY29uc3QgdmFsdWUgPSBpbnB1dCB8fCAnJ1xuICAgIGNvbnN0IGNodW5rcyA9IHZhbHVlLnNwbGl0KC9cXHI/XFxufFxcci9nKVxuICAgIGNvbnN0IHRhaWwgPSBjaHVua3NbY2h1bmtzLmxlbmd0aCAtIDFdXG4gICAgbGluZSArPSBjaHVua3MubGVuZ3RoIC0gMVxuICAgIGNvbHVtbiA9XG4gICAgICBjaHVua3MubGVuZ3RoID09PSAxID8gY29sdW1uICsgdGFpbC5sZW5ndGggOiAxICsgdGFpbC5sZW5ndGggKyBsaW5lU2hpZnRcbiAgICByZXR1cm4gdmFsdWVcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js\n");

/***/ })

};
;