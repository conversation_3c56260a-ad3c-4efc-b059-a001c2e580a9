---
title: Getting Started
---

import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Callout } from 'fumadocs-ui/components/callout'

Three steps, you can get TEN up and running.

### Step ⓵ - Prerequisites

<Tabs items={['API Keys', 'Installations', 'Minimum system requirements']}>
  <Tab>
    • Agora [App ID](https://docs.agora.io/en/video-calling/get-started/manage-agora-account?platform=web#create-an-agora-project) and [App Certificate](https://docs.agora.io/en/video-calling/get-started/manage-agora-account?platform=web#create-an-agora-project) (free minutes every month) <br />
    • [OpenAI API key](https://openai.com/index/openai-api/) (any LLM that is compatible with OpenAI) <br />
    • [Deepgram ASR](https://deepgram.com/) (free credits available with signup) <br />
    • [ElevenLabs TTS](https://elevenlabs.io/) (free credits available with signup)
  </Tab>
  <Tab>
    • [Docker](https://www.docker.com/) / [Docker Compose](https://docs.docker.com/compose/) <br />
    • [Node.js(LTS) v18](https://nodejs.org/en)
  </Tab>
  <Tab>
    • CPU >= 2 Core <br />
    • RAM >= 4 GB
  </Tab>
</Tabs>

<Callout title="macOS: Docker setting on Apple Silicon">
  Uncheck "Use Rosetta for x86/amd64 emulation" in Docker settings, it may result in slower build times on ARM, but performance will be normal when deployed to x64 servers.
</Callout>

### Step ⓶ - Build agent in VM

##### 1. Clone down the repo,`cd` to `ai-agents` and create `.env` file from `.env.example`

```bash
cd ai_agents
cp ./.env.example ./.env
```

##### 2. Setup Agora App ID and App Certificate in `.env`

```bash
AGORA_APP_ID=
AGORA_APP_CERTIFICATE=
```

##### 3. Start agent development containers

```bash
docker compose up -d
```

##### 4. Enter container

```bash
docker exec -it ten_agent_dev bash
```

##### 5. Build agent with the default `graph` ( ~5min - ~8min)

check the `/examples` folder for more examples

```bash
# use the default agent
task use

# or use the demo agent
task use AGENT=agents/examples/demo
```

##### 6. Start the web server

```bash
task run
```

### Step ⓷ - Customize your agent with TMAN Designer

1. Open [localhost:49483](http://localhost:49483).
2. Right-click to load the corresponding graph (e.g., Voice Assistant).
3. Enter API keys and set preferences for each extension.
4. Right-click and select `Manage Apps` to start the Apps Manager.
5. Click `Run App` to start TEN Agent.
