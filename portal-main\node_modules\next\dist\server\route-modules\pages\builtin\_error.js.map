{"version": 3, "sources": ["../../../../../src/server/route-modules/pages/builtin/_error.tsx"], "sourcesContent": ["import Document from '../../../../pages/_document'\nimport App from '../../../../pages/_app'\nimport { RouteKind } from '../../../route-kind'\n\nimport * as moduleError from '../../../../pages/_error'\n\nimport PagesRouteModule from '../module'\n\nexport const routeModule = new PagesRouteModule({\n  // TODO: add descriptor for internal error page\n  definition: {\n    kind: RouteKind.PAGES,\n    page: '/_error',\n    pathname: '/_error',\n    filename: '',\n    bundlePath: '',\n  },\n  components: {\n    App,\n    Document,\n  },\n  // @ts-expect-error -- Types don't account for getInitialProps. `Error` requires to be instantiated with `statusCode` but the types currently don't guarantee that.\n  userland: moduleError,\n})\n"], "names": ["routeModule", "PagesRouteModule", "definition", "kind", "RouteKind", "PAGES", "page", "pathname", "filename", "bundlePath", "components", "App", "Document", "userland", "moduleError"], "mappings": ";;;;+BAQaA;;;eAAAA;;;iEARQ;4DACL;2BACU;+DAEG;+DAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMA,cAAc,IAAIC,eAAgB,CAAC;IAC9C,+CAA+C;IAC/CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,YAAY;QACVC,KAAAA,YAAG;QACHC,UAAAA,iBAAQ;IACV;IACA,mKAAmK;IACnKC,UAAUC;AACZ"}