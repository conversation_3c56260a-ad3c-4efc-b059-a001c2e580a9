{"name": "my-app", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "serve": "npx serve -s out -p 3000", "postinstall": "fumadocs-mdx"}, "dependencies": {"@orama/orama": "^3.1.11", "clsx": "^2.1.1", "fumadocs-core": "15.6.12", "fumadocs-mdx": "11.7.5", "fumadocs-ui": "15.6.12", "next": "15.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-motion": "^1.1.1", "tw-animate-css": "^1.3.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/mdx": "^2.0.13", "@types/node": "24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}