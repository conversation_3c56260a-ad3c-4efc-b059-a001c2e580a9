---
title: 开发流程
---

基本上，通过使用 TEN framework 来完成所需的场景，有两种开发方法或视角：

1. 从小扩展到大场景
2. 从大场景到小扩展

**注意：** 无论您采取这两种开发方法中的哪一种，您都可以从头开始编写自己的扩展，或者从 TEN 云商店下载预构建的扩展。

在第一种方法中，**从小扩展到大场景**，开发过程通常遵循以下步骤：

1. 开发扩展。
2. 将扩展组装成图，并进一步创建agent，例如 [TEN-Agent](https://github.com/TEN-framework/TEN-Agent)。

在第二种方法中，**从大场景到小扩展**，开发过程通常遵循以下步骤：

1. 选择所需的agent模板，例如 [TEN-Agent](https://github.com/TEN-framework/TEN-Agent)。
2. 将所选agent模板中的某些扩展替换为您自己的扩展。

在第二种开发方法中，通过使用框架提供的扩展**独立测试**功能，您可以确保替换后的扩展行为与原扩展一致。在此过程中，开发人员只需了解与 TEN 扩展相关的概念，包括如何独立开发和测试 TEN 扩展。其他与 TEN 相关的概念（扩展开发和测试之外）则无需关注，因为这些概念已封装在所选的 TEN agent中。
