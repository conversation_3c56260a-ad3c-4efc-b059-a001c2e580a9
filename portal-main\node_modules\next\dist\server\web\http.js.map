{"version": 3, "sources": ["../../../src/server/web/http.ts"], "sourcesContent": ["/**\n * List of valid HTTP methods that can be implemented by Next.js's Custom App\n * Routes.\n */\nexport const HTTP_METHODS = [\n  'GET',\n  'HEAD',\n  'OPTIONS',\n  'POST',\n  'PUT',\n  'DELETE',\n  'PATCH',\n] as const\n\n/**\n * A type representing the valid HTTP methods that can be implemented by\n * Next.js's Custom App Routes.\n */\nexport type HTTP_METHOD = (typeof HTTP_METHODS)[number]\n\n/**\n * Checks to see if the passed string is an HTTP method. Note that this is case\n * sensitive.\n *\n * @param maybeMethod the string that may be an HTTP method\n * @returns true if the string is an HTTP method\n */\nexport function isHTTPMethod(maybeMethod: string): maybeMethod is HTTP_METHOD {\n  return HTTP_METHODS.includes(maybeMethod as HTTP_METHOD)\n}\n"], "names": ["HTTP_METHODS", "isHTTPMethod", "<PERSON><PERSON><PERSON><PERSON>", "includes"], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;IACYA,YAAY;eAAZA;;IAuBGC,YAAY;eAAZA;;;AAvBT,MAAMD,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAeM,SAASC,aAAaC,WAAmB;IAC9C,OAAOF,aAAaG,QAAQ,CAACD;AAC/B"}