{"name": "unrs-resolver", "version": "1.3.3", "description": "Oxc Resolver Node API with PNP support", "main": "index.js", "browser": "browser.js", "files": ["index.d.ts", "index.js", "browser.js"], "license": "MIT", "homepage": "https://github.com/unrs/unrs-resolver", "repository": {"type": "git", "url": "https://github.com/unrs/unrs-resolver.git"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "napi": {"binaryName": "resolver", "packageName": "@unrs/resolver-binding", "wasm": {"browser": {"fs": true}}, "targets": ["x86_64-pc-windows-msvc", "aarch64-pc-windows-msvc", "i686-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "armv7-unknown-linux-musleabihf", "powerpc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "aarch64-apple-darwin", "wasm32-wasip1-threads"]}, "funding": {"url": "https://github.com/sponsors/JounQin"}, "optionalDependencies": {"@unrs/resolver-binding-win32-x64-msvc": "1.3.3", "@unrs/resolver-binding-win32-arm64-msvc": "1.3.3", "@unrs/resolver-binding-win32-ia32-msvc": "1.3.3", "@unrs/resolver-binding-linux-x64-gnu": "1.3.3", "@unrs/resolver-binding-linux-x64-musl": "1.3.3", "@unrs/resolver-binding-freebsd-x64": "1.3.3", "@unrs/resolver-binding-linux-arm64-gnu": "1.3.3", "@unrs/resolver-binding-linux-arm64-musl": "1.3.3", "@unrs/resolver-binding-linux-arm-gnueabihf": "1.3.3", "@unrs/resolver-binding-linux-arm-musleabihf": "1.3.3", "@unrs/resolver-binding-linux-ppc64-gnu": "1.3.3", "@unrs/resolver-binding-linux-s390x-gnu": "1.3.3", "@unrs/resolver-binding-darwin-x64": "1.3.3", "@unrs/resolver-binding-darwin-arm64": "1.3.3", "@unrs/resolver-binding-wasm32-wasi": "1.3.3"}}