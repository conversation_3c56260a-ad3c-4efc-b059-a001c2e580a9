{"version": 3, "file": "flat.js", "sourceRoot": "", "sources": ["../../../src/trees/flat.ts"], "names": [], "mappings": "AAGA,MAAM,OAAO,QAAQ;IACnB,kBAAkB,CAAqD;IAEvE;QACE,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAA;IACrC,CAAC;IAED,MAAM,CAAC,GAA0B,EAAE,KAAyB;QAC1D,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAED,IAAI,CAAC,GAA0B;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC9C,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACzC,CAAC;IAED,MAAM,CAAC,GAA0B;QAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IACrC,CAAC;IAED,cAAc,CAAC,EAAsB,EAAE,GAA0B;QAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC9C,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAChB,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,GAA0B;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,OAAO;QACL,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC;YACrD,IAAI,IAAI,KAAK,CAAC,IAAI,CAAA;QACpB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAC,SAAiC;QACtC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAE5C,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAiC,CAAA;QAEtE,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAE,CAAA;gBACvC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;gBAChD,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACvC,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAE,CAAA;gBACxC,MAAM,SAAS,GAA4B,IAAI,GAAG,EAAE,CAAA;gBACpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;oBAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;oBAChD,IAAI,KAAK,EAAE,CAAC;wBACV,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;4BACvB,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;wBACnB,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC9B,CAAC;YACD,KAAK,KAAK,CAAC,CAAC,CAAC;gBACX,MAAM,aAAa,GAAG,IAAI,GAAG,CAAwB,SAAS,CAAC,aAAa,CAAE,CAAC,CAAA;gBAC/E,MAAM,SAAS,GAA4B,IAAI,GAAG,EAAE,CAAA;gBACpD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC7D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC5B,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;4BACvB,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;wBACnB,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC9B,CAAC;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,SAAS,CAAC,SAAoC;QAC5C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAE5C,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAoC,CAAA;QAEzE,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAE,CAAA;gBACxC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;gBACrF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;oBAAE,OAAO,EAAE,CAAA;gBAClC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;oBAChD,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;gBACxD,CAAC,CAAC,CAAA;gBACF,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAyB,CAAA;YACzD,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAE,CAAA;gBACxC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;gBACrF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;oBAAE,OAAO,EAAE,CAAA;gBAClC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;oBACzC,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;gBACpC,CAAC,CAAC,CAAA;gBACF,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAyB,CAAA;YAClD,CAAC;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,IAAS;QACvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;QAC3C,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;QAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QAChD,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM;QACJ,OAAO;YACL,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAClH,CAAA;IACH,CAAC;CACF"}