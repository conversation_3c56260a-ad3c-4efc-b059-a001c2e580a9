/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\[lang]\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_e8ce0c {font-family: 'Inter', 'Inter Fallback';font-style: normal
}

/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/global.css ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-indigo-300: oklch(78.5% 0.115 274.713);
    --color-violet-200: oklch(89.4% 0.057 293.283);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-900: oklch(38.1% 0.176 304.987);
    --color-slate-700: oklch(37.2% 0.044 257.287);
    --color-slate-800: oklch(27.9% 0.041 260.031);
    --color-slate-950: oklch(12.9% 0.042 264.695);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-zinc-50: oklch(98.5% 0 0);
    --color-zinc-900: oklch(21% 0.006 285.885);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --breakpoint-sm: 40rem;
    --container-sm: 24rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-fd-background: hsl(0, 0%, 96%);
    --color-fd-foreground: hsl(0, 0%, 3.9%);
    --color-fd-muted: hsl(0, 0%, 96.1%);
    --color-fd-muted-foreground: hsl(0, 0%, 45.1%);
    --color-fd-popover: hsl(0, 0%, 98%);
    --color-fd-popover-foreground: hsl(0, 0%, 15.1%);
    --color-fd-card: hsl(0, 0%, 94.7%);
    --color-fd-card-foreground: hsl(0, 0%, 3.9%);
    --color-fd-border: hsl(0, 0%, 89.8%);
    --color-fd-primary: hsl(0, 0%, 9%);
    --color-fd-primary-foreground: hsl(0, 0%, 98%);
    --color-fd-secondary: hsl(0, 0%, 93.1%);
    --color-fd-secondary-foreground: hsl(0, 0%, 9%);
    --color-fd-accent: hsl(0, 0%, 90.1%);
    --color-fd-accent-foreground: hsl(0, 0%, 9%);
    --animate-fd-fade-in: fd-fade-in 300ms ease;
    --animate-fd-fade-out: fd-fade-out 300ms ease;
    --animate-fd-dialog-in: fd-dialog-in 200ms cubic-bezier(0.32, 0.72, 0, 1);
    --animate-fd-dialog-out: fd-dialog-out 300ms cubic-bezier(0.32, 0.72, 0, 1);
    --animate-fd-popover-in: fd-popover-in 150ms ease;
    --animate-fd-popover-out: fd-popover-out 150ms ease;
    --animate-fd-collapsible-down: fd-collapsible-down 150ms ease-out;
    --animate-fd-collapsible-up: fd-collapsible-up 150ms ease-out;
    --animate-fd-accordion-down: fd-accordion-down 200ms ease-out;
    --animate-fd-accordion-up: fd-accordion-up 200ms ease-out;
    --animate-fd-nav-menu-in: fd-nav-menu-in 200ms ease;
    --animate-fd-nav-menu-out: fd-nav-menu-out 200ms ease;
    --animate-fd-enterFromLeft: fd-enterFromLeft 250ms ease;
    --animate-fd-enterFromRight: fd-enterFromRight 250ms ease;
    --animate-fd-exitToLeft: fd-exitToLeft 250ms ease;
    --animate-fd-exitToRight: fd-exitToRight 250ms ease;
    --spacing-fd-container: 1400px;
    --fd-sidebar-width: 0px;
    --fd-toc-width: 0px;
    --fd-layout-width: 100vw;
    --fd-banner-height: 0px;
    --fd-nav-height: 0px;
    --fd-tocnav-height: 0px;
    --fd-diff-remove-color: rgba(200, 10, 100, 0.12);
    --fd-diff-remove-symbol-color: rgb(230, 10, 100);
    --fd-diff-add-color: rgba(14, 180, 100, 0.12);
    --fd-diff-add-symbol-color: rgb(10, 200, 100);
    --transparent: transparent;
    --white: var(--color-white);
    --black: var(--color-black);
    --blue-300: var(--color-blue-300);
    --blue-400: var(--color-blue-400);
    --blue-500: var(--color-blue-500);
    --indigo-300: var(--color-indigo-300);
    --violet-200: var(--color-violet-200);
    --animate-aurora: aurora 60s linear infinite;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .\@container {
    container-type: inline-size;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .visible {
    visibility: visible;
  }
  .fd-step {
    &:before {
      background-color: var(--color-fd-secondary);
      color: var(--color-fd-secondary-foreground);
      content: counter(step);
      counter-increment: step;
      justify-content: center;
      align-items: center;
      font-size: 0.875rem;
      line-height: 1.25rem;
      display: flex;
      position: absolute;
      inset-inline-start: calc(var(--spacing) * -4);
      width: calc(var(--spacing) * 8);
      height: calc(var(--spacing) * 8);
      border-radius: calc(infinity * 1px);
    }
  }
  .fd-steps {
    counter-reset: step;
    position: relative;
    margin-left: calc(var(--spacing) * 2);
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
    padding-left: calc(var(--spacing) * 6);
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 4);
    }
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 7);
    }
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .-inset-2 {
    inset: calc(var(--spacing) * -2);
  }
  .-inset-\[10px\] {
    inset: calc(10px * -1);
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .inset-y-2 {
    inset-block: calc(var(--spacing) * 2);
  }
  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }
  .start-3 {
    inset-inline-start: calc(var(--spacing) * 3);
  }
  .end-2 {
    inset-inline-end: calc(var(--spacing) * 2);
  }
  .-top-1\.5 {
    top: calc(var(--spacing) * -1.5);
  }
  .top-\(--fd-banner-height\) {
    top: var(--fd-banner-height);
  }
  .top-\(--fd-top\) {
    top: var(--fd-top);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-\[10vh\] {
    top: 10vh;
  }
  .top-\[calc\(var\(--fd-banner-height\)\+var\(--fd-nav-height\)\)\] {
    top: calc(var(--fd-banner-height) + var(--fd-nav-height));
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .-bottom-8 {
    bottom: calc(var(--spacing) * -8);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1\.5 {
    bottom: calc(var(--spacing) * 1.5);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[-1\] {
    z-index: -1;
  }
  .z-\[2\] {
    z-index: 2;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .container {
    margin-inline: auto;
    padding-inline: 1rem;
    @media (width >= 96rem) {
      max-width: 1400px;
    }
  }
  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }
  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .\!my-0 {
    margin-block: calc(var(--spacing) * 0) !important;
  }
  .-my-1\.5 {
    margin-block: calc(var(--spacing) * -1.5);
  }
  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-auto {
    margin-block: auto;
  }
  .-ms-1 {
    margin-inline-start: calc(var(--spacing) * -1);
  }
  .-ms-1\.5 {
    margin-inline-start: calc(var(--spacing) * -1.5);
  }
  .ms-1 {
    margin-inline-start: calc(var(--spacing) * 1);
  }
  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }
  .ms-auto {
    margin-inline-start: auto;
  }
  .-me-1\.5 {
    margin-inline-end: calc(var(--spacing) * -1.5);
  }
  .-me-2 {
    margin-inline-end: calc(var(--spacing) * -2);
  }
  .me-1\.5 {
    margin-inline-end: calc(var(--spacing) * 1.5);
  }
  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }
  .me-auto {
    margin-inline-end: auto;
  }
  .prose {
    color: var(--tw-prose-body);
    max-width: none;
    font-size: 1rem;
    line-height: 1.75rem;
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
      color: var(--tw-prose-lead);
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 1rem;
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.25em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      padding-inline-start: 1.625em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-color: var(--tw-prose-hr);
      border-top-width: 1px;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-bold);
      font-weight: 500;
    }
    :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      font-weight: 400;
      color: var(--tw-prose-counters);
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      color: var(--tw-prose-bullets);
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.6em;
      margin-bottom: 1.6em;
      padding-inline-start: 1em;
      font-weight: 500;
      font-style: italic;
      color: var(--tw-prose-quotes);
      border-inline-start-width: 0.25rem;
      border-inline-start-color: var(--tw-prose-quote-borders);
      quotes: "\201C""\201D""\2018""\2019";
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 800;
      font-size: var(--text-3xl);
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-size: 1.5em;
      margin-top: 2em;
      margin-bottom: 1em;
      line-height: 1.3333333;
      font-weight: 600;
    }
    :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      font-size: 1.25em;
      margin-top: 1.6em;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      display: block;
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.875em;
      border-radius: 0.3125rem;
      padding-top: 0.1875em;
      padding-inline-end: 0.375em;
      padding-bottom: 0.1875em;
      padding-inline-start: 0.375em;
      font-weight: 500;
      font-family: inherit;
      color: var(--tw-prose-kbd);
      box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows),0 3px 0 var(--tw-prose-kbd-shadows);
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding: 3px;
      border: solid 1px;
      font-size: 13px;
      border-color: var(--color-fd-border);
      border-radius: 5px;
      font-weight: 400;
      background: var(--color-fd-muted);
      color: var(--tw-prose-code);
    }
    :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: var(--text-2xl);
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.875em;
      line-height: 1.7142857;
      width: 100%;
      table-layout: auto;
      margin-top: 2em;
      margin-bottom: 2em;
      border-collapse: separate;
      border-spacing: 0;
      background: var(--color-fd-card);
      border-radius: var(--radius-lg);
      border: 1px solid var(--color-fd-border);
      overflow: hidden;
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    :where(a:not([data-card])):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-links);
      transition: opacity .2s;
      font-weight: 500;
      text-decoration: underline;
      text-underline-offset: 3.5px;
      text-decoration-color: var(--color-fd-primary);
      text-decoration-thickness: 1.5px;
    }
    :where(a:not([data-card]):hover):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      opacity: 80%;
    }
    --tw-prose-body: color-mix(in srgb, hsl(0, 0%, 3.9%) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-prose-body: color-mix(in oklab, var(--color-fd-foreground) 90%, transparent);
    }
    --tw-prose-headings: var(--color-fd-foreground);
    --tw-prose-lead: var(--color-fd-foreground);
    --tw-prose-links: var(--color-fd-foreground);
    --tw-prose-bold: var(--color-fd-foreground);
    --tw-prose-counters: var(--color-fd-muted-foreground);
    --tw-prose-bullets: var(--color-fd-muted-foreground);
    --tw-prose-hr: var(--color-fd-border);
    --tw-prose-quotes: var(--color-fd-foreground);
    --tw-prose-quote-borders: var(--color-fd-border);
    --tw-prose-captions: var(--color-fd-foreground);
    --tw-prose-code: var(--color-fd-foreground);
    --tw-prose-th-borders: var(--color-fd-border);
    --tw-prose-td-borders: var(--color-fd-border);
    --tw-prose-kbd: var(--color-fd-foreground);
    --tw-prose-kbd-shadows: color-mix(in srgb, hsl(0, 0%, 9%) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-prose-kbd-shadows: color-mix(in oklab, var(--color-fd-primary) 50%, transparent);
    }
    :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
    :where(th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
      padding: calc(var(--spacing) * 2.5);
      border-inline-start: 1px solid var(--color-fd-border);
      background: var(--color-fd-muted);
    }
    :where(th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-inline-start: none;
    }
    :where(th:not(tr:last-child *), td:not(tr:last-child *)):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom: 1px solid var(--color-fd-border);
    }
    :where(td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
      border-inline-start: 1px solid var(--color-fd-border);
      padding: calc(var(--spacing) * 2.5);
    }
    :where(td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-inline-start: none;
    }
    :where(tfoot th, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-prose-th-borders);
    }
    :where(thead th, thead td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-th-borders);
    }
  }
  .prose-no-margin {
    & > :first-child {
      margin-top: 0;
    }
    & > :last-child {
      margin-bottom: 0;
    }
  }
  .mt-\(--fd-nav-height\) {
    margin-top: var(--fd-nav-height);
  }
  .mt-\(--fd-top\) {
    margin-top: var(--fd-top);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-14 {
    margin-top: calc(var(--spacing) * 14);
  }
  .mt-auto {
    margin-top: auto;
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .-mb-1 {
    margin-bottom: calc(var(--spacing) * -1);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-auto {
    margin-bottom: auto;
  }
  .box-content {
    box-sizing: content-box;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }
  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-4\.5 {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6\.5 {
    width: calc(var(--spacing) * 6.5);
    height: calc(var(--spacing) * 6.5);
  }
  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }
  .size-full {
    width: 100%;
    height: 100%;
  }
  .h-\(--fd-height\) {
    height: var(--fd-height);
  }
  .h-\(--fd-toc-height\) {
    height: var(--fd-toc-height);
  }
  .h-\(--radix-navigation-menu-viewport-height\) {
    height: var(--radix-navigation-menu-viewport-height);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-\[100vh\] {
    height: 100vh;
  }
  .h-\[calc\(100dvh-56px\)\] {
    height: calc(100dvh - 56px);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-\[50vh\] {
    max-height: 50vh;
  }
  .max-h-\[400px\] {
    max-height: 400px;
  }
  .max-h-\[460px\] {
    max-height: 460px;
  }
  .max-h-\[600px\] {
    max-height: 600px;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .min-h-10 {
    min-height: calc(var(--spacing) * 10);
  }
  .min-h-\[320px\] {
    min-height: 320px;
  }
  .min-h-\[calc\(100dvh-56px\)\] {
    min-height: calc(100dvh - 56px);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-\(--fd-toc-width\) {
    width: var(--fd-toc-width);
  }
  .w-\(--radix-popover-trigger-width\) {
    width: var(--radix-popover-trigger-width);
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-\[30\%\] {
    width: 30%;
  }
  .w-\[45\%\] {
    width: 45%;
  }
  .w-\[98vw\] {
    width: 98vw;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-\[98vw\] {
    max-width: 98vw;
  }
  .max-w-\[240px\] {
    max-width: 240px;
  }
  .max-w-\[400px\] {
    max-width: 400px;
  }
  .max-w-\[860px\] {
    max-width: 860px;
  }
  .max-w-\[1120px\] {
    max-width: 1120px;
  }
  .max-w-fd-container {
    max-width: var(--spacing-fd-container);
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-screen-sm {
    max-width: var(--breakpoint-sm);
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-\[220px\] {
    min-width: 220px;
  }
  .flex-1 {
    flex: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .origin-\[top_center\] {
    transform-origin: top center;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-full {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-110 {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .-rotate-90 {
    rotate: calc(90deg * -1);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-fd-fade-in {
    animation: var(--animate-fd-fade-in);
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .scroll-m-20 {
    scroll-margin: calc(var(--spacing) * 20);
  }
  .scroll-m-28 {
    scroll-margin: calc(var(--spacing) * 28);
  }
  .list-none {
    list-style-type: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-fd-border {
    :where(& > :not(:last-child)) {
      border-color: var(--color-fd-border);
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-visible {
    overflow: visible;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-\[inherit\] {
    border-radius: inherit;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius);
  }
  .rounded-md {
    border-radius: calc(var(--radius) - 2px);
  }
  .rounded-xl {
    border-radius: calc(var(--radius) + 4px);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
  }
  .border-s-2 {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-fd-foreground\/10 {
    border-color: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }
  .border-fd-primary {
    border-color: var(--color-fd-primary);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-s-blue-500\/50 {
    border-inline-start-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-inline-start-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
    }
  }
  .border-s-orange-500\/50 {
    border-inline-start-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-inline-start-color: color-mix(in oklab, var(--color-orange-500) 50%, transparent);
    }
  }
  .border-s-red-500\/50 {
    border-inline-start-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-inline-start-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
    }
  }
  .bg-\(--shiki-light-bg\) {
    background-color: var(--shiki-light-bg);
  }
  .bg-background {
    background-color: var(--background);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/30 {
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-600\/\[0\.05\] {
    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-600) 5%, transparent);
    }
  }
  .bg-destructive {
    background-color: var(--destructive);
  }
  .bg-fd-accent {
    background-color: var(--color-fd-accent);
  }
  .bg-fd-background {
    background-color: var(--color-fd-background);
  }
  .bg-fd-background\/80 {
    background-color: color-mix(in srgb, hsl(0, 0%, 96%) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-fd-background) 80%, transparent);
    }
  }
  .bg-fd-border {
    background-color: var(--color-fd-border);
  }
  .bg-fd-card {
    background-color: var(--color-fd-card);
  }
  .bg-fd-foreground\/10 {
    background-color: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }
  .bg-fd-muted {
    background-color: var(--color-fd-muted);
  }
  .bg-fd-popover {
    background-color: var(--color-fd-popover);
  }
  .bg-fd-primary {
    background-color: var(--color-fd-primary);
  }
  .bg-fd-primary\/10 {
    background-color: color-mix(in srgb, hsl(0, 0%, 9%) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);
    }
  }
  .bg-fd-secondary {
    background-color: var(--color-fd-secondary);
  }
  .bg-fd-secondary\/50 {
    background-color: color-mix(in srgb, hsl(0, 0%, 93.1%) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-fd-secondary) 50%, transparent);
    }
  }
  .bg-gray-50\/50 {
    background-color: color-mix(in srgb, oklch(98.5% 0.002 247.839) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-50) 50%, transparent);
    }
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-primary {
    background-color: var(--primary);
  }
  .bg-secondary {
    background-color: var(--secondary);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-zinc-50 {
    background-color: var(--color-zinc-50);
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .\[background-image\:var\(--white-gradient\)\,var\(--aurora\)\] {
    background-image: var(--white-gradient),var(--aurora);
  }
  .from-background\/80 {
    --tw-gradient-from: var(--background);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--background) 80%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-100 {
    --tw-gradient-from: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-700 {
    --tw-gradient-from: var(--color-gray-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-600 {
    --tw-gradient-from: var(--color-yellow-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-background\/50 {
    --tw-gradient-via: var(--background);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--background) 50%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-primary\/5 {
    --tw-gradient-via: var(--primary);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--primary) 5%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-background\/30 {
    --tw-gradient-to: var(--background);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--background) 30%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-black {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-600 {
    --tw-gradient-to: var(--color-gray-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-primary\/10 {
    --tw-gradient-to: var(--primary);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-100 {
    --tw-gradient-to: var(--color-purple-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-yellow-500 {
    --tw-gradient-to: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .\[mask-image\:linear-gradient\(to_bottom\,transparent\,white_16px\,white_calc\(100\%-16px\)\,transparent\)\] {
    mask-image: linear-gradient(to bottom,transparent,white 16px,white calc(100% - 16px),transparent);
  }
  .\[mask-image\:radial-gradient\(ellipse_at_100\%_0\%\,black_10\%\,var\(--transparent\)_70\%\)\] {
    mask-image: radial-gradient(ellipse at 100% 0%,black 10%,var(--transparent) 70%);
  }
  .\[background-size\:300\%\,_200\%\] {
    background-size: 300%, 200%;
  }
  .\[background-position\:50\%_50\%\,50\%_50\%\] {
    background-position: 50% 50%,50% 50%;
  }
  .fill-blue-500 {
    fill: var(--color-blue-500);
  }
  .fill-orange-500 {
    fill: var(--color-orange-500);
  }
  .fill-red-500 {
    fill: var(--color-red-500);
  }
  .fill-transparent {
    fill: transparent;
  }
  .stroke-current\/25 {
    stroke: currentcolor;
    @supports (color: color-mix(in lab, red, red)) {
      stroke: color-mix(in oklab, currentcolor 25%, transparent);
    }
  }
  .stroke-fd-foreground\/10 {
    stroke: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      stroke: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);
    }
  }
  .stroke-white {
    stroke: var(--color-white);
  }
  .stroke-yellow-500 {
    stroke: var(--color-yellow-500);
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-px {
    padding: 1px;
  }
  .px-\(--fd-layout-offset\) {
    padding-inline: var(--fd-layout-offset);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-7 {
    padding-block: calc(var(--spacing) * 7);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }
  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }
  .ps-6 {
    padding-inline-start: calc(var(--spacing) * 6);
  }
  .ps-8 {
    padding-inline-start: calc(var(--spacing) * 8);
  }
  .ps-\[calc\(var\(--fd-layout-offset\)\+var\(--fd-sidebar-width\)\)\] {
    padding-inline-start: calc(var(--fd-layout-offset) + var(--fd-sidebar-width));
  }
  .ps-px {
    padding-inline-start: 1px;
  }
  .pe-\(--fd-layout-offset\) {
    padding-inline-end: var(--fd-layout-offset);
  }
  .pe-2 {
    padding-inline-end: calc(var(--spacing) * 2);
  }
  .pe-4 {
    padding-inline-end: calc(var(--spacing) * 4);
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-12 {
    padding-top: calc(var(--spacing) * 12);
  }
  .pt-14 {
    padding-top: calc(var(--spacing) * 14);
  }
  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .text-center {
    text-align: center;
  }
  .text-end {
    text-align: end;
  }
  .text-start {
    text-align: start;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[13px\] {
    font-size: 13px;
  }
  .text-\[15px\] {
    font-size: 15px;
  }
  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-tighter {
    --tw-tracking: var(--tracking-tighter);
    letter-spacing: var(--tracking-tighter);
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .\[overflow-wrap\:anywhere\] {
    overflow-wrap: anywhere;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-fd-accent-foreground {
    color: var(--color-fd-accent-foreground);
  }
  .text-fd-card {
    color: var(--color-fd-card);
  }
  .text-fd-card-foreground {
    color: var(--color-fd-card-foreground);
  }
  .text-fd-foreground {
    color: var(--color-fd-foreground);
  }
  .text-fd-foreground\/30 {
    color: color-mix(in srgb, hsl(0, 0%, 3.9%) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-fd-foreground) 30%, transparent);
    }
  }
  .text-fd-foreground\/80 {
    color: color-mix(in srgb, hsl(0, 0%, 3.9%) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-fd-foreground) 80%, transparent);
    }
  }
  .text-fd-muted-foreground {
    color: var(--color-fd-muted-foreground);
  }
  .text-fd-popover-foreground {
    color: var(--color-fd-popover-foreground);
  }
  .text-fd-primary {
    color: var(--color-fd-primary);
  }
  .text-fd-primary-foreground {
    color: var(--color-fd-primary-foreground);
  }
  .text-fd-secondary-foreground {
    color: var(--color-fd-secondary-foreground);
  }
  .text-foreground {
    color: var(--foreground);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-muted-foreground {
    color: var(--muted-foreground);
  }
  .text-muted-foreground\/100 {
    color: var(--muted-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--muted-foreground) 100%, transparent);
    }
  }
  .text-primary {
    color: var(--primary);
  }
  .text-primary-foreground {
    color: var(--primary-foreground);
  }
  .text-purple-400 {
    color: var(--color-purple-400);
  }
  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }
  .text-slate-950 {
    color: var(--color-slate-950);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .italic {
    font-style: italic;
  }
  .underline {
    text-decoration-line: underline;
  }
  .decoration-gray-300 {
    text-decoration-color: var(--color-gray-300);
  }
  .underline-offset-2 {
    text-underline-offset: 2px;
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .underline-offset-5 {
    text-underline-offset: 5px;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-37 {
    opacity: 37%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur-\[10px\] {
    --tw-blur: blur(10px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[width\,height\] {
    transition-property: width,height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-600 {
    --tw-duration: 600ms;
    transition-duration: 600ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .will-change-transform {
    will-change: transform;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .\[--aurora\:repeating-linear-gradient\(100deg\,var\(--blue-500\)_10\%\,var\(--indigo-300\)_15\%\,var\(--blue-300\)_20\%\,var\(--violet-200\)_25\%\,var\(--blue-400\)_30\%\)\] {
    --aurora: repeating-linear-gradient(100deg,var(--blue-500) 10%,var(--indigo-300) 15%,var(--blue-300) 20%,var(--violet-200) 25%,var(--blue-400) 30%);
  }
  .\[--dark-gradient\:repeating-linear-gradient\(100deg\,var\(--black\)_0\%\,var\(--black\)_7\%\,var\(--transparent\)_10\%\,var\(--transparent\)_12\%\,var\(--black\)_16\%\)\] {
    --dark-gradient: repeating-linear-gradient(100deg,var(--black) 0%,var(--black) 7%,var(--transparent) 10%,var(--transparent) 12%,var(--black) 16%);
  }
  .\[--fd-nav-height\:calc\(var\(--spacing\)\*14\)\] {
    --fd-nav-height: calc(var(--spacing) * 14);
  }
  .\[--fd-tocnav-height\:36px\] {
    --fd-tocnav-height: 36px;
  }
  .\[--white-gradient\:repeating-linear-gradient\(100deg\,var\(--white\)_0\%\,var\(--white\)_7\%\,var\(--transparent\)_10\%\,var\(--transparent\)_12\%\,var\(--white\)_16\%\)\] {
    --white-gradient: repeating-linear-gradient(100deg,var(--white) 0%,var(--white) 7%,var(--transparent) 10%,var(--transparent) 12%,var(--white) 16%);
  }
  .running {
    animation-play-state: running;
  }
  .\*\:col-start-1 {
    :is(& > *) {
      grid-column-start: 1;
    }
  }
  .\*\:row-start-1 {
    :is(& > *) {
      grid-row-start: 1;
    }
  }
  .group-hover\:-translate-x-1 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:text-blue-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-data-\[state\=open\]\:rotate-180 {
    &:is(:where(.group)[data-state="open"] *) {
      rotate: 180deg;
    }
  }
  .group-data-\[state\=open\]\/accordion\:rotate-90 {
    &:is(:where(.group\/accordion)[data-state="open"] *) {
      rotate: 90deg;
    }
  }
  .peer-hover\:opacity-100 {
    &:is(:where(.peer):hover ~ *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .placeholder\:text-fd-muted-foreground {
    &::placeholder {
      color: var(--color-fd-muted-foreground);
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:inset-0 {
    &::after {
      content: var(--tw-content);
      inset: calc(var(--spacing) * 0);
    }
  }
  .after\:animate-aurora {
    &::after {
      content: var(--tw-content);
      animation: var(--animate-aurora);
    }
  }
  .after\:\[background-image\:var\(--white-gradient\)\,var\(--aurora\)\] {
    &::after {
      content: var(--tw-content);
      background-image: var(--white-gradient),var(--aurora);
    }
  }
  .after\:\[background-size\:200\%\,_100\%\] {
    &::after {
      content: var(--tw-content);
      background-size: 200%, 100%;
    }
  }
  .after\:\[background-attachment\:fixed\] {
    &::after {
      content: var(--tw-content);
      background-attachment: fixed;
    }
  }
  .after\:mix-blend-difference {
    &::after {
      content: var(--tw-content);
      mix-blend-mode: difference;
    }
  }
  .after\:content-\[\"\"\] {
    &::after {
      content: var(--tw-content);
      --tw-content: "";
      content: var(--tw-content);
    }
  }
  .first\:pt-0 {
    &:first-child {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .last\:pb-0 {
    &:last-child {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .empty\:hidden {
    &:empty {
      display: none;
    }
  }
  .hover\:-translate-y-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-2 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:bg-accent {
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
  }
  .hover\:bg-blue-600\/\[0\.08\] {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 8%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-600) 8%, transparent);
        }
      }
    }
  }
  .hover\:bg-destructive\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-fd-accent {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-fd-accent);
      }
    }
  }
  .hover\:bg-fd-accent\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, hsl(0, 0%, 90.1%) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-fd-accent\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, hsl(0, 0%, 90.1%) 80%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-fd-accent) 80%, transparent);
        }
      }
    }
  }
  .hover\:bg-fd-primary\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, hsl(0, 0%, 9%) 80%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-fd-primary) 80%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--primary) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-secondary\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--secondary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
        }
      }
    }
  }
  .hover\:from-gray-800 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-gray-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-gray-900 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-gray-900);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:text-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
  }
  .hover\:text-blue-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .hover\:text-fd-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--color-fd-accent-foreground);
      }
    }
  }
  .hover\:text-fd-accent-foreground\/80 {
    &:hover {
      @media (hover: hover) {
        color: color-mix(in srgb, hsl(0, 0%, 9%) 80%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-fd-accent-foreground) 80%, transparent);
        }
      }
    }
  }
  .hover\:text-fd-popover-foreground\/50 {
    &:hover {
      @media (hover: hover) {
        color: color-mix(in srgb, hsl(0, 0%, 15.1%) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-fd-popover-foreground) 50%, transparent);
        }
      }
    }
  }
  .hover\:text-primary {
    &:hover {
      @media (hover: hover) {
        color: var(--primary);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:decoration-\[\#13C2FF\] {
    &:hover {
      @media (hover: hover) {
        text-decoration-color: #13C2FF;
      }
    }
  }
  .hover\:opacity-80 {
    &:hover {
      @media (hover: hover) {
        opacity: 80%;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:transition-none {
    &:hover {
      @media (hover: hover) {
        transition-property: none;
      }
    }
  }
  .focus-visible\:border-ring {
    &:focus-visible {
      border-color: var(--ring);
    }
  }
  .focus-visible\:ring-\[3px\] {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-destructive\/20 {
    &:focus-visible {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
  .focus-visible\:ring-ring\/50 {
    &:focus-visible {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
      }
    }
  }
  .focus-visible\:outline-none {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .has-focus-visible\:bg-fd-accent {
    &:has(*:focus-visible) {
      background-color: var(--color-fd-accent);
    }
  }
  .has-\[\>svg\]\:px-2\.5 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 2.5);
    }
  }
  .has-\[\>svg\]\:px-3 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .has-\[\>svg\]\:px-4 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .aria-invalid\:border-destructive {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
  }
  .aria-invalid\:ring-destructive\/20 {
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
  .data-\[active\=true\]\:font-medium {
    &[data-active="true"] {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .data-\[active\=true\]\:text-fd-primary {
    &[data-active="true"] {
      color: var(--color-fd-primary);
    }
  }
  .data-\[collapsed\=false\]\:hidden {
    &[data-collapsed="false"] {
      display: none;
    }
  }
  .data-\[motion\=from-end\]\:animate-fd-enterFromRight {
    &[data-motion="from-end"] {
      animation: var(--animate-fd-enterFromRight);
    }
  }
  .data-\[motion\=from-start\]\:animate-fd-enterFromLeft {
    &[data-motion="from-start"] {
      animation: var(--animate-fd-enterFromLeft);
    }
  }
  .data-\[motion\=to-end\]\:animate-fd-exitToRight {
    &[data-motion="to-end"] {
      animation: var(--animate-fd-exitToRight);
    }
  }
  .data-\[motion\=to-start\]\:animate-fd-exitToLeft {
    &[data-motion="to-start"] {
      animation: var(--animate-fd-exitToLeft);
    }
  }
  .data-\[state\=active\]\:border-fd-primary {
    &[data-state="active"] {
      border-color: var(--color-fd-primary);
    }
  }
  .data-\[state\=active\]\:text-fd-primary {
    &[data-state="active"] {
      color: var(--color-fd-primary);
    }
  }
  .data-\[state\=closed\]\:animate-fd-accordion-up {
    &[data-state="closed"] {
      animation: var(--animate-fd-accordion-up);
    }
  }
  .data-\[state\=closed\]\:animate-fd-collapsible-up {
    &[data-state="closed"] {
      animation: var(--animate-fd-collapsible-up);
    }
  }
  .data-\[state\=closed\]\:animate-fd-dialog-out {
    &[data-state="closed"] {
      animation: var(--animate-fd-dialog-out);
    }
  }
  .data-\[state\=closed\]\:animate-fd-fade-out {
    &[data-state="closed"] {
      animation: var(--animate-fd-fade-out);
    }
  }
  .data-\[state\=closed\]\:animate-fd-nav-menu-out {
    &[data-state="closed"] {
      animation: var(--animate-fd-nav-menu-out);
    }
  }
  .data-\[state\=closed\]\:animate-fd-popover-out {
    &[data-state="closed"] {
      animation: var(--animate-fd-popover-out);
    }
  }
  .data-\[state\=hidden\]\:animate-fd-fade-out {
    &[data-state="hidden"] {
      animation: var(--animate-fd-fade-out);
    }
  }
  .data-\[state\=open\]\:animate-fd-accordion-down {
    &[data-state="open"] {
      animation: var(--animate-fd-accordion-down);
    }
  }
  .data-\[state\=open\]\:animate-fd-collapsible-down {
    &[data-state="open"] {
      animation: var(--animate-fd-collapsible-down);
    }
  }
  .data-\[state\=open\]\:animate-fd-dialog-in {
    &[data-state="open"] {
      animation: var(--animate-fd-dialog-in);
    }
  }
  .data-\[state\=open\]\:animate-fd-fade-in {
    &[data-state="open"] {
      animation: var(--animate-fd-fade-in);
    }
  }
  .data-\[state\=open\]\:animate-fd-nav-menu-in {
    &[data-state="open"] {
      animation: var(--animate-fd-nav-menu-in);
    }
  }
  .data-\[state\=open\]\:animate-fd-popover-in {
    &[data-state="open"] {
      animation: var(--animate-fd-popover-in);
    }
  }
  .data-\[state\=open\]\:bg-fd-accent\/50 {
    &[data-state="open"] {
      background-color: color-mix(in srgb, hsl(0, 0%, 90.1%) 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);
      }
    }
  }
  .max-xl\:end-4 {
    @media (width < 80rem) {
      inset-inline-end: calc(var(--spacing) * 4);
    }
  }
  .max-xl\:hidden {
    @media (width < 80rem) {
      display: none;
    }
  }
  .max-lg\:hidden {
    @media (width < 64rem) {
      display: none;
    }
  }
  .max-md\:inset-x-0 {
    @media (width < 48rem) {
      inset-inline: calc(var(--spacing) * 0);
    }
  }
  .max-md\:bottom-0 {
    @media (width < 48rem) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .max-md\:hidden {
    @media (width < 48rem) {
      display: none;
    }
  }
  .max-md\:bg-fd-background\/80 {
    @media (width < 48rem) {
      background-color: color-mix(in srgb, hsl(0, 0%, 96%) 80%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-fd-background) 80%, transparent);
      }
    }
  }
  .max-md\:text-\[15px\] {
    @media (width < 48rem) {
      font-size: 15px;
    }
  }
  .max-md\:backdrop-blur-lg {
    @media (width < 48rem) {
      --tw-backdrop-blur: blur(var(--blur-lg));
      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    }
  }
  .max-md\:data-\[open\=false\]\:invisible {
    @media (width < 48rem) {
      &[data-open="false"] {
        visibility: hidden;
      }
    }
  }
  .max-sm\:mt-2 {
    @media (width < 40rem) {
      margin-top: calc(var(--spacing) * 2);
    }
  }
  .max-sm\:hidden {
    @media (width < 40rem) {
      display: none;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\:py-0 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:sticky {
    @media (width >= 48rem) {
      position: sticky;
    }
  }
  .md\:ms-auto {
    @media (width >= 48rem) {
      margin-inline-start: auto;
    }
  }
  .md\:-me-\(--fd-sidebar-width\) {
    @media (width >= 48rem) {
      margin-inline-end: calc(var(--fd-sidebar-width) * -1);
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:h-\(--fd-sidebar-height\) {
    @media (width >= 48rem) {
      height: var(--fd-sidebar-height);
    }
  }
  .md\:w-\(--fd-sidebar-width\) {
    @media (width >= 48rem) {
      width: var(--fd-sidebar-width);
    }
  }
  .md\:-translate-x-\(--fd-sidebar-offset\) {
    @media (width >= 48rem) {
      --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:translate-x-0 {
    @media (width >= 48rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:border-e {
    @media (width >= 48rem) {
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 1px;
    }
  }
  .md\:px-6 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:py-1\.5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }
  .md\:ps-\(--fd-layout-offset\) {
    @media (width >= 48rem) {
      padding-inline-start: var(--fd-layout-offset);
    }
  }
  .md\:pt-0 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .md\:pt-2\.5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 2.5);
    }
  }
  .md\:pt-3\.5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 3.5);
    }
  }
  .md\:pt-4 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 4);
    }
  }
  .md\:pt-12 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 12);
    }
  }
  .md\:text-7xl {
    @media (width >= 48rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:leading-normal {
    @media (width >= 48rem) {
      --tw-leading: var(--leading-normal);
      line-height: var(--leading-normal);
    }
  }
  .md\:opacity-0 {
    @media (width >= 48rem) {
      opacity: 0%;
    }
  }
  .md\:transition-all {
    @media (width >= 48rem) {
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
  .md\:\[--fd-nav-height\:0px\] {
    @media (width >= 48rem) {
      --fd-nav-height: 0px;
    }
  }
  .md\:\[--fd-sidebar-width\:268px\] {
    @media (width >= 48rem) {
      --fd-sidebar-width: 268px;
    }
  }
  .md\:\[--fd-sidebar-width\:286px\] {
    @media (width >= 48rem) {
      --fd-sidebar-width: 286px;
    }
  }
  .lg\:mt-2 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 2);
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:h-12 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .lg\:w-\[calc\(100\%-1rem\)\] {
    @media (width >= 64rem) {
      width: calc(100% - 1rem);
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:items-center {
    @media (width >= 64rem) {
      align-items: center;
    }
  }
  .lg\:rounded-2xl {
    @media (width >= 64rem) {
      border-radius: var(--radius-2xl);
    }
  }
  .lg\:border {
    @media (width >= 64rem) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .lg\:pt-8 {
    @media (width >= 64rem) {
      padding-top: calc(var(--spacing) * 8);
    }
  }
  .lg\:pb-60 {
    @media (width >= 64rem) {
      padding-bottom: calc(var(--spacing) * 60);
    }
  }
  .lg\:\[--fd-nav-height\:calc\(var\(--spacing\)\*24\)\] {
    @media (width >= 64rem) {
      --fd-nav-height: calc(var(--spacing) * 24);
    }
  }
  .lg\:\[--fd-sidebar-width\:286px\] {
    @media (width >= 64rem) {
      --fd-sidebar-width: 286px;
    }
  }
  .xl\:start-4 {
    @media (width >= 80rem) {
      inset-inline-start: calc(var(--spacing) * 4);
    }
  }
  .xl\:mx-auto {
    @media (width >= 80rem) {
      margin-inline: auto;
    }
  }
  .xl\:hidden {
    @media (width >= 80rem) {
      display: none;
    }
  }
  .xl\:px-12 {
    @media (width >= 80rem) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .xl\:\[--fd-toc-width\:286px\] {
    @media (width >= 80rem) {
      --fd-toc-width: 286px;
    }
  }
  .xl\:\[--fd-tocnav-height\:0px\] {
    @media (width >= 80rem) {
      --fd-tocnav-height: 0px;
    }
  }
  .\@max-lg\:col-span-full {
    @container (width < 32rem) {
      grid-column: 1 / -1;
    }
  }
  .rtl\:-scale-x-100 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      --tw-scale-x: calc(100% * -1);
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .rtl\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
  }
  .rtl\:md\:translate-x-\(--fd-sidebar-offset\) {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      @media (width >= 48rem) {
        --tw-translate-x: var(--fd-sidebar-offset);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .dark\:border-gray-700 {
    &:is(.dark *) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-input {
    &:is(.dark *) {
      border-color: var(--input);
    }
  }
  .dark\:border-slate-700 {
    &:is(.dark *) {
      border-color: var(--color-slate-700);
    }
  }
  .dark\:bg-\(--shiki-dark-bg\) {
    &:is(.dark *) {
      background-color: var(--shiki-dark-bg);
    }
  }
  .dark\:bg-blue-900\/30 {
    &:is(.dark *) {
      background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
      }
    }
  }
  .dark\:bg-destructive\/60 {
    &:is(.dark *) {
      background-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
  }
  .dark\:bg-gray-700 {
    &:is(.dark *) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    &:is(.dark *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-900\/50 {
    &:is(.dark *) {
      background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
      }
    }
  }
  .dark\:bg-input\/30 {
    &:is(.dark *) {
      background-color: var(--input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--input) 30%, transparent);
      }
    }
  }
  .dark\:bg-slate-700 {
    &:is(.dark *) {
      background-color: var(--color-slate-700);
    }
  }
  .dark\:bg-slate-800 {
    &:is(.dark *) {
      background-color: var(--color-slate-800);
    }
  }
  .dark\:bg-zinc-900 {
    &:is(.dark *) {
      background-color: var(--color-zinc-900);
    }
  }
  .dark\:\[background-image\:var\(--dark-gradient\)\,var\(--aurora\)\] {
    &:is(.dark *) {
      background-image: var(--dark-gradient),var(--aurora);
    }
  }
  .dark\:from-blue-900\/20 {
    &:is(.dark *) {
      --tw-gradient-from: color-mix(in srgb, oklch(37.9% 0.146 265.522) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-purple-900\/20 {
    &:is(.dark *) {
      --tw-gradient-to: color-mix(in srgb, oklch(38.1% 0.176 304.987) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-gradient-to: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:text-blue-300 {
    &:is(.dark *) {
      color: var(--color-blue-300);
    }
  }
  .dark\:text-gray-300 {
    &:is(.dark *) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-gray-400 {
    &:is(.dark *) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-white {
    &:is(.dark *) {
      color: var(--color-white);
    }
  }
  .dark\:opacity-57 {
    &:is(.dark *) {
      opacity: 57%;
    }
  }
  .dark\:invert-0 {
    &:is(.dark *) {
      --tw-invert: invert(0%);
      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
    }
  }
  .dark\:group-hover\:text-blue-400 {
    &:is(.dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          color: var(--color-blue-400);
        }
      }
    }
  }
  .after\:dark\:\[background-image\:var\(--dark-gradient\)\,var\(--aurora\)\] {
    &::after {
      content: var(--tw-content);
      &:is(.dark *) {
        background-image: var(--dark-gradient),var(--aurora);
      }
    }
  }
  .dark\:hover\:bg-accent\/50 {
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--accent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--accent) 50%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:bg-input\/50 {
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--input);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--input) 50%, transparent);
          }
        }
      }
    }
  }
  .dark\:focus-visible\:ring-destructive\/40 {
    &:is(.dark *) {
      &:focus-visible {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
      }
    }
  }
  .dark\:aria-invalid\:ring-destructive\/40 {
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
      }
    }
  }
  .\[\&_svg\]\:pointer-events-none {
    & svg {
      pointer-events: none;
    }
  }
  .\[\&_svg\]\:\!size-4\.5 {
    & svg {
      width: calc(var(--spacing) * 4.5) !important;
      height: calc(var(--spacing) * 4.5) !important;
    }
  }
  .\[\&_svg\]\:size-3\.5 {
    & svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .\[\&_svg\]\:size-4 {
    & svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\[\&_svg\]\:size-4\.5 {
    & svg {
      width: calc(var(--spacing) * 4.5);
      height: calc(var(--spacing) * 4.5);
    }
  }
  .\[\&_svg\]\:size-5 {
    & svg {
      width: calc(var(--spacing) * 5);
      height: calc(var(--spacing) * 5);
    }
  }
  .\[\&_svg\]\:shrink-0 {
    & svg {
      flex-shrink: 0;
    }
  }
  .md\:\[\&_svg\]\:size-4\.5 {
    @media (width >= 48rem) {
      & svg {
        width: calc(var(--spacing) * 4.5);
        height: calc(var(--spacing) * 4.5);
      }
    }
  }
  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 {
    & svg:not([class*='size-']) {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\[\&\>figure\:only-child\]\:-m-4 {
    &>figure:only-child {
      margin: calc(var(--spacing) * -4);
    }
  }
  .\[\&\>figure\:only-child\]\:rounded-none {
    &>figure:only-child {
      border-radius: 0;
    }
  }
  .\[\&\>figure\:only-child\]\:border-none {
    &>figure:only-child {
      --tw-border-style: none;
      border-style: none;
    }
  }
  .\[\@media\(hover\:hover\)\]\:opacity-0 {
    @media (hover:hover) {
      opacity: 0%;
    }
  }
}
.dark {
  --color-fd-background: hsl(0, 0%, 7.04%);
  --color-fd-foreground: hsl(0, 0%, 92%);
  --color-fd-muted: hsl(0, 0%, 12.9%);
  --color-fd-muted-foreground: hsl(0, 0%, 60.9%);
  --color-fd-popover: hsl(0, 0%, 9.8%);
  --color-fd-popover-foreground: hsl(0, 0%, 88%);
  --color-fd-card: hsl(0, 0%, 9.8%);
  --color-fd-card-foreground: hsl(0, 0%, 98%);
  --color-fd-border: hsl(0, 0%, 14%);
  --color-fd-primary: hsl(0, 0%, 98%);
  --color-fd-primary-foreground: hsl(0, 0%, 9%);
  --color-fd-secondary: hsl(0, 0%, 12.9%);
  --color-fd-secondary-foreground: hsl(0, 0%, 98%);
  --color-fd-accent: hsl(0, 0%, 16.9%);
  --color-fd-accent-foreground: hsl(0, 0%, 90%);
  --color-fd-ring: hsl(0, 0%, 54.9%);
}
.dark #nd-sidebar {
  --color-fd-muted: hsl(0, 0%, 16%);
  --color-fd-secondary: hsl(0, 0%, 18%);
  --color-fd-muted-foreground: hsl(0, 0%, 72%);
}
.shiki code span {
  color: var(--shiki-light);
}
.dark .shiki code span {
  color: var(--shiki-dark);
}
.fd-codeblock pre > * {
  display: grid;
  font-size: 13px;
}
.shiki code .diff.remove {
  background-color: var(--fd-diff-remove-color);
  opacity: 0.7;
}
.shiki code .diff::before {
  position: absolute;
  left: 6px;
}
.shiki code .diff.remove::before {
  content: '-';
  color: var(--fd-diff-remove-symbol-color);
}
.shiki code .diff.add {
  background-color: var(--fd-diff-add-color);
}
.shiki code .diff.add::before {
  content: '+';
  color: var(--fd-diff-add-symbol-color);
}
.shiki code .diff {
  margin: 0 -16px;
  padding: 0 16px;
  position: relative;
}
.shiki .highlighted {
  margin: 0 -16px;
  padding: 0 16px;
  background-color: color-mix( in oklab, hsl(0, 0%, 9%) 10%, transparent );
  @supports (color: color-mix(in lab, red, red)) {
    background-color: color-mix( in oklab, var(--color-fd-primary) 10%, transparent );
  }
}
.shiki .highlighted-word {
  padding: 1px 2px;
  margin: -1px -3px;
  border: 1px solid color-mix(in srgb, hsl(0, 0%, 9%) 50%, transparent);
  @supports (color: color-mix(in lab, red, red)) {
    border: 1px solid color-mix(in oklab, var(--color-fd-primary) 50%, transparent);
  }
  background-color: color-mix( in oklab, hsl(0, 0%, 9%) 10%, transparent );
  @supports (color: color-mix(in lab, red, red)) {
    background-color: color-mix( in oklab, var(--color-fd-primary) 10%, transparent );
  }
  border-radius: 2px;
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    border-color: var(--color-fd-border, currentColor);
  }
  body {
    background-color: var(--color-fd-background);
    color: var(--color-fd-foreground);
  }
  [data-rmiz-modal-overlay='visible'] {
    background-color: var(--color-fd-background);
  }
}
@property --radix-collapsible-content-height {
  syntax: '<length>';
  inherits: false;
  initial-value: 0px;
}
@font-face {
  font-family: 'Jokker';
  src: url('/fonts/Jokker.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Jokker';
  src: url('/fonts/Jokker-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Jokker';
  src: url('/fonts/Jokker-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Jokker';
  src: url('/fonts/Jokker-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
:root {
  --font-jokker: 'Jokker', system-ui, sans-serif;
}
body {
  font-family: var(--font-jokker);
  font-weight: 500;
}
.animation-delay-300 {
  animation-delay: 300ms;
}
.animation-delay-500 {
  animation-delay: 500ms;
}
.animation-delay-700 {
  animation-delay: 700ms;
}
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}
@layer base {
  * {
    border-color: var(--border);
    outline-color: var(--ring);
    @supports (color: color-mix(in lab, red, red)) {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}
@layer base {
  :root {
    --motion-default-timing: cubic-bezier(.165, .84, .44, 1);
    --motion-bounce: linear(0, 0.004, 0.016, 0.035, 0.063, 0.098, 0.141 13.6%, 0.25, 0.391, 0.563, 0.765,1, 0.891 40.9%, 0.848, 0.813, 0.785, 0.766, 0.754, 0.75, 0.754, 0.766, 0.785,0.813, 0.848, 0.891 68.2%, 1 72.7%, 0.973, 0.953, 0.941, 0.938, 0.941, 0.953,0.973, 1, 0.988, 0.984, 0.988, 1);
    --motion-spring-smooth: linear(0, 0.001 0.44%, 0.0045 0.94%, 0.0195 2.03%, 0.0446 3.19%, 0.0811 4.5%, 0.1598 6.82%, 0.3685 12.34%, 0.4693 15.17%, 0.5663, 0.6498 21.27%, 0.7215 24.39%, 0.7532 25.98%, 0.7829 27.65%, 0.8105, 0.8349 31.14%, 0.8573 32.95%, 0.8776 34.84%, 0.8964 36.87%, 0.9136 39.05%, 0.929 41.37%, 0.9421 43.77%, 0.9537 46.38%, 0.9636 49.14%, 0.9789 55.31%, 0.9888 62.35%, 0.9949 71.06%, 0.9982 82.52%, 0.9997 99.94%);
    --motion-spring-snappy: linear(0, 0.0014, 0.0053 1.02%, 0.0126, 0.0227 2.18%, 0.0517 3.41%, 0.094 4.79%, 0.1865 7.26%, 0.4182 12.77%, 0.5246 15.46%, 0.6249, 0.7112, 0.7831 23.95%, 0.8146 25.4%, 0.844, 0.8699 28.45%, 0.8935, 0.9139 31.64%, 0.932, 0.9473, 0.9601 36.65%, 0.9714 38.47%, 0.9808 40.35%, 0.9948 44.49%, 1.0031 49.43%, 1.0057 53.35%, 1.0063 58.14%, 1.0014 80.78%, 1.0001 99.94%);
    --motion-spring-bouncy: linear(0, 0.0018, 0.0069, 0.0151 1.74%, 0.0277 2.4%, 0.062 3.7%, 0.1115 5.15%, 0.2211 7.77%, 0.4778 13.21%, 0.5912 15.75%, 0.6987 18.44%, 0.7862 20.98%, 0.861 23.59%, 0.8926, 0.9205, 0.945 27.51%, 0.9671 28.89%, 0.9868, 1.003 31.79%, 1.0224 34.11%, 1.0358 36.58%, 1.0436 39.27%, 1.046 42.31%, 1.0446 44.71%, 1.0406 47.47%, 1.0118 61.84%, 1.0027 69.53%, 0.9981 80.49%, 0.9991 99.94%);
    --motion-spring-bouncier: linear(0, 0.0023, 0.0088, 0.0194 1.59%, 0.035 2.17%, 0.078 3.33%, 0.1415 4.64%, 0.2054 5.75%, 0.2821 6.95%, 0.5912 11.45%, 0.7205 13.43%, 0.8393 15.45%, 0.936 17.39%, 0.9778, 1.015, 1.0477, 1.0759, 1.0998 22.22%, 1.1203, 1.1364, 1.1484 25.26%, 1.1586 26.61%, 1.1629 28.06%, 1.1613 29.56%, 1.1537 31.2%, 1.1434 32.6%, 1.1288 34.19%, 1.0508 41.29%, 1.0174 44.87%, 1.0025 46.89%, 0.9911 48.87%, 0.9826 50.9%, 0.9769 53.03%, 0.9735 56.02%, 0.9748 59.45%, 0.9964 72.64%, 1.0031 79.69%, 1.0042 86.83%, 1.0008 99.97%);
    --motion-spring-bounciest: linear(0, 0.0032, 0.0131, 0.0294, 0.0524, 0.0824, 0.1192 1.54%, 0.2134 2.11%, 0.3102 2.59%, 0.4297 3.13%, 0.8732 4.95%, 1.0373, 1.1827 6.36%, 1.2972 7.01%, 1.3444, 1.3859, 1.4215, 1.4504, 1.4735, 1.4908, 1.5024, 1.5084 9.5%, 1.5091, 1.5061, 1.4993, 1.4886, 1.4745, 1.4565 11.11%, 1.4082 11.7%, 1.3585 12.2%, 1.295 12.77%, 1.0623 14.64%, 0.9773, 0.9031 16.08%, 0.8449 16.73%, 0.8014, 0.7701 17.95%, 0.7587, 0.7501, 0.7443, 0.7412 19.16%, 0.7421 19.68%, 0.7508 20.21%, 0.7672 20.77%, 0.7917 21.37%, 0.8169 21.87%, 0.8492 22.43%, 0.9681 24.32%, 1.0114, 1.0492 25.75%, 1.0789 26.41%, 1.1008, 1.1167, 1.1271, 1.1317 28.81%, 1.1314, 1.1271 29.87%, 1.1189 30.43%, 1.1063 31.03%, 1.0769 32.11%, 0.9941 34.72%, 0.9748 35.43%, 0.9597 36.09%, 0.9487, 0.9407, 0.9355, 0.933 38.46%, 0.9344 39.38%, 0.9421 40.38%, 0.9566 41.5%, 0.9989 44.12%, 1.0161 45.37%, 1.029 46.75%, 1.0341 48.1%, 1.0335 49.04%, 1.0295 50.05%, 1.0221 51.18%, 0.992 55.02%, 0.9854 56.38%, 0.9827 57.72%, 0.985 59.73%, 1.004 64.67%, 1.0088 67.34%, 1.0076 69.42%, 0.9981 74.28%, 0.9956 76.85%, 0.9961 79.06%, 1.0023 86.46%, 0.999 95.22%, 0.9994 100%);
  }
  * {
    --motion-origin-scale-x: 100%;
    --motion-origin-scale-y: 100%;
    --motion-origin-translate-x: 0%;
    --motion-origin-translate-y: 0%;
    --motion-origin-rotate: 0deg;
    --motion-origin-blur: 0px;
    --motion-origin-grayscale: 0%;
    --motion-origin-opacity: 100%;
    --motion-origin-background-color: ;
    --motion-origin-text-color: ;
    --motion-end-scale-x: 100%;
    --motion-end-scale-y: 100%;
    --motion-end-translate-x: 0%;
    --motion-end-translate-y: 0%;
    --motion-end-rotate: 0deg;
    --motion-end-blur: 0px;
    --motion-end-grayscale: 0%;
    --motion-end-opacity: 100%;
    --motion-end-background-color: ;
    --motion-end-text-color: ;
    --motion-loop-scale-x: 100%;
    --motion-loop-scale-y: 100%;
    --motion-loop-translate-x: 0%;
    --motion-loop-translate-y: 0%;
    --motion-loop-rotate: 0deg;
    --motion-loop-blur: 0px;
    --motion-loop-grayscale: 0%;
    --motion-loop-opacity: 100%;
    --motion-loop-background-color: ;
    --motion-loop-text-color: ;
    --motion-duration: 700ms;
    --motion-timing: var(--motion-default-timing);
    --motion-perceptual-duration-multiplier: 1;
    --motion-delay: 0ms;
    --motion-loop-count: infinite;
    --motion-scale-duration: var(--motion-duration);
    --motion-scale-timing: var(--motion-timing);
    --motion-scale-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-scale-delay: var(--motion-delay);
    --motion-scale-loop-count: var(--motion-loop-count);
    --motion-translate-duration: var(--motion-duration);
    --motion-translate-timing: var(--motion-timing);
    --motion-translate-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-translate-delay: var(--motion-delay);
    --motion-translate-loop-count: var(--motion-loop-count);
    --motion-rotate-duration: var(--motion-duration);
    --motion-rotate-timing: var(--motion-timing);
    --motion-rotate-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-rotate-delay: var(--motion-delay);
    --motion-rotate-loop-count: var(--motion-loop-count);
    --motion-filter-duration: var(--motion-duration);
    --motion-filter-timing: var(--motion-timing);
    --motion-filter-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-filter-delay: var(--motion-delay);
    --motion-filter-loop-count: var(--motion-loop-count);
    --motion-opacity-duration: var(--motion-duration);
    --motion-opacity-timing: var(--motion-timing);
    --motion-opacity-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-opacity-delay: var(--motion-delay);
    --motion-opacity-loop-count: var(--motion-loop-count);
    --motion-background-color-duration: var(--motion-duration);
    --motion-background-color-timing: var(--motion-timing);
    --motion-background-color-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-background-color-delay: var(--motion-delay);
    --motion-background-color-loop-count: var(--motion-loop-count);
    --motion-text-color-duration: var(--motion-duration);
    --motion-text-color-timing: var(--motion-timing);
    --motion-text-color-perceptual-duration-multiplier: var(--motion-perceptual-duration-multiplier);
    --motion-text-color-delay: var(--motion-delay);
    --motion-text-color-loop-count: var(--motion-loop-count);
    --motion-scale-in-animation: none;
    --motion-translate-in-animation: none;
    --motion-rotate-in-animation: none;
    --motion-filter-in-animation: none;
    --motion-opacity-in-animation: none;
    --motion-background-color-in-animation: none;
    --motion-text-color-in-animation: none;
    --motion-scale-out-animation: none;
    --motion-translate-out-animation: none;
    --motion-rotate-out-animation: none;
    --motion-filter-out-animation: none;
    --motion-opacity-out-animation: none;
    --motion-background-color-out-animation: none;
    --motion-text-color-out-animation: none;
    --motion-scale-loop-animation: none;
    --motion-translate-loop-animation: none;
    --motion-rotate-loop-animation: none;
    --motion-filter-loop-animation: none;
    --motion-opacity-loop-animation: none;
    --motion-background-color-loop-animation: none;
    --motion-text-color-loop-animation: none;
    --motion-all-enter-animations: var(--motion-scale-in-animation), var(--motion-translate-in-animation), var(--motion-rotate-in-animation), var(--motion-filter-in-animation), var(--motion-opacity-in-animation), var(--motion-background-color-in-animation), var(--motion-text-color-in-animation);
    --motion-all-exit-animations: var(--motion-scale-out-animation), var(--motion-translate-out-animation), var(--motion-rotate-out-animation), var(--motion-filter-out-animation), var(--motion-opacity-out-animation), var(--motion-background-color-out-animation), var(--motion-text-color-out-animation);
    --motion-all-loop-animations: var(--motion-scale-loop-animation), var(--motion-translate-loop-animation), var(--motion-rotate-loop-animation), var(--motion-filter-loop-animation), var(--motion-opacity-loop-animation), var(--motion-background-color-loop-animation), var(--motion-text-color-loop-animation);
    --motion-all-loop-and-enter-animations: var(--motion-all-loop-animations), var(--motion-all-enter-animations);
  }
}
@layer base {
  @media screen and (prefers-reduced-motion: no-preference) {
    @keyframes motion-scale-in {
      0% {
        scale: var(--motion-origin-scale-x) var(--motion-origin-scale-y);
      }
      100% {
        scale: 1 1;
      }
    }
    @keyframes motion-scale-out {
      0% {
        scale: 1 1;
      }
      100% {
        scale: var(--motion-end-scale-x) var(--motion-end-scale-y);
      }
    }
    @keyframes motion-scale-loop-mirror {
      0%, 100% {
        scale: 1 1;
      }
      50% {
        scale: var(--motion-loop-scale-x) var(--motion-loop-scale-y);
      }
    }
    @keyframes motion-scale-loop-reset {
      0% {
        scale: 1 1;
      }
      100% {
        scale: var(--motion-loop-scale-x) var(--motion-loop-scale-y);
      }
    }
    @keyframes motion-translate-in {
      0% {
        translate: var(--motion-origin-translate-x) var(--motion-origin-translate-y);
      }
      100% {
        translate: 0 0;
      }
    }
    @keyframes motion-translate-out {
      0% {
        translate: 0 0;
      }
      100% {
        translate: var(--motion-end-translate-x) var(--motion-end-translate-y);
      }
    }
    @keyframes motion-translate-loop-mirror {
      0%, 100% {
        translate: 0 0;
      }
      50% {
        translate: var(--motion-loop-translate-x) var(--motion-loop-translate-y);
      }
    }
    @keyframes motion-translate-loop-reset {
      0% {
        translate: 0 0;
      }
      100% {
        translate: var(--motion-loop-translate-x) var(--motion-loop-translate-y);
      }
    }
    @keyframes motion-rotate-in {
      0% {
        rotate: var(--motion-origin-rotate);
      }
      100% {
        rotate: 0;
      }
    }
    @keyframes motion-rotate-out {
      0% {
        rotate: 0;
      }
      100% {
        rotate: var(--motion-end-rotate);
      }
    }
    @keyframes motion-rotate-loop-mirror {
      0%, 100% {
        rotate: 0deg;
      }
      50% {
        rotate: var(--motion-loop-rotate);
      }
    }
    @keyframes motion-rotate-loop-reset {
      100% {
        rotate: var(--motion-loop-rotate);
      }
    }
  }
  @keyframes motion-filter-in {
    0% {
      filter: blur(var(--motion-origin-blur)) grayscale(var(--motion-origin-grayscale));
    }
    100% {
      filter: blur(0) grayscale(0);
    }
  }
  @keyframes motion-filter-out {
    0% {
      filter: blur(0) grayscale(0);
    }
    100% {
      filter: blur(var(--motion-end-blur)) grayscale(var(--motion-end-grayscale));
    }
  }
  @keyframes motion-filter-loop-mirror {
    0%, 100% {
      filter: blur(0) grayscale(0);
    }
    50% {
      filter: blur(var(--motion-loop-blur)) grayscale(var(--motion-loop-grayscale));
    }
  }
  @keyframes motion-filter-loop-reset {
    0% {
      filter: blur(0) grayscale(0);
    }
    100% {
      filter: blur(var(--motion-loop-blur)) grayscale(var(--motion-loop-grayscale));
    }
  }
  @keyframes motion-opacity-in {
    0% {
      opacity: var(--motion-origin-opacity);
    }
  }
  @keyframes motion-opacity-out {
    100% {
      opacity: var(--motion-end-opacity);
    }
  }
  @keyframes motion-opacity-loop-mirror {
    50% {
      opacity: var(--motion-loop-opacity);
    }
  }
  @keyframes motion-opacity-loop-reset {
    100% {
      opacity: var(--motion-loop-opacity);
    }
  }
  @keyframes motion-background-color-in {
    0% {
      background-color: var(--motion-origin-background-color);
    }
  }
  @keyframes motion-background-color-out {
    100% {
      background-color: var(--motion-end-background-color);
    }
  }
  @keyframes motion-background-color-loop-mirror {
    50% {
      background-color: var(--motion-loop-background-color);
    }
  }
  @keyframes motion-background-color-loop-reset {
    100% {
      background-color: var(--motion-loop-background-color);
    }
  }
  @keyframes motion-text-color-in {
    0% {
      color: var(--motion-origin-text-color);
    }
  }
  @keyframes motion-text-color-out {
    100% {
      color: var(--motion-end-text-color);
    }
  }
  @keyframes motion-text-color-loop-mirror {
    50% {
      color: var(--motion-loop-text-color);
    }
  }
  @keyframes motion-text-color-loop-reset {
    100% {
      color: var(--motion-loop-text-color);
    }
  }
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes fd-collapsible-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}
@keyframes fd-collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
    opacity: 0;
  }
}
@keyframes fd-accordion-down {
  from {
    height: 0;
    opacity: 0.5;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}
@keyframes fd-accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
    opacity: 0.5;
  }
}
@keyframes fd-dialog-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
  }
}
@keyframes fd-dialog-out {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.95);
    opacity: 0;
  }
}
@keyframes fd-popover-in {
  from {
    opacity: 0;
    transform: scale(0.98) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
@keyframes fd-popover-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-4px);
  }
}
@keyframes fd-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fd-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fd-enterFromRight {
  from {
    opacity: 0;
    transform: translateX(200px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fd-enterFromLeft {
  from {
    opacity: 0;
    transform: translateX(-200px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fd-exitToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(200px);
  }
}
@keyframes fd-exitToLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-200px);
  }
}
@keyframes fd-nav-menu-in {
  from {
    opacity: 0;
    height: 0px;
  }
  to {
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }
}
@keyframes fd-nav-menu-out {
  from {
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }
  to {
    opacity: 0;
    height: 0px;
  }
}
@keyframes aurora {
  from {
    background-position: 50% 50%,
        50% 50%;
  }
  to {
    background-position: 350% 50%,
        350% 50%;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-border-style: solid;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-content: "";
      --radix-collapsible-content-height: 0px;
    }
  }
}

