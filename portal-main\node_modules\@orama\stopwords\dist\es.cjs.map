{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'a',\n    'actualmente',\n    'acuerdo',\n    'adelante',\n    'ademas',\n    'además',\n    'adrede',\n    'afirmó',\n    'agregó',\n    'ahi',\n    'ahora',\n    'ahí',\n    'al',\n    'algo',\n    'alguna',\n    'algunas',\n    'alguno',\n    'algunos',\n    'algún',\n    'alli',\n    'allí',\n    'alrededor',\n    'ambos',\n    'ampleamos',\n    'antano',\n    'antaño',\n    'ante',\n    'anterior',\n    'antes',\n    'apenas',\n    'aproximadamente',\n    'aquel',\n    'aquella',\n    'aquellas',\n    'aquello',\n    'aquellos',\n    'aqui',\n    'aquél',\n    'aquélla',\n    'aquéllas',\n    'aquéllos',\n    'aquí',\n    'arriba',\n    'arriba<PERSON>jo',\n    'aseguró',\n    'asi',\n    'así',\n    'atras',\n    'aun',\n    'aunque',\n    'ayer',\n    'añadi<PERSON>',\n    'aún',\n    'b',\n    'bajo',\n    'bastante',\n    'bien',\n    'breve',\n    'buen',\n    'buena',\n    'buenas',\n    'bueno',\n    'buenos',\n    'c',\n    'cada',\n    'casi',\n    'cerca',\n    'cierta',\n    'ciertas',\n    'cierto',\n    'ciertos',\n    'cinco',\n    'claro',\n    'comentó',\n    'como',\n    'con',\n    'conmigo',\n    'conocer',\n    'conseguimos',\n    'conseguir',\n    'considera',\n    'consideró',\n    'consigo',\n    'consigue',\n    'consiguen',\n    'consigues',\n    'contigo',\n    'contra',\n    'cosas',\n    'creo',\n    'cual',\n    'cuales',\n    'cualquier',\n    'cuando',\n    'cuanta',\n    'cuantas',\n    'cuanto',\n    'cuantos',\n    'cuatro',\n    'cuenta',\n    'cuál',\n    'cuáles',\n    'cuándo',\n    'cuánta',\n    'cuántas',\n    'cuánto',\n    'cuántos',\n    'cómo',\n    'd',\n    'da',\n    'dado',\n    'dan',\n    'dar',\n    'de',\n    'debajo',\n    'debe',\n    'deben',\n    'debido',\n    'decir',\n    'dejó',\n    'del',\n    'delante',\n    'demasiado',\n    'demás',\n    'dentro',\n    'deprisa',\n    'desde',\n    'despacio',\n    'despues',\n    'después',\n    'detras',\n    'detrás',\n    'dia',\n    'dias',\n    'dice',\n    'dicen',\n    'dicho',\n    'dieron',\n    'diferente',\n    'diferentes',\n    'dijeron',\n    'dijo',\n    'dio',\n    'donde',\n    'dos',\n    'durante',\n    'día',\n    'días',\n    'dónde',\n    'e',\n    'ejemplo',\n    'el',\n    'ella',\n    'ellas',\n    'ello',\n    'ellos',\n    'embargo',\n    'empleais',\n    'emplean',\n    'emplear',\n    'empleas',\n    'empleo',\n    'en',\n    'encima',\n    'encuentra',\n    'enfrente',\n    'enseguida',\n    'entonces',\n    'entre',\n    'era',\n    'eramos',\n    'eran',\n    'eras',\n    'eres',\n    'es',\n    'esa',\n    'esas',\n    'ese',\n    'eso',\n    'esos',\n    'esta',\n    'estaba',\n    'estaban',\n    'estado',\n    'estados',\n    'estais',\n    'estamos',\n    'estan',\n    'estar',\n    'estará',\n    'estas',\n    'este',\n    'esto',\n    'estos',\n    'estoy',\n    'estuvo',\n    'está',\n    'están',\n    'ex',\n    'excepto',\n    'existe',\n    'existen',\n    'explicó',\n    'expresó',\n    'f',\n    'fin',\n    'final',\n    'fue',\n    'fuera',\n    'fueron',\n    'fui',\n    'fuimos',\n    'g',\n    'general',\n    'gran',\n    'grandes',\n    'gueno',\n    'h',\n    'ha',\n    'haber',\n    'habia',\n    'habla',\n    'hablan',\n    'habrá',\n    'había',\n    'habían',\n    'hace',\n    'haceis',\n    'hacemos',\n    'hacen',\n    'hacer',\n    'hacerlo',\n    'haces',\n    'hacia',\n    'haciendo',\n    'hago',\n    'han',\n    'hasta',\n    'hay',\n    'haya',\n    'he',\n    'hecho',\n    'hemos',\n    'hicieron',\n    'hizo',\n    'horas',\n    'hoy',\n    'hubo',\n    'i',\n    'igual',\n    'incluso',\n    'indicó',\n    'informo',\n    'informó',\n    'intenta',\n    'intentais',\n    'intentamos',\n    'intentan',\n    'intentar',\n    'intentas',\n    'intento',\n    'ir',\n    'j',\n    'junto',\n    'k',\n    'l',\n    'la',\n    'lado',\n    'largo',\n    'las',\n    'le',\n    'lejos',\n    'les',\n    'llegó',\n    'lleva',\n    'llevar',\n    'lo',\n    'los',\n    'luego',\n    'lugar',\n    'm',\n    'mal',\n    'manera',\n    'manifestó',\n    'mas',\n    'mayor',\n    'me',\n    'mediante',\n    'medio',\n    'mejor',\n    'mencionó',\n    'menos',\n    'menudo',\n    'mi',\n    'mia',\n    'mias',\n    'mientras',\n    'mio',\n    'mios',\n    'mis',\n    'misma',\n    'mismas',\n    'mismo',\n    'mismos',\n    'modo',\n    'momento',\n    'mucha',\n    'muchas',\n    'mucho',\n    'muchos',\n    'muy',\n    'más',\n    'mí',\n    'mía',\n    'mías',\n    'mío',\n    'míos',\n    'n',\n    'nada',\n    'nadie',\n    'ni',\n    'ninguna',\n    'ningunas',\n    'ninguno',\n    'ningunos',\n    'ningún',\n    'no',\n    'nos',\n    'nosotras',\n    'nosotros',\n    'nuestra',\n    'nuestras',\n    'nuestro',\n    'nuestros',\n    'nueva',\n    'nuevas',\n    'nuevo',\n    'nuevos',\n    'nunca',\n    'o',\n    'ocho',\n    'os',\n    'otra',\n    'otras',\n    'otro',\n    'otros',\n    'p',\n    'pais',\n    'para',\n    'parece',\n    'parte',\n    'partir',\n    'pasada',\n    'pasado',\n    'paìs',\n    'peor',\n    'pero',\n    'pesar',\n    'poca',\n    'pocas',\n    'poco',\n    'pocos',\n    'podeis',\n    'podemos',\n    'poder',\n    'podria',\n    'podriais',\n    'podriamos',\n    'podrian',\n    'podrias',\n    'podrá',\n    'podrán',\n    'podría',\n    'podrían',\n    'poner',\n    'por',\n    'porque',\n    'posible',\n    'primer',\n    'primera',\n    'primero',\n    'primeros',\n    'principalmente',\n    'pronto',\n    'propia',\n    'propias',\n    'propio',\n    'propios',\n    'proximo',\n    'próximo',\n    'próximos',\n    'pudo',\n    'pueda',\n    'puede',\n    'pueden',\n    'puedo',\n    'pues',\n    'q',\n    'qeu',\n    'que',\n    'quedó',\n    'queremos',\n    'quien',\n    'quienes',\n    'quiere',\n    'quiza',\n    'quizas',\n    'quizá',\n    'quizás',\n    'quién',\n    'quiénes',\n    'qué',\n    'r',\n    'raras',\n    'realizado',\n    'realizar',\n    'realizó',\n    'repente',\n    'respecto',\n    's',\n    'sabe',\n    'sabeis',\n    'sabemos',\n    'saben',\n    'saber',\n    'sabes',\n    'salvo',\n    'se',\n    'sea',\n    'sean',\n    'segun',\n    'segunda',\n    'segundo',\n    'según',\n    'seis',\n    'ser',\n    'sera',\n    'será',\n    'serán',\n    'sería',\n    'señaló',\n    'si',\n    'sido',\n    'siempre',\n    'siendo',\n    'siete',\n    'sigue',\n    'siguiente',\n    'sin',\n    'sino',\n    'sobre',\n    'sois',\n    'sola',\n    'solamente',\n    'solas',\n    'solo',\n    'solos',\n    'somos',\n    'son',\n    'soy',\n    'soyos',\n    'su',\n    'supuesto',\n    'sus',\n    'suya',\n    'suyas',\n    'suyo',\n    'sé',\n    'sí',\n    'sólo',\n    't',\n    'tal',\n    'tambien',\n    'también',\n    'tampoco',\n    'tan',\n    'tanto',\n    'tarde',\n    'te',\n    'temprano',\n    'tendrá',\n    'tendrán',\n    'teneis',\n    'tenemos',\n    'tener',\n    'tenga',\n    'tengo',\n    'tenido',\n    'tenía',\n    'tercera',\n    'ti',\n    'tiempo',\n    'tiene',\n    'tienen',\n    'toda',\n    'todas',\n    'todavia',\n    'todavía',\n    'todo',\n    'todos',\n    'total',\n    'trabaja',\n    'trabajais',\n    'trabajamos',\n    'trabajan',\n    'trabajar',\n    'trabajas',\n    'trabajo',\n    'tras',\n    'trata',\n    'través',\n    'tres',\n    'tu',\n    'tus',\n    'tuvo',\n    'tuya',\n    'tuyas',\n    'tuyo',\n    'tuyos',\n    'tú',\n    'u',\n    'ultimo',\n    'un',\n    'una',\n    'unas',\n    'uno',\n    'unos',\n    'usa',\n    'usais',\n    'usamos',\n    'usan',\n    'usar',\n    'usas',\n    'uso',\n    'usted',\n    'ustedes',\n    'v',\n    'va',\n    'vais',\n    'valor',\n    'vamos',\n    'van',\n    'varias',\n    'varios',\n    'vaya',\n    'veces',\n    'ver',\n    'verdad',\n    'verdadera',\n    'verdadero',\n    'vez',\n    'vosotras',\n    'vosotros',\n    'voy',\n    'vuestra',\n    'vuestras',\n    'vuestro',\n    'vuestros',\n    'w',\n    'x',\n    'y',\n    'ya',\n    'yo',\n    'z',\n    'él',\n    'ésa',\n    'ésas',\n    'ése',\n    'ésos',\n    'ésta',\n    'éstas',\n    'éste',\n    'éstos',\n    'última',\n    'últimas',\n    'último',\n    'últimos'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,IACA,cACA,UACA,WACA,SACA,YACA,SACA,YACA,YACA,MACA,QACA,SACA,KACA,OACA,SACA,UACA,SACA,UACA,WACA,OACA,UACA,YACA,QACA,YACA,SACA,YACA,OACA,WACA,QACA,SACA,kBACA,QACA,UACA,WACA,UACA,WACA,OACA,WACA,aACA,cACA,cACA,UACA,SACA,cACA,aACA,MACA,SACA,QACA,MACA,SACA,OACA,eACA,SACA,IACA,OACA,WACA,OACA,QACA,OACA,QACA,SACA,QACA,SACA,IACA,OACA,OACA,QACA,SACA,UACA,SACA,UACA,QACA,QACA,aACA,OACA,MACA,UACA,UACA,cACA,YACA,YACA,eACA,UACA,WACA,YACA,YACA,UACA,SACA,QACA,OACA,OACA,SACA,YACA,SACA,SACA,UACA,SACA,UACA,SACA,SACA,UACA,YACA,YACA,YACA,aACA,YACA,aACA,UACA,IACA,KACA,OACA,MACA,MACA,KACA,SACA,OACA,QACA,SACA,QACA,UACA,MACA,UACA,YACA,WACA,SACA,UACA,QACA,WACA,UACA,aACA,SACA,YACA,MACA,OACA,OACA,QACA,QACA,SACA,YACA,aACA,UACA,OACA,MACA,QACA,MACA,UACA,SACA,UACA,WACA,IACA,UACA,KACA,OACA,QACA,OACA,QACA,UACA,WACA,UACA,UACA,UACA,SACA,KACA,SACA,YACA,WACA,YACA,WACA,QACA,MACA,SACA,OACA,OACA,OACA,KACA,MACA,OACA,MACA,MACA,OACA,OACA,SACA,UACA,SACA,UACA,SACA,UACA,QACA,QACA,YACA,QACA,OACA,OACA,QACA,QACA,SACA,UACA,WACA,KACA,UACA,SACA,UACA,aACA,aACA,IACA,MACA,QACA,MACA,QACA,SACA,MACA,SACA,IACA,UACA,OACA,UACA,QACA,IACA,KACA,QACA,QACA,QACA,SACA,WACA,WACA,YACA,OACA,SACA,UACA,QACA,QACA,UACA,QACA,QACA,WACA,OACA,MACA,QACA,MACA,OACA,KACA,QACA,QACA,WACA,OACA,QACA,MACA,OACA,IACA,QACA,UACA,YACA,UACA,aACA,UACA,YACA,aACA,WACA,WACA,WACA,UACA,KACA,IACA,QACA,IACA,IACA,KACA,OACA,QACA,MACA,KACA,QACA,MACA,WACA,QACA,SACA,KACA,MACA,QACA,QACA,IACA,MACA,SACA,eACA,MACA,QACA,KACA,WACA,QACA,QACA,cACA,QACA,SACA,KACA,MACA,OACA,WACA,MACA,OACA,MACA,QACA,SACA,QACA,SACA,OACA,UACA,QACA,SACA,QACA,SACA,MACA,SACA,QACA,SACA,UACA,SACA,UACA,IACA,OACA,QACA,KACA,UACA,WACA,UACA,WACA,YACA,KACA,MACA,WACA,WACA,UACA,WACA,UACA,WACA,QACA,SACA,QACA,SACA,QACA,IACA,OACA,KACA,OACA,QACA,OACA,QACA,IACA,OACA,OACA,SACA,QACA,SACA,SACA,SACA,UACA,OACA,OACA,QACA,OACA,QACA,OACA,QACA,SACA,UACA,QACA,SACA,WACA,YACA,UACA,UACA,WACA,YACA,YACA,aACA,QACA,MACA,SACA,UACA,SACA,UACA,UACA,WACA,iBACA,SACA,SACA,UACA,SACA,UACA,UACA,aACA,cACA,OACA,QACA,QACA,SACA,QACA,OACA,IACA,MACA,MACA,WACA,WACA,QACA,UACA,SACA,QACA,SACA,WACA,YACA,WACA,aACA,SACA,IACA,QACA,YACA,WACA,aACA,UACA,WACA,IACA,OACA,SACA,UACA,QACA,QACA,QACA,QACA,KACA,MACA,OACA,QACA,UACA,UACA,WACA,OACA,MACA,OACA,UACA,WACA,WACA,eACA,KACA,OACA,UACA,SACA,QACA,QACA,YACA,MACA,OACA,QACA,OACA,OACA,YACA,QACA,OACA,QACA,QACA,MACA,MACA,QACA,KACA,WACA,MACA,OACA,QACA,OACA,QACA,QACA,UACA,IACA,MACA,UACA,aACA,UACA,MACA,QACA,QACA,KACA,WACA,YACA,aACA,SACA,UACA,QACA,QACA,QACA,SACA,WACA,UACA,KACA,SACA,QACA,SACA,OACA,QACA,UACA,aACA,OACA,QACA,QACA,UACA,YACA,aACA,WACA,WACA,WACA,UACA,OACA,QACA,YACA,OACA,KACA,MACA,OACA,OACA,QACA,OACA,QACA,QACA,IACA,SACA,KACA,MACA,OACA,MACA,OACA,MACA,QACA,SACA,OACA,OACA,OACA,MACA,QACA,UACA,IACA,KACA,OACA,QACA,QACA,MACA,SACA,SACA,OACA,QACA,MACA,SACA,YACA,YACA,MACA,WACA,WACA,MACA,UACA,WACA,UACA,WACA,IACA,IACA,IACA,KACA,KACA,IACA,QACA,SACA,UACA,SACA,UACA,UACA,WACA,UACA,WACA,YACA,aACA,YACA,aACH"}