{"version": 3, "file": "remove.js", "sourceRoot": "", "sources": ["../../../src/methods/remove.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAA;AACvE,OAAO,EAEL,2BAA2B,EAC3B,qBAAqB,GACtB,MAAM,6CAA6C,CAAA;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAA;AACrE,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAA;AAE7C,MAAM,UAAU,MAAM,CACpB,KAAQ,EACR,EAAc,EACd,QAAiB,EACjB,SAAmB;IAEnB,MAAM,WAAW,GACf,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;QACzC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;QACnC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IAE1C,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;IACpD,CAAC;IAED,OAAO,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;AACnD,CAAC;AAED,KAAK,UAAU,WAAW,CACxB,KAAQ,EACR,EAAc,EACd,QAAiB,EACjB,SAAmB;IAEnB,IAAI,MAAM,GAAG,IAAI,CAAA;IACjB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAA;IAElC,MAAM,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IAC9C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;IAC3E,MAAM,KAAK,GAAG,2BAA2B,CACvC,KAAK,CAAC,uBAAuB,EAC7B,UAAU,CACX,CAAA;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAElD,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,aAAa,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IACvD,CAAC;IAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;IACtE,MAAM,4BAA4B,GAAG,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAA;IACxF,MAAM,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;IAEpE,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;YACjC,SAAQ;QACV,CAAC;QAED,MAAM,UAAU,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAA;QAErD,MAAM,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,CAC9B,KAAK,CAAC,IAAI,CAAC,KAAK,EAChB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,CAAC,SAAS,EACf,SAAS,CACV,CAAA;QAED,IACE,CAAC,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,MAAM,CACxB,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,IAAI,CAAC,KAAK,EAChB,IAAI,EACJ,EAAE,EACF,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,CAAC,SAAS,EACf,SAAS,CACV,CAAC,EACF,CAAC;YACD,MAAM,GAAG,KAAK,CAAA;QAChB,CAAC;QAED,MAAM,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,CAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,EAChB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,CAAC,SAAS,EACf,SAAS,CACV,CAAA;IACH,CAAC;IAED,MAAM,kBAAkB,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACvF,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAA;IACjF,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;QACtC,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,CAAC;YAChD,SAAQ;QACV,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,aAAa,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IACtD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA;IAE5D,YAAY,CAAC,KAAK,CAAC,CAAA;IACnB,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,UAAU,CAAqB,KAAQ,EAAE,EAAc,EAAE,QAAiB,EAAE,SAAmB;IACtG,IAAI,MAAM,GAAG,IAAI,CAAA;IACjB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAA;IAElC,MAAM,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IAC9C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;IAC3E,MAAM,KAAK,GAAG,2BAA2B,CACvC,KAAK,CAAC,uBAAuB,EAC7B,UAAU,CACX,CAAA;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAElD,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,aAAa,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;IACtE,MAAM,4BAA4B,GAAG,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAA;IACxF,MAAM,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;IAEpE,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;YACjC,SAAQ;QACV,CAAC;QAED,MAAM,UAAU,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAA;QAErD,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAElH,IACE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CACjB,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,IAAI,CAAC,KAAK,EAChB,IAAI,EACJ,EAAE,EACF,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,CAAC,SAAS,EACf,SAAS,CACV,EACD,CAAC;YACD,MAAM,GAAG,KAAK,CAAA;QAChB,CAAC;QAED,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;IACnH,CAAC;IAED,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACjF,MAAM,cAAc,GAAG,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAA;IAC3E,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;QACtC,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,CAAC;YAChD,SAAQ;QACV,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,aAAa,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IAChD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA;IAE5D,YAAY,CAAC,KAAK,CAAC,CAAA;IACnB,OAAO,MAAM,CAAA;AACf,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,KAAQ,EACR,GAAiB,EACjB,SAAkB,EAClB,QAAiB,EACjB,SAAmB;IAEnB,MAAM,WAAW,GACf,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;QACzC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;QACnC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;QACxC,eAAe,CAAC,KAAK,CAAC,oBAAoB,CAAC;QAC3C,eAAe,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IAE5C,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,mBAAmB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;IACxE,CAAC;IAED,OAAO,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;AACvE,CAAC;AAED,KAAK,UAAU,mBAAmB,CAChC,KAAQ,EACR,GAAiB,EACjB,SAAkB,EAClB,QAAiB,EACjB,SAAmB;IAEnB,IAAI,MAAM,GAAG,CAAC,CAAA;IAEd,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,SAAS,GAAG,IAAI,CAAA;IAClB,CAAC;IAED,MAAM,cAAc,GAAG,SAAS;QAC9B,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CACb,2BAA2B,CACzB,KAAK,CAAC,uBAAuB,EAC7B,qBAAqB,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CACzD,CACF,CAAA;IAEL,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,eAAe,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;IAC1E,CAAC;IAED,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1C,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,KAAK,UAAU,eAAe;YAC5B,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAU,EAAE,EAAE,CAAC,GAAG,SAAU,CAAC,CAAA;YAEzD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,OAAO,OAAO,EAAE,CAAA;YAClB,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACxB,IAAI,CAAC;oBACH,IAAI,MAAM,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;wBAClD,MAAM,EAAE,CAAA;oBACV,CAAC;gBACH,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,MAAM,CAAC,GAAG,CAAC,CAAA;gBACb,CAAC;YACH,CAAC;YAED,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;QAChC,CAAC;QAED,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;IAChC,CAAC,CAAC,CAAA;IAEF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,eAAe,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;IACzE,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,kBAAkB,CACzB,KAAQ,EACR,GAAiB,EACjB,SAAkB,EAClB,QAAiB,EACjB,SAAmB;IAEnB,IAAI,MAAM,GAAG,CAAC,CAAA;IAEd,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,SAAS,GAAG,IAAI,CAAA;IAClB,CAAC;IAED,MAAM,cAAc,GAAG,SAAS;QAC9B,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CACb,2BAA2B,CACzB,KAAK,CAAC,uBAAuB,EAC7B,qBAAqB,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CACzD,CACF,CAAA;IAEL,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,eAAe,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;IACpE,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,SAAS,mBAAmB;QAC1B,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAU,EAAE,EAAE,CAAC,GAAG,SAAU,CAAC,CAAA;QAEzD,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAM;QAEzB,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACxB,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC5C,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC;QAED,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAA;IACpC,CAAC;IAED,mBAAmB,EAAE,CAAA;IAErB,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,eAAe,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;IACnE,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}