---
title: 🌟 Welcome to TEN
---

Welcome to TEN! TEN stands for Transformative Extensions Network, is a voice agent framework to create conversational AI.


TEN framework offers the following advantages:

1. **Native Support for High-Performance, Real-Time Multimodal Interactions**

   If your AI applications involve complex audio-visual scenarios, TEN is your go-to solution. It offers high performance and low latency, with extensive optimization of interactions between various extensions to ensure efficient development of AI applications.

2. **Supports Multiple Languages and Platforms**

   Developers can create modular and reusable extensions using various programming languages, such as C++, Go, and Python (with future support for JavaScript/TypeScript). Moreover, the TEN framework runs seamlessly across platforms, including Windows, Mac, Linux, and mobile devices.

3. **Edge-Cloud Integration**

   Through the TEN framework, extensions deployed across edge and cloud environments can be easily combined to create diverse applications and scenarios. For privacy-sensitive edge deployments, small models leverage local compute power for reduced costs and lower latency, while cloud-based large models can be integrated for an optimal balance of cost and performance.

4. **Flexibility Beyond Model Limitations**

   The TEN framework allows for the creation of complex AI applications that transcend the limitations of large models alone. Agents can be easily constructed to meet a wide range of needs using a simple drag-and-drop, responsive programming approach. TEN also facilitates the integration of AI with audio-visual tools, databases, monitoring systems, RAG, and more.

5. **Real-Time Agent State Management**

   TEN has the ability to manage real-time agent states, enabling dynamic responsiveness and adjustment of agent behavior in real time.
