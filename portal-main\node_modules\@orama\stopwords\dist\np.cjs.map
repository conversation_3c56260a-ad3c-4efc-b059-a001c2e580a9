{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'अक्सर',\n    'अगाडि',\n    'अझै',\n    'अनुसार',\n    'अन्तर्गत',\n    'अन्य',\n    'अन्यत्र',\n    'अन्यथा',\n    'अब',\n    'अरू',\n    'अरूलाई',\n    'अर्को',\n    'अर्थात',\n    'अर्थात्',\n    'अलग',\n    'आए',\n    'आजको',\n    'आठ',\n    'आत्म',\n    'आदि',\n    'आफू',\n    'आफूलाई',\n    'आफैलाई',\n    'आफ्नै',\n    'आफ्नो',\n    'आयो',\n    'उदाहरण',\n    'उन',\n    'उनको',\n    'उनले',\n    'उप',\n    'उहाँलाई',\n    'एउटै',\n    'एक',\n    'एकदम',\n    'औं',\n    'कतै',\n    'कम',\n    'कसरी',\n    'कसै',\n    'कसैले',\n    'कहाँबाट',\n    'कहिलेकाहीं',\n    'कहिल्यै',\n    'कहीं',\n    'का',\n    'कि',\n    'किन',\n    'किनभने',\n    'कुनै',\n    'कुरा',\n    'कृपया',\n    'के',\n    'केहि',\n    'केही',\n    'को',\n    'कोही',\n    'क्रमशः',\n    'गए',\n    'गरि',\n    'गरी',\n    'गरेका',\n    'गरेको',\n    'गरेर',\n    'गरौं',\n    'गर्छ',\n    'गर्छु',\n    'गर्दै',\n    'गर्न',\n    'गर्नु',\n    'गर्नुपर्छ',\n    'गर्ने',\n    'गर्यौं',\n    'गैर',\n    'चाँडै',\n    'चार',\n    'चाले',\n    'चाहनुहुन्छ',\n    'चाहन्छु',\n    'चाहिए',\n    'छ',\n    'छन्',\n    'छु',\n    'छैन',\n    'छौँ',\n    'छौं',\n    'जताततै',\n    'जब',\n    'जबकि',\n    'जसको',\n    'जसबाट',\n    'जसमा',\n    'जसलाई',\n    'जसले',\n    'जस्तै',\n    'जस्तो',\n    'जस्तोसुकै',\n    'जहाँ',\n    'जान',\n    'जाहिर',\n    'जुन',\n    'जे',\n    'जो',\n    'ठीक',\n    'त',\n    'तत्काल',\n    'तथा',\n    'तदनुसार',\n    'तपाइँको',\n    'तपाईं',\n    'तर',\n    'तल',\n    'तापनि',\n    'तिनी',\n    'तिनीहरू',\n    'तिनीहरूको',\n    'तिनीहरूलाई',\n    'तिनीहरूले',\n    'तिमी',\n    'तिर',\n    'ती',\n    'तीन',\n    'तुरुन्तै',\n    'तेस्रो',\n    'त्यसकारण',\n    'त्यसपछि',\n    'त्यसमा',\n    'त्यसैले',\n    'त्यहाँ',\n    'त्यो',\n    'थिए',\n    'थिएन',\n    'थिएनन्',\n    'थियो',\n    'दिए',\n    'दिनुभएको',\n    'दिनुहुन्छ',\n    'दुई',\n    'देख',\n    'देखि',\n    'देखिन्छ',\n    'देखियो',\n    'देखे',\n    'देखेको',\n    'देखेर',\n    'देख्न',\n    'दोश्रो',\n    'दोस्रो',\n    'धेरै',\n    'न',\n    'नजिकै',\n    'नत्र',\n    'नयाँ',\n    'नि',\n    'निम्ति',\n    'निम्न',\n    'निम्नानुसार',\n    'निर्दिष्ट',\n    'नै',\n    'नौ',\n    'पक्का',\n    'पक्कै',\n    'पछि',\n    'पछिल्लो',\n    'पटक',\n    'पनि',\n    'पर्छ',\n    'पर्थ्यो',\n    'पर्याप्त',\n    'पहिले',\n    'पहिलो',\n    'पहिल्यै',\n    'पाँच',\n    'पाँचौं',\n    'पूर्व',\n    'प्रति',\n    'प्रत्येक',\n    'प्लस',\n    'फेरि',\n    'बने',\n    'बन्द',\n    'बन्न',\n    'बरु',\n    'बाटो',\n    'बारे',\n    'बाहिर',\n    'बाहेक',\n    'बीच',\n    'बीचमा',\n    'भए',\n    'भएको',\n    'भन',\n    'भने',\n    'भने्',\n    'भन्छन्',\n    'भन्छु',\n    'भन्दा',\n    'भन्नुभयो',\n    'भन्ने',\n    'भर',\n    'भित्र',\n    'भित्री',\n    'म',\n    'मलाई',\n    'मा',\n    'मात्र',\n    'माथि',\n    'मुख्य',\n    'मेरो',\n    'यति',\n    'यथोचित',\n    'यदि',\n    'यद्यपि',\n    'यस',\n    'यसको',\n    'यसपछि',\n    'यसबाहेक',\n    'यसरी',\n    'यसो',\n    'यस्तो',\n    'यहाँ',\n    'यहाँसम्म',\n    'या',\n    'यी',\n    'यो',\n    'र',\n    'रही',\n    'रहेका',\n    'रहेको',\n    'राखे',\n    'राख्छ',\n    'राम्रो',\n    'रूप',\n    'लगभग',\n    'लाई',\n    'लागि',\n    'ले',\n    'वरिपरि',\n    'वास्तवमा',\n    'वाहेक',\n    'विरुद्ध',\n    'विशेष',\n    'शायद',\n    'सँग',\n    'सँगै',\n    'सक्छ',\n    'सट्टा',\n    'सधैं',\n    'सबै',\n    'सबैलाई',\n    'समय',\n    'सम्भव',\n    'सम्म',\n    'सही',\n    'साँच्चै',\n    'सात',\n    'साथ',\n    'साथै',\n    'सायद',\n    'सारा',\n    'सो',\n    'सोध्न',\n    'सोही',\n    'स्पष्ट',\n    'हरे',\n    'हरेक',\n    'हामी',\n    'हामीलाई',\n    'हाम्रो',\n    'हुँ',\n    'हुन',\n    'हुने',\n    'हुनेछ',\n    'हुन्',\n    'हुन्छ',\n    'हो',\n    'होइन',\n    'होइनन्',\n    'होला',\n    'होस्'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,QACA,QACA,MACA,SACA,WACA,OACA,UACA,SACA,KACA,MACA,SACA,QACA,SACA,UACA,MACA,KACA,OACA,KACA,OACA,MACA,MACA,SACA,SACA,QACA,QACA,MACA,SACA,KACA,OACA,OACA,KACA,UACA,OACA,KACA,OACA,KACA,MACA,KACA,OACA,MACA,QACA,UACA,aACA,UACA,OACA,KACA,KACA,MACA,SACA,OACA,OACA,QACA,KACA,OACA,OACA,KACA,OACA,SACA,KACA,MACA,MACA,QACA,QACA,OACA,OACA,OACA,QACA,QACA,OACA,QACA,YACA,QACA,SACA,MACA,QACA,MACA,OACA,aACA,UACA,QACA,IACA,MACA,KACA,MACA,MACA,MACA,SACA,KACA,OACA,OACA,QACA,OACA,QACA,OACA,QACA,QACA,YACA,OACA,MACA,QACA,MACA,KACA,KACA,MACA,IACA,SACA,MACA,UACA,UACA,QACA,KACA,KACA,QACA,OACA,UACA,YACA,aACA,YACA,OACA,MACA,KACA,MACA,WACA,SACA,WACA,UACA,SACA,UACA,SACA,OACA,MACA,OACA,SACA,OACA,MACA,WACA,YACA,MACA,MACA,OACA,UACA,SACA,OACA,SACA,QACA,QACA,SACA,SACA,OACA,IACA,QACA,OACA,OACA,KACA,SACA,QACA,cACA,YACA,KACA,KACA,QACA,QACA,MACA,UACA,MACA,MACA,OACA,UACA,WACA,QACA,QACA,UACA,OACA,SACA,QACA,QACA,WACA,OACA,OACA,MACA,OACA,OACA,MACA,OACA,OACA,QACA,QACA,MACA,QACA,KACA,OACA,KACA,MACA,OACA,SACA,QACA,QACA,WACA,QACA,KACA,QACA,SACA,IACA,OACA,KACA,QACA,OACA,QACA,OACA,MACA,SACA,MACA,SACA,KACA,OACA,QACA,UACA,OACA,MACA,QACA,OACA,WACA,KACA,KACA,KACA,IACA,MACA,QACA,QACA,OACA,QACA,SACA,MACA,OACA,MACA,OACA,KACA,SACA,WACA,QACA,UACA,QACA,OACA,MACA,OACA,OACA,QACA,OACA,MACA,SACA,MACA,QACA,OACA,MACA,UACA,MACA,MACA,OACA,OACA,OACA,KACA,QACA,OACA,SACA,MACA,OACA,OACA,UACA,SACA,MACA,MACA,OACA,QACA,OACA,QACA,KACA,OACA,SACA,OACA,OACH"}