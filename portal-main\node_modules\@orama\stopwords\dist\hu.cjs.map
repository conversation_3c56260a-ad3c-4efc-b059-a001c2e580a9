{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'a',\n    'abba',\n    'abban',\n    'abból',\n    'addig',\n    'ahhoz',\n    'ahogy',\n    'ahol',\n    'aki',\n    'akik',\n    'akkor',\n    'akár',\n    'alapján',\n    'alatt',\n    'alatta',\n    'alattad',\n    'alattam',\n    'alattatok',\n    'alattuk',\n    'alattunk',\n    'alá',\n    'alád',\n    'alájuk',\n    'alám',\n    'alánk',\n    'alátok',\n    'alól',\n    'alóla',\n    'alólad',\n    'alólam',\n    'alólatok',\n    'alóluk',\n    'alólunk',\n    'amely',\n    'amelybol',\n    'amelyek',\n    'amelyekben',\n    'amelyeket',\n    'amelyet',\n    'amelyik',\n    'amely<PERSON><PERSON>',\n    'ami',\n    'amikor',\n    'amit',\n    'amolyan',\n    'amott',\n    'amíg',\n    'annak',\n    'annál',\n    'arra',\n    'arról',\n    'attól',\n    'az',\n    'aznap',\n    'azok',\n    'azokat',\n    'azokba',\n    'azokban',\n    'azokból',\n    'azokhoz',\n    'azokig',\n    'azokkal',\n    'azokká',\n    'azoknak',\n    'azoknál',\n    'azokon',\n    'azokra',\n    'azokról',\n    'azoktól',\n    'azokért',\n    'azon',\n    'azonban',\n    'azonnal',\n    'azt',\n    'aztán',\n    'azután',\n    'azzal',\n    'azzá',\n    'azért',\n    'bal',\n    'balra',\n    'ban',\n    'be',\n    'belé',\n    'beléd',\n    'beléjük',\n    'belém',\n    'belénk',\n    'belétek',\n    'belül',\n    'belőle',\n    'belőled',\n    'belőlem',\n    'belőletek',\n    'belőlük',\n    'belőlünk',\n    'ben',\n    'benne',\n    'benned',\n    'bennem',\n    'bennetek',\n    'bennük',\n    'bennünk',\n    'bár',\n    'bárcsak',\n    'bármilyen',\n    'búcsú',\n    'cikk',\n    'cikkek',\n    'cikkeket',\n    'csak',\n    'csakhogy',\n    'csupán',\n    'de',\n    'dehogy',\n    'e',\n    'ebbe',\n    'ebben',\n    'ebből',\n    'eddig',\n    'egy',\n    'egyebek',\n    'egyebet',\n    'egyedül',\n    'egyelőre',\n    'egyes',\n    'egyet',\n    'egyetlen',\n    'egyik',\n    'egymás',\n    'egyre',\n    'egyszerre',\n    'egyéb',\n    'együtt',\n    'egész',\n    'egészen',\n    'ehhez',\n    'ekkor',\n    'el',\n    'eleinte',\n    'ellen',\n    'ellenes',\n    'elleni',\n    'ellenére',\n    'elmondta',\n    'első',\n    'elsők',\n    'elsősorban',\n    'elsőt',\n    'elé',\n    'eléd',\n    'elég',\n    'eléjük',\n    'elém',\n    'elénk',\n    'elétek',\n    'elő',\n    'előbb',\n    'elől',\n    'előle',\n    'előled',\n    'előlem',\n    'előletek',\n    'előlük',\n    'előlünk',\n    'először',\n    'előtt',\n    'előtte',\n    'előtted',\n    'előttem',\n    'előttetek',\n    'előttük',\n    'előttünk',\n    'előző',\n    'emilyen',\n    'engem',\n    'ennek',\n    'ennyi',\n    'ennél',\n    'enyém',\n    'erre',\n    'erről',\n    'esetben',\n    'ettől',\n    'ez',\n    'ezek',\n    'ezekbe',\n    'ezekben',\n    'ezekből',\n    'ezeken',\n    'ezeket',\n    'ezekhez',\n    'ezekig',\n    'ezekkel',\n    'ezekké',\n    'ezeknek',\n    'ezeknél',\n    'ezekre',\n    'ezekről',\n    'ezektől',\n    'ezekért',\n    'ezen',\n    'ezentúl',\n    'ezer',\n    'ezret',\n    'ezt',\n    'ezután',\n    'ezzel',\n    'ezzé',\n    'ezért',\n    'fel',\n    'fele',\n    'felek',\n    'felet',\n    'felett',\n    'felé',\n    'fent',\n    'fenti',\n    'fél',\n    'fölé',\n    'gyakran',\n    'ha',\n    'halló',\n    'hamar',\n    'hanem',\n    'harmadik',\n    'harmadikat',\n    'harminc',\n    'hat',\n    'hatodik',\n    'hatodikat',\n    'hatot',\n    'hatvan',\n    'helyett',\n    'hetedik',\n    'hetediket',\n    'hetet',\n    'hetven',\n    'hirtelen',\n    'hiszen',\n    'hiába',\n    'hogy',\n    'hogyan',\n    'hol',\n    'holnap',\n    'holnapot',\n    'honnan',\n    'hova',\n    'hozzá',\n    'hozzád',\n    'hozzájuk',\n    'hozzám',\n    'hozzánk',\n    'hozzátok',\n    'hurrá',\n    'huszadik',\n    'hány',\n    'hányszor',\n    'hármat',\n    'három',\n    'hát',\n    'hátha',\n    'hátulsó',\n    'hét',\n    'húsz',\n    'ide',\n    'ide-оda',\n    'idén',\n    'igazán',\n    'igen',\n    'ill',\n    'illetve',\n    'ilyen',\n    'ilyenkor',\n    'immár',\n    'inkább',\n    'is',\n    'ismét',\n    'ison',\n    'itt',\n    'jelenleg',\n    'jobban',\n    'jobbra',\n    'jó',\n    'jól',\n    'jólesik',\n    'jóval',\n    'jövőre',\n    'kell',\n    'kellene',\n    'kellett',\n    'kelljen',\n    'keressünk',\n    'keresztül',\n    'ketten',\n    'kettő',\n    'kettőt',\n    'kevés',\n    'ki',\n    'kiben',\n    'kiből',\n    'kicsit',\n    'kicsoda',\n    'kihez',\n    'kik',\n    'kikbe',\n    'kikben',\n    'kikből',\n    'kiken',\n    'kiket',\n    'kikhez',\n    'kikkel',\n    'kikké',\n    'kiknek',\n    'kiknél',\n    'kikre',\n    'kikről',\n    'kiktől',\n    'kikért',\n    'kilenc',\n    'kilencedik',\n    'kilencediket',\n    'kilencet',\n    'kilencven',\n    'kin',\n    'kinek',\n    'kinél',\n    'kire',\n    'kiről',\n    'kit',\n    'kitől',\n    'kivel',\n    'kivé',\n    'kié',\n    'kiért',\n    'korábban',\n    'képest',\n    'kérem',\n    'kérlek',\n    'kész',\n    'késő',\n    'később',\n    'későn',\n    'két',\n    'kétszer',\n    'kívül',\n    'körül',\n    'köszönhetően',\n    'köszönöm',\n    'közben',\n    'közel',\n    'közepesen',\n    'közepén',\n    'közé',\n    'között',\n    'közül',\n    'külön',\n    'különben',\n    'különböző',\n    'különbözőbb',\n    'különbözőek',\n    'lassan',\n    'le',\n    'legalább',\n    'legyen',\n    'lehet',\n    'lehetetlen',\n    'lehetett',\n    'lehetőleg',\n    'lehetőség',\n    'lenne',\n    'lenni',\n    'lennék',\n    'lennének',\n    'lesz',\n    'leszek',\n    'lesznek',\n    'leszünk',\n    'lett',\n    'lettek',\n    'lettem',\n    'lettünk',\n    'lévő',\n    'ma',\n    'maga',\n    'magad',\n    'magam',\n    'magatokat',\n    'magukat',\n    'magunkat',\n    'magát',\n    'mai',\n    'majd',\n    'majdnem',\n    'manapság',\n    'meg',\n    'megcsinál',\n    'megcsinálnak',\n    'megint',\n    'megvan',\n    'mellett',\n    'mellette',\n    'melletted',\n    'mellettem',\n    'mellettetek',\n    'mellettük',\n    'mellettünk',\n    'mellé',\n    'melléd',\n    'melléjük',\n    'mellém',\n    'mellénk',\n    'mellétek',\n    'mellől',\n    'mellőle',\n    'mellőled',\n    'mellőlem',\n    'mellőletek',\n    'mellőlük',\n    'mellőlünk',\n    'mely',\n    'melyek',\n    'melyik',\n    'mennyi',\n    'mert',\n    'mi',\n    'miatt',\n    'miatta',\n    'miattad',\n    'miattam',\n    'miattatok',\n    'miattuk',\n    'miattunk',\n    'mibe',\n    'miben',\n    'miből',\n    'mihez',\n    'mik',\n    'mikbe',\n    'mikben',\n    'mikből',\n    'miken',\n    'miket',\n    'mikhez',\n    'mikkel',\n    'mikké',\n    'miknek',\n    'miknél',\n    'mikor',\n    'mikre',\n    'mikről',\n    'miktől',\n    'mikért',\n    'milyen',\n    'min',\n    'mind',\n    'mindegyik',\n    'mindegyiket',\n    'minden',\n    'mindenesetre',\n    'mindenki',\n    'mindent',\n    'mindenütt',\n    'mindig',\n    'mindketten',\n    'minek',\n    'minket',\n    'mint',\n    'mintha',\n    'minél',\n    'mire',\n    'miről',\n    'mit',\n    'mitől',\n    'mivel',\n    'mivé',\n    'miért',\n    'mondta',\n    'most',\n    'mostanáig',\n    'már',\n    'más',\n    'másik',\n    'másikat',\n    'másnap',\n    'második',\n    'másodszor',\n    'mások',\n    'másokat',\n    'mást',\n    'még',\n    'mégis',\n    'míg',\n    'mögé',\n    'mögéd',\n    'mögéjük',\n    'mögém',\n    'mögénk',\n    'mögétek',\n    'mögött',\n    'mögötte',\n    'mögötted',\n    'mögöttem',\n    'mögöttetek',\n    'mögöttük',\n    'mögöttünk',\n    'mögül',\n    'mögüle',\n    'mögüled',\n    'mögülem',\n    'mögületek',\n    'mögülük',\n    'mögülünk',\n    'múltkor',\n    'múlva',\n    'na',\n    'nagy',\n    'nagyobb',\n    'nagyon',\n    'naponta',\n    'napot',\n    'ne',\n    'negyedik',\n    'negyediket',\n    'negyven',\n    'neked',\n    'nekem',\n    'neki',\n    'nekik',\n    'nektek',\n    'nekünk',\n    'nem',\n    'nemcsak',\n    'nemrég',\n    'nincs',\n    'nyolc',\n    'nyolcadik',\n    'nyolcadikat',\n    'nyolcat',\n    'nyolcvan',\n    'nála',\n    'nálad',\n    'nálam',\n    'nálatok',\n    'náluk',\n    'nálunk',\n    'négy',\n    'négyet',\n    'néha',\n    'néhány',\n    'nélkül',\n    'o',\n    'oda',\n    'ok',\n    'olyan',\n    'onnan',\n    'ott',\n    'pedig',\n    'persze',\n    'pár',\n    'például',\n    'rajta',\n    'rajtad',\n    'rajtam',\n    'rajtatok',\n    'rajtuk',\n    'rajtunk',\n    'rendben',\n    'rosszul',\n    'rá',\n    'rád',\n    'rájuk',\n    'rám',\n    'ránk',\n    'rátok',\n    'régen',\n    'régóta',\n    'részére',\n    'róla',\n    'rólad',\n    'rólam',\n    'rólatok',\n    'róluk',\n    'rólunk',\n    'rögtön',\n    's',\n    'saját',\n    'se',\n    'sem',\n    'semmi',\n    'semmilyen',\n    'semmiség',\n    'senki',\n    'soha',\n    'sok',\n    'sokan',\n    'sokat',\n    'sokkal',\n    'sokszor',\n    'sokáig',\n    'során',\n    'stb.',\n    'szemben',\n    'szerbusz',\n    'szerint',\n    'szerinte',\n    'szerinted',\n    'szerintem',\n    'szerintetek',\n    'szerintük',\n    'szerintünk',\n    'szervusz',\n    'szinte',\n    'számára',\n    'száz',\n    'századik',\n    'százat',\n    'szépen',\n    'szét',\n    'szíves',\n    'szívesen',\n    'szíveskedjék',\n    'sőt',\n    'talán',\n    'tavaly',\n    'te',\n    'tegnap',\n    'tegnapelőtt',\n    'tehát',\n    'tele',\n    'teljes',\n    'tessék',\n    'ti',\n    'tied',\n    'titeket',\n    'tizedik',\n    'tizediket',\n    'tizenegy',\n    'tizenegyedik',\n    'tizenhat',\n    'tizenhárom',\n    'tizenhét',\n    'tizenkettedik',\n    'tizenkettő',\n    'tizenkilenc',\n    'tizenkét',\n    'tizennyolc',\n    'tizennégy',\n    'tizenöt',\n    'tizet',\n    'tovább',\n    'további',\n    'továbbá',\n    'távol',\n    'téged',\n    'tényleg',\n    'tíz',\n    'több',\n    'többi',\n    'többször',\n    'túl',\n    'tőle',\n    'tőled',\n    'tőlem',\n    'tőletek',\n    'tőlük',\n    'tőlünk',\n    'ugyanakkor',\n    'ugyanez',\n    'ugyanis',\n    'ugye',\n    'urak',\n    'uram',\n    'urat',\n    'utoljára',\n    'utolsó',\n    'után',\n    'utána',\n    'vagy',\n    'vagyis',\n    'vagyok',\n    'vagytok',\n    'vagyunk',\n    'vajon',\n    'valahol',\n    'valaki',\n    'valakit',\n    'valamelyik',\n    'valami',\n    'valamint',\n    'való',\n    'van',\n    'vannak',\n    'vele',\n    'veled',\n    'velem',\n    'veletek',\n    'velük',\n    'velünk',\n    'vissza',\n    'viszlát',\n    'viszont',\n    'viszontlátásra',\n    'volna',\n    'volnának',\n    'volnék',\n    'volt',\n    'voltak',\n    'voltam',\n    'voltunk',\n    'végre',\n    'végén',\n    'végül',\n    'által',\n    'általában',\n    'ám',\n    'át',\n    'éljen',\n    'én',\n    'éppen',\n    'érte',\n    'érted',\n    'értem',\n    'értetek',\n    'értük',\n    'értünk',\n    'és',\n    'év',\n    'évben',\n    'éve',\n    'évek',\n    'éves',\n    'évi',\n    'évvel',\n    'így',\n    'óta',\n    'ön',\n    'önbe',\n    'önben',\n    'önből',\n    'önhöz',\n    'önnek',\n    'önnel',\n    'önnél',\n    'önre',\n    'önről',\n    'önt',\n    'öntől',\n    'önért',\n    'önök',\n    'önökbe',\n    'önökben',\n    'önökből',\n    'önöket',\n    'önökhöz',\n    'önökkel',\n    'önöknek',\n    'önöknél',\n    'önökre',\n    'önökről',\n    'önöktől',\n    'önökért',\n    'önökön',\n    'önön',\n    'össze',\n    'öt',\n    'ötven',\n    'ötödik',\n    'ötödiket',\n    'ötöt',\n    'úgy',\n    'úgyis',\n    'úgynevezett',\n    'új',\n    'újabb',\n    'újra',\n    'úr',\n    'ő',\n    'ők',\n    'őket',\n    'őt'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,IACA,OACA,QACA,WACA,QACA,QACA,QACA,OACA,MACA,OACA,QACA,UACA,aACA,QACA,SACA,UACA,UACA,YACA,UACA,WACA,SACA,UACA,YACA,UACA,WACA,YACA,UACA,WACA,YACA,YACA,cACA,YACA,aACA,QACA,WACA,UACA,aACA,YACA,UACA,UACA,WACA,MACA,SACA,OACA,UACA,QACA,UACA,QACA,WACA,OACA,WACA,WACA,KACA,QACA,OACA,SACA,SACA,UACA,aACA,UACA,SACA,UACA,YACA,UACA,aACA,SACA,SACA,aACA,aACA,aACA,OACA,UACA,UACA,MACA,WACA,YACA,QACA,UACA,WACA,MACA,QACA,MACA,KACA,UACA,WACA,gBACA,WACA,YACA,aACA,WACA,SACA,UACA,UACA,YACA,aACA,cACA,MACA,QACA,SACA,SACA,WACA,YACA,aACA,SACA,aACA,eACA,cACA,OACA,SACA,WACA,OACA,WACA,YACA,KACA,SACA,IACA,OACA,QACA,QACA,QACA,MACA,UACA,UACA,aACA,WACA,QACA,QACA,WACA,QACA,YACA,QACA,YACA,WACA,YACA,WACA,aACA,QACA,QACA,KACA,UACA,QACA,UACA,SACA,cACA,WACA,OACA,QACA,aACA,QACA,SACA,UACA,UACA,eACA,UACA,WACA,YACA,MACA,QACA,OACA,QACA,SACA,SACA,WACA,YACA,aACA,aACA,QACA,SACA,UACA,UACA,YACA,aACA,cACA,QACA,UACA,QACA,QACA,QACA,WACA,WACA,OACA,QACA,UACA,QACA,KACA,OACA,SACA,UACA,UACA,SACA,SACA,UACA,SACA,UACA,YACA,UACA,aACA,SACA,UACA,UACA,aACA,OACA,aACA,OACA,QACA,MACA,YACA,QACA,UACA,WACA,MACA,OACA,QACA,QACA,SACA,UACA,OACA,QACA,SACA,aACA,UACA,KACA,WACA,QACA,QACA,WACA,aACA,UACA,MACA,UACA,YACA,QACA,SACA,UACA,UACA,YACA,QACA,SACA,WACA,SACA,WACA,OACA,SACA,MACA,SACA,WACA,SACA,OACA,WACA,YACA,cACA,YACA,aACA,cACA,WACA,WACA,UACA,cACA,YACA,WACA,SACA,WACA,gBACA,SACA,UACA,MACA,UACA,UACA,YACA,OACA,MACA,UACA,QACA,WACA,WACA,YACA,KACA,WACA,OACA,MACA,WACA,SACA,SACA,QACA,SACA,aACA,WACA,YACA,OACA,UACA,UACA,UACA,eACA,eACA,SACA,QACA,SACA,WACA,KACA,QACA,QACA,SACA,UACA,QACA,MACA,QACA,SACA,SACA,QACA,QACA,SACA,SACA,WACA,SACA,YACA,QACA,SACA,SACA,YACA,SACA,aACA,eACA,WACA,YACA,MACA,QACA,WACA,OACA,QACA,MACA,QACA,QACA,UACA,SACA,WACA,cACA,YACA,WACA,YACA,UACA,UACA,YACA,WACA,SACA,aACA,cACA,cACA,qBACA,oBACA,YACA,WACA,eACA,gBACA,aACA,eACA,cACA,cACA,iBACA,qBACA,uBACA,uBACA,SACA,KACA,cACA,SACA,QACA,aACA,WACA,YACA,eACA,QACA,QACA,YACA,cACA,OACA,SACA,UACA,aACA,OACA,SACA,SACA,aACA,UACA,KACA,OACA,QACA,QACA,YACA,UACA,WACA,WACA,MACA,OACA,UACA,cACA,MACA,eACA,kBACA,SACA,SACA,UACA,WACA,YACA,YACA,cACA,eACA,gBACA,WACA,YACA,iBACA,YACA,aACA,cACA,SACA,UACA,WACA,WACA,aACA,cACA,eACA,OACA,SACA,SACA,SACA,OACA,KACA,QACA,SACA,UACA,UACA,YACA,UACA,WACA,OACA,QACA,QACA,QACA,MACA,QACA,SACA,SACA,QACA,QACA,SACA,SACA,WACA,SACA,YACA,QACA,QACA,SACA,SACA,YACA,SACA,MACA,OACA,YACA,cACA,SACA,eACA,WACA,UACA,eACA,SACA,aACA,QACA,SACA,OACA,SACA,WACA,OACA,QACA,MACA,QACA,QACA,UACA,WACA,SACA,OACA,eACA,SACA,SACA,WACA,aACA,YACA,aACA,eACA,WACA,aACA,UACA,SACA,WACA,SACA,aACA,cACA,mBACA,cACA,eACA,gBACA,eACA,gBACA,iBACA,iBACA,mBACA,oBACA,qBACA,cACA,eACA,gBACA,gBACA,kBACA,mBACA,oBACA,aACA,WACA,KACA,OACA,UACA,SACA,UACA,QACA,KACA,WACA,aACA,UACA,QACA,QACA,OACA,QACA,SACA,YACA,MACA,UACA,YACA,QACA,QACA,YACA,cACA,UACA,WACA,UACA,WACA,WACA,aACA,WACA,YACA,UACA,YACA,UACA,eACA,eACA,IACA,MACA,KACA,QACA,QACA,MACA,QACA,SACA,SACA,gBACA,QACA,SACA,SACA,WACA,SACA,UACA,UACA,UACA,QACA,SACA,WACA,SACA,UACA,WACA,WACA,eACA,gBACA,UACA,WACA,WACA,aACA,WACA,YACA,eACA,IACA,WACA,KACA,MACA,QACA,YACA,cACA,QACA,OACA,MACA,QACA,QACA,SACA,UACA,YACA,WACA,OACA,UACA,WACA,UACA,WACA,YACA,YACA,cACA,eACA,gBACA,WACA,SACA,gBACA,UACA,cACA,YACA,YACA,UACA,YACA,cACA,qBACA,MACA,WACA,SACA,KACA,SACA,cACA,WACA,OACA,SACA,YACA,KACA,OACA,UACA,UACA,YACA,WACA,eACA,WACA,gBACA,cACA,gBACA,aACA,cACA,cACA,aACA,eACA,aACA,QACA,YACA,aACA,gBACA,WACA,WACA,aACA,SACA,UACA,WACA,iBACA,SACA,OACA,QACA,QACA,UACA,WACA,YACA,aACA,UACA,UACA,OACA,OACA,OACA,OACA,cACA,YACA,UACA,WACA,OACA,SACA,SACA,UACA,UACA,QACA,UACA,SACA,UACA,aACA,SACA,WACA,UACA,MACA,SACA,OACA,QACA,QACA,UACA,WACA,YACA,SACA,aACA,UACA,uBACA,QACA,cACA,YACA,OACA,SACA,SACA,UACA,WACA,cACA,cACA,WACA,kBACA,QACA,QACA,WACA,QACA,WACA,UACA,WACA,WACA,aACA,cACA,eACA,QACA,QACA,WACA,SACA,UACA,UACA,SACA,WACA,SACA,SACA,QACA,UACA,WACA,WACA,cACA,WACA,WACA,cACA,UACA,WACA,SACA,WACA,cACA,aACA,eACA,gBACA,gBACA,eACA,mBACA,gBACA,gBACA,mBACA,eACA,gBACA,gBACA,mBACA,kBACA,aACA,WACA,QACA,WACA,eACA,iBACA,aACA,SACA,WACA,iBACA,QACA,WACA,UACA,QACA,IACA,KACA,OACA,KACH"}