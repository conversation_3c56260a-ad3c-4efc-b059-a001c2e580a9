"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_algolia-NTWLS6J3_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(index, query, tag, options) {\n  let filters = options?.filters;\n  if (tag) filters = filters ? `tag:${tag} AND (${filters})` : `tag:${tag}`;\n  if (query.length === 0) {\n    const result2 = await index.search(query, {\n      distinct: 1,\n      hitsPerPage: 8,\n      ...options,\n      filters\n    });\n    return groupResults(result2.hits).filter((hit) => hit.type === \"page\");\n  }\n  const result = await index.search(query, {\n    distinct: 5,\n    hitsPerPage: 10,\n    ...options,\n    filters\n  });\n  return groupResults(result.hits);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvYWxnb2xpYS1OVFdMUzZKMy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkI7O0FBRTdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixRQUFRLEdBQUcsZUFBZTtBQUN6RDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLEtBQUssT0FBTyxRQUFRLFlBQVksSUFBSTtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUlFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcZnVtYWRvY3MtY29yZVxcZGlzdFxcYWxnb2xpYS1OVFdMUzZKMy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCIuL2NodW5rLU1MS0dBQk1LLmpzXCI7XG5cbi8vIHNyYy9zZWFyY2gvY2xpZW50L2FsZ29saWEudHNcbmZ1bmN0aW9uIGdyb3VwUmVzdWx0cyhoaXRzKSB7XG4gIGNvbnN0IGdyb3VwZWQgPSBbXTtcbiAgY29uc3Qgc2Nhbm5lZFVybHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICBmb3IgKGNvbnN0IGhpdCBvZiBoaXRzKSB7XG4gICAgaWYgKCFzY2FubmVkVXJscy5oYXMoaGl0LnVybCkpIHtcbiAgICAgIHNjYW5uZWRVcmxzLmFkZChoaXQudXJsKTtcbiAgICAgIGdyb3VwZWQucHVzaCh7XG4gICAgICAgIGlkOiBoaXQudXJsLFxuICAgICAgICB0eXBlOiBcInBhZ2VcIixcbiAgICAgICAgdXJsOiBoaXQudXJsLFxuICAgICAgICBjb250ZW50OiBoaXQudGl0bGVcbiAgICAgIH0pO1xuICAgIH1cbiAgICBncm91cGVkLnB1c2goe1xuICAgICAgaWQ6IGhpdC5vYmplY3RJRCxcbiAgICAgIHR5cGU6IGhpdC5jb250ZW50ID09PSBoaXQuc2VjdGlvbiA/IFwiaGVhZGluZ1wiIDogXCJ0ZXh0XCIsXG4gICAgICB1cmw6IGhpdC5zZWN0aW9uX2lkID8gYCR7aGl0LnVybH0jJHtoaXQuc2VjdGlvbl9pZH1gIDogaGl0LnVybCxcbiAgICAgIGNvbnRlbnQ6IGhpdC5jb250ZW50XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIGdyb3VwZWQ7XG59XG5hc3luYyBmdW5jdGlvbiBzZWFyY2hEb2NzKGluZGV4LCBxdWVyeSwgdGFnLCBvcHRpb25zKSB7XG4gIGxldCBmaWx0ZXJzID0gb3B0aW9ucz8uZmlsdGVycztcbiAgaWYgKHRhZykgZmlsdGVycyA9IGZpbHRlcnMgPyBgdGFnOiR7dGFnfSBBTkQgKCR7ZmlsdGVyc30pYCA6IGB0YWc6JHt0YWd9YDtcbiAgaWYgKHF1ZXJ5Lmxlbmd0aCA9PT0gMCkge1xuICAgIGNvbnN0IHJlc3VsdDIgPSBhd2FpdCBpbmRleC5zZWFyY2gocXVlcnksIHtcbiAgICAgIGRpc3RpbmN0OiAxLFxuICAgICAgaGl0c1BlclBhZ2U6IDgsXG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgZmlsdGVyc1xuICAgIH0pO1xuICAgIHJldHVybiBncm91cFJlc3VsdHMocmVzdWx0Mi5oaXRzKS5maWx0ZXIoKGhpdCkgPT4gaGl0LnR5cGUgPT09IFwicGFnZVwiKTtcbiAgfVxuICBjb25zdCByZXN1bHQgPSBhd2FpdCBpbmRleC5zZWFyY2gocXVlcnksIHtcbiAgICBkaXN0aW5jdDogNSxcbiAgICBoaXRzUGVyUGFnZTogMTAsXG4gICAgLi4ub3B0aW9ucyxcbiAgICBmaWx0ZXJzXG4gIH0pO1xuICByZXR1cm4gZ3JvdXBSZXN1bHRzKHJlc3VsdC5oaXRzKTtcbn1cbmV4cG9ydCB7XG4gIGdyb3VwUmVzdWx0cyxcbiAgc2VhcmNoRG9jc1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js\n"));

/***/ })

}]);