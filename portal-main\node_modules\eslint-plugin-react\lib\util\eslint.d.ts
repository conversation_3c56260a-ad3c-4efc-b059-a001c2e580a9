export function getAncestors(context: any, node: any): any;
export function getFirstTokens(context: any, node: any, count: any): any;
export function getScope(context: any, node: any): any;
export function getSourceCode(context: any): any;
export function getText(context: any, ...args: any[]): any;
export function markVariableAsUsed(name: any, node: any, context: any): any;
//# sourceMappingURL=eslint.d.ts.map