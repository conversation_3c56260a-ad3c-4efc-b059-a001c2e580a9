{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'այդ',\n    'այլ',\n    'այն',\n    'այս',\n    'դու',\n    'դուք',\n    'եմ',\n    'են',\n    'ենք',\n    'ես',\n    'եք',\n    'է',\n    'էի',\n    'էին',\n    'էինք',\n    'էիր',\n    'էիք',\n    'էր',\n    'ըստ',\n    'թ',\n    'ի',\n    'ին',\n    'իսկ',\n    'իր',\n    'կամ',\n    'համար',\n    'հետ',\n    'հետո',\n    'մենք',\n    'մեջ',\n    'մի',\n    'ն',\n    'նա',\n    'նաև',\n    'նրա',\n    'նրանք',\n    'որ',\n    'որը',\n    'որոնք',\n    'որպես',\n    'ու',\n    'ում',\n    'պիտի',\n    'վրա',\n    'և'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,MACA,MACA,MACA,MACA,MACA,OACA,KACA,KACA,MACA,KACA,KACA,IACA,KACA,MACA,OACA,MACA,MACA,KACA,MACA,IACA,IACA,KACA,MACA,KACA,MACA,QACA,MACA,OACA,OACA,MACA,KACA,IACA,KACA,MACA,MACA,QACA,KACA,MACA,QACA,QACA,KACA,MACA,OACA,MACA,IACH"}