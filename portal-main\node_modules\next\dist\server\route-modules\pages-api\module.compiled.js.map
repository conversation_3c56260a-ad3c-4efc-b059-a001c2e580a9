{"version": 3, "sources": ["../../../../src/server/route-modules/pages-api/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/pages-api/module.js')\n} else {\n  if (process.env.NODE_ENV === 'development') {\n    if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/pages-api-turbo.runtime.dev.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/pages-api.runtime.dev.js')\n    }\n  } else {\n    if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/pages-api-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/pages-api.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCC,OAAOC,OAAO,GAAGC,QAAQ;AAC3B,OAAO;IACL,IAAIL,QAAQC,GAAG,CAACK,QAAQ,KAAK,eAAe;QAC1C,IAAIN,QAAQC,GAAG,CAACM,SAAS,EAAE;YACzBJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO;YACLF,OAAOC,OAAO,GAAGC,QAAQ;QAC3B;IACF,OAAO;QACL,IAAIL,QAAQC,GAAG,CAACM,SAAS,EAAE;YACzBJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO;YACLF,OAAOC,OAAO,GAAGC,QAAQ;QAC3B;IACF;AACF"}