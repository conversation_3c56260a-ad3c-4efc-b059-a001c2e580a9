---
title: Create a Hello World Extension
---

In this chapter, we are going to create a `Hello World` extension step by step, available in Python, Go, and C++. Feel free to choose whichever language you prefer. So buckle up.

## Prerequisites

Before diving into this chapter, you’ll need to be familiar with the [basics covered earlier](./demo). Specifically, ensure you understand how to use `docker compose up` and are aware of the services running in the background.

## 1. Compose up the servers

First things first, let’s start by composing the servers. Run the following command:

<Callout>
  If the caption says `Terminal`, it means you are running the command locally.
  If the caption says `Bash`, it means you are running the command in the Docker
  container.
</Callout>

```bash title="Terminal"
docker compose up
```

Once the command is entered, you should see output similar to this:

```bash title="Terminal"
Attaching to ten_agent_dev, ten_agent_playground
ten_agent_dev         | cd agents && tman designer
ten_agent_dev         | :-)  Starting server at http://0.0.0.0:49483
ten_agent_playground  |   ▲ Next.js 14.2.4
ten_agent_playground  |   - Local:        http://localhost:3000
ten_agent_playground  |   - Network:      http://0.0.0.0:3000
ten_agent_playground  |
ten_agent_playground  |  ✓ Starting...
ten_agent_playground  |  ✓ Ready in 429ms
...
```

Now, we’ve got the following services running:

• `ten_agent_dev` at `http://0.0.0.0:49483` (dev server)

• `ten_agent_playground` at `http://localhost:3000` (TEN Agent playground)

## 2. Enter the docker container

To work within the isolated environment, run the following command:

```bash title="Terminal"
docker exec -it ten_agent_dev bash
```

## 3. Create the hello world extension

By running the following commands, an extension called `hello_world` will be created in Python, Go, or C++.

import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

<Tabs items={['Python', 'Go', 'C++']} defaultIndex={0}>
  <Tab>

```bash title="Bash"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_async_extension_python@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
  <Tab>

```bash title="Bash"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_extension_go@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
  <Tab>

```bash title="Bash"
cd /app/agents/ten_packages/extension
tman create extension hello_world --template=default_extension_cpp@0.6 --template-data class_name_prefix=HelloWorld
```

  </Tab>
</Tabs>

After running the command, the log will display something like this:

```bash title="Bash"
Package 'extension:hello_world' created successfully in '/app' in 3 seconds.
```

## 4. Adding API to the extension

Navigate into the `hello_world` directory and open manifest.json. Add the API objects with `data_in` and `cmd_out`:

```json title="./hello_world/manifest.json"
{
  "type": "extension",
  "name": "hello_world",
  "version": "0.3.1",
  "dependencies": [
    {
      "type": "system",
      "name": "ten_runtime_python",
      "version": "0.3.1"
    }
  ],
  "package": {
    "include": [
      "manifest.json",
      "property.json",
      "BUILD.gn",
      "**.tent",
      "**.py",
      "README.md",
      "tests/**"
    ]
  },
  "api": {
    "data_in": [ // [!code highlight]
      { // [!code highlight]
        "name": "text_data", // [!code highlight]
        "property": { // [!code highlight]
          "text": { // [!code highlight]
            "type": "string" // [!code highlight]
          }, // [!code highlight]
          "is_final": { // [!code highlight]
            "type": "bool" // [!code highlight]
          } // [!code highlight]
        } // [!code highlight]
      } // [!code highlight]
    ], // [!code highlight]
    "cmd_out": [ // [!code highlight]
      { // [!code highlight]
        "name": "flush" // [!code highlight]
      } // [!code highlight]
    ] // [!code highlight]
  }
```

## 5. Build the extension

Let's use `cd /app` command to go back to the root of the project, and run `make build` to build the extension.

```bash title="Bash"
cd /app

task use
```

## 6. Verify the extension

Congratulations! You’ve successfully created your first `hello_world` extension.
