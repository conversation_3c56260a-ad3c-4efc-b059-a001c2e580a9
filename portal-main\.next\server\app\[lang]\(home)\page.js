/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[lang]/(home)/page";
exports.ids = ["app/[lang]/(home)/page"];
exports.modules = {

/***/ "(rsc)/./app/[lang]/(home)/layout.tsx":
/*!**************************************!*\
  !*** ./app/[lang]/(home)/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fumadocs_ui_layouts_home__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fumadocs-ui/layouts/home */ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home.js\");\n/* harmony import */ var _app_layout_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/layout.config */ \"(rsc)/./app/layout.config.tsx\");\n\n\n\nasync function Layout({ params, children }) {\n    const { lang } = await params;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_layouts_home__WEBPACK_IMPORTED_MODULE_1__.HomeLayout, {\n        ...(0,_app_layout_config__WEBPACK_IMPORTED_MODULE_2__.baseOptions)(lang),\n        className: \"pt-0\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xhbmddLyhob21lKS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUNxRDtBQUNKO0FBRWxDLGVBQWVFLE9BQU8sRUFDbkNDLE1BQU0sRUFDTkMsUUFBUSxFQUlUO0lBQ0MsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNRjtJQUV2QixxQkFDRSw4REFBQ0gsZ0VBQVVBO1FBQUUsR0FBR0MsK0RBQVdBLENBQUNJLEtBQUs7UUFBRUMsV0FBVTtrQkFDMUNGOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxhcHBcXFtsYW5nXVxcKGhvbWUpXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBIb21lTGF5b3V0IH0gZnJvbSAnZnVtYWRvY3MtdWkvbGF5b3V0cy9ob21lJ1xuaW1wb3J0IHsgYmFzZU9wdGlvbnMgfSBmcm9tICdAL2FwcC9sYXlvdXQuY29uZmlnJ1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBMYXlvdXQoe1xuICBwYXJhbXMsXG4gIGNoaWxkcmVuLFxufToge1xuICBwYXJhbXM6IFByb21pc2U8eyBsYW5nOiBzdHJpbmcgfT5cbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufSkge1xuICBjb25zdCB7IGxhbmcgfSA9IGF3YWl0IHBhcmFtc1xuXG4gIHJldHVybiAoXG4gICAgPEhvbWVMYXlvdXQgey4uLmJhc2VPcHRpb25zKGxhbmcpfSBjbGFzc05hbWU9XCJwdC0wXCI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Ib21lTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiSG9tZUxheW91dCIsImJhc2VPcHRpb25zIiwiTGF5b3V0IiwicGFyYW1zIiwiY2hpbGRyZW4iLCJsYW5nIiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[lang]/(home)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[lang]/(home)/page.tsx":
/*!************************************!*\
  !*** ./app/[lang]/(home)/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\portal-main (1)\\portal-main\\app\\[lang]\\(home)\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[lang]/layout.tsx":
/*!*******************************!*\
  !*** ./app/[lang]/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fumadocs-ui/provider */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\");\n/* harmony import */ var next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\[lang]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\[lang]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n/* harmony import */ var _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/messages/cn.json */ \"(rsc)/./messages/cn.json\");\n/* harmony import */ var _messages_en_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/messages/en.json */ \"(rsc)/./messages/en.json\");\n/* harmony import */ var _app_metadata_config__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/metadata.config */ \"(rsc)/./app/metadata.config.ts\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _global_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../global.css */ \"(rsc)/./app/global.css\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst cn = _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__.fuma;\nconst en = _messages_en_json__WEBPACK_IMPORTED_MODULE_5__.fuma;\nconst locales = [\n    {\n        name: 'English',\n        locale: 'en'\n    },\n    {\n        name: '中文',\n        locale: 'cn'\n    }\n];\nasync function generateMetadata({ params: paramsPromise }) {\n    const { lang } = await paramsPromise;\n    return (0,_app_metadata_config__WEBPACK_IMPORTED_MODULE_6__.generateSiteMetadata)({\n        lang\n    });\n}\nasync function Layout({ params, children }) {\n    const { lang } = await params;\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n        locale: lang\n    });\n    if (!(0,next_intl__WEBPACK_IMPORTED_MODULE_10__.hasLocale)(_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.nextIntlRouting.locales, lang)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: lang,\n        className: (next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11___default().className),\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Audiowide&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"flex min-h-screen flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    locale: lang,\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_1__.RootProvider, {\n                        i18n: {\n                            locale: lang,\n                            locales: locales,\n                            translations: {\n                                cn,\n                                en\n                            }[lang]\n                        },\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                src: \"/analytics/scarf-analytics.js\",\n                                strategy: \"afterInteractive\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[lang]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/global.css":
/*!************************!*\
  !*** ./app/global.css ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e2435ebca42e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFsLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxhcHBcXGdsb2JhbC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMjQzNWViY2E0MmVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/global.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.config.tsx":
/*!*******************************!*\
  !*** ./app/layout.config.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseOptions: () => (/* binding */ baseOptions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_logo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/logo */ \"(rsc)/./components/ui/logo.tsx\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n/* harmony import */ var _messages_en_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/messages/en.json */ \"(rsc)/./messages/en.json\");\n/* harmony import */ var _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/messages/cn.json */ \"(rsc)/./messages/cn.json\");\n\n\n\n\n\nconst getMessages = (locale)=>{\n    switch(locale){\n        case 'cn':\n            return _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__;\n        case 'en':\n        default:\n            return _messages_en_json__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\nfunction baseOptions(locale) {\n    const messages = getMessages(locale);\n    return {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.i18n,\n        nav: {\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mt-0.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_logo__WEBPACK_IMPORTED_MODULE_1__.Logo, {\n                    height: 33,\n                    width: 66\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\layout.config.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\layout.config.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this),\n            url: `/${locale}`\n        },\n        links: [\n            {\n                text: messages.nav.docs,\n                url: `/${locale}/docs`,\n                active: 'nested-url'\n            }\n        ],\n        themeSwitch: {\n            enabled: true,\n            mode: 'light-dark-system'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.config.tsx\n");

/***/ }),

/***/ "(rsc)/./app/metadata.config.ts":
/*!********************************!*\
  !*** ./app/metadata.config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DESCRIPTION: () => (/* binding */ DEFAULT_DESCRIPTION),\n/* harmony export */   DEFAULT_TITLE: () => (/* binding */ DEFAULT_TITLE),\n/* harmony export */   KEYWORDS: () => (/* binding */ KEYWORDS),\n/* harmony export */   SITE_URL: () => (/* binding */ SITE_URL),\n/* harmony export */   SOCIAL_HANDLES: () => (/* binding */ SOCIAL_HANDLES),\n/* harmony export */   generateSiteMetadata: () => (/* binding */ generateSiteMetadata)\n/* harmony export */ });\nconst SITE_URL = 'https://theten.ai';\nconst DEFAULT_TITLE = 'Open-source framework for all AI agents.';\nconst DEFAULT_DESCRIPTION = 'TEN is an open-source framework designed for building multimodal conversational AI';\nconst KEYWORDS = [\n    'AI Framework',\n    'Conversational AI',\n    'Multimodal AI',\n    'Real-time AI',\n    'Voice AI',\n    'AI Agents'\n];\nconst SOCIAL_HANDLES = {\n    twitter: '@TenFramework'\n};\nfunction generateSiteMetadata({ title = DEFAULT_TITLE, description = DEFAULT_DESCRIPTION, lang }) {\n    return {\n        metadataBase: new URL(SITE_URL),\n        title: {\n            template: '%s | TEN Framework',\n            default: title\n        },\n        description,\n        keywords: KEYWORDS,\n        authors: [\n            {\n                name: 'TEN Framework Team'\n            }\n        ],\n        openGraph: {\n            title,\n            description,\n            url: SITE_URL,\n            siteName: 'TEN Framework',\n            locale: lang,\n            type: 'website'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title,\n            description,\n            creator: SOCIAL_HANDLES.twitter\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        alternates: {\n            canonical: `${SITE_URL}/${lang}`,\n            languages: {\n                'en-US': `${SITE_URL}/en`,\n                'zh-CN': `${SITE_URL}/cn`\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/metadata.config.ts\n");

/***/ }),

/***/ "(rsc)/./components/ui/logo.tsx":
/*!********************************!*\
  !*** ./components/ui/logo.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logo: () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Logo = ({ height = 32, width })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        id: \"ten-logo\",\n        \"data-name\": \"ten-logo\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 536.03 168.7\",\n        style: {\n            height: `${height}px`,\n            width: width ? `${width}px` : 'auto'\n        },\n        className: \"w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\n          @import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');\n          .cls-1 {\n            font-family: 'Audiowide', cursive;\n            font-size: 144px;\n            fill: currentColor;\n            dominant-baseline: middle;\n            text-anchor: start;\n          }\n        `\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                id: \"logo-container\",\n                \"data-name\": \"logo-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        id: \"logo-shape\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                            points: \"153.1 3.22 153.3 3.22 150.5 .72 147.3 .72 147.3 .72 34.2 .52 33.4 2.82 0 99.72 28 130.42 63.5 130.32 98.9 27.42 61.5 27.22 36.6 2.82 149.9 3.22 177.5 29.82 133.8 28.22 98.6 130.32 146.2 129.82 180.6 29.92 153.1 3.22\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                        className: \"cls-1\",\n                        x: \"200\",\n                        y: \"85\",\n                        children: \"TEN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n        lineNumber: 7,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/logo.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   nextIntlRouting: () => (/* binding */ nextIntlRouting)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst LOCALES = [\n    'en',\n    'cn'\n];\nconst DEFAULT_LOCALE = 'en';\nconst i18n = {\n    defaultLanguage: DEFAULT_LOCALE,\n    languages: LOCALES,\n    hideLocale: 'default-locale'\n};\nconst nextIntlRouting = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: LOCALES,\n    // Used when no locale matches\n    defaultLocale: DEFAULT_LOCALE\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDaUQ7QUFFakQsTUFBTUMsVUFBVTtJQUFDO0lBQU07Q0FBSztBQUM1QixNQUFNQyxpQkFBaUI7QUFFaEIsTUFBTUMsT0FBbUI7SUFDOUJDLGlCQUFpQkY7SUFDakJHLFdBQVdKO0lBQ1hLLFlBQVk7QUFDZCxFQUFDO0FBRU0sTUFBTUMsa0JBQWtCUCw2REFBYUEsQ0FBQztJQUMzQywyQ0FBMkM7SUFDM0NRLFNBQVNQO0lBQ1QsOEJBQThCO0lBQzlCUSxlQUFlUDtBQUNqQixHQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXGxpYlxcaTE4bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEkxOG5Db25maWcgfSBmcm9tICdmdW1hZG9jcy1jb3JlL2kxOG4nXG5pbXBvcnQgeyBkZWZpbmVSb3V0aW5nIH0gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnXG5cbmNvbnN0IExPQ0FMRVMgPSBbJ2VuJywgJ2NuJ11cbmNvbnN0IERFRkFVTFRfTE9DQUxFID0gJ2VuJ1xuXG5leHBvcnQgY29uc3QgaTE4bjogSTE4bkNvbmZpZyA9IHtcbiAgZGVmYXVsdExhbmd1YWdlOiBERUZBVUxUX0xPQ0FMRSxcbiAgbGFuZ3VhZ2VzOiBMT0NBTEVTLFxuICBoaWRlTG9jYWxlOiAnZGVmYXVsdC1sb2NhbGUnLFxufVxuXG5leHBvcnQgY29uc3QgbmV4dEludGxSb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XG4gIC8vIEEgbGlzdCBvZiBhbGwgbG9jYWxlcyB0aGF0IGFyZSBzdXBwb3J0ZWRcbiAgbG9jYWxlczogTE9DQUxFUyxcbiAgLy8gVXNlZCB3aGVuIG5vIGxvY2FsZSBtYXRjaGVzXG4gIGRlZmF1bHRMb2NhbGU6IERFRkFVTFRfTE9DQUxFLFxufSlcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwiTE9DQUxFUyIsIkRFRkFVTFRfTE9DQUxFIiwiaTE4biIsImRlZmF1bHRMYW5ndWFnZSIsImxhbmd1YWdlcyIsImhpZGVMb2NhbGUiLCJuZXh0SW50bFJvdXRpbmciLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./lib/next-intl-requests.ts":
/*!***********************************!*\
  !*** ./lib/next-intl-requests.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // Typically corresponds to the `[locale]` segment\n    const requested = await requestLocale;\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.hasLocale)(_lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting.locales, requested) ? requested : _lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting.defaultLocale;\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbmV4dC1pbnRsLXJlcXVlc3RzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUQ7QUFDZDtBQUNPO0FBRTVDLGlFQUFlQSw0REFBZ0JBLENBQUMsT0FBTyxFQUFFRyxhQUFhLEVBQUU7SUFDdEQsa0RBQWtEO0lBQ2xELE1BQU1DLFlBQVksTUFBTUQ7SUFDeEIsTUFBTUUsU0FBU0osb0RBQVNBLENBQUNDLHNEQUFlQSxDQUFDSSxPQUFPLEVBQUVGLGFBQzlDQSxZQUNBRixzREFBZUEsQ0FBQ0ssYUFBYTtJQUVqQyxPQUFPO1FBQ0xGO1FBQ0FHLFVBQVUsQ0FBQyxNQUFNLHlFQUFPLEdBQWEsRUFBRUgsT0FBTyxNQUFNLEdBQUdJLE9BQU87SUFDaEU7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXGxpYlxcbmV4dC1pbnRsLXJlcXVlc3RzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFJlcXVlc3RDb25maWcgfSBmcm9tICduZXh0LWludGwvc2VydmVyJ1xuaW1wb3J0IHsgaGFzTG9jYWxlIH0gZnJvbSAnbmV4dC1pbnRsJ1xuaW1wb3J0IHsgbmV4dEludGxSb3V0aW5nIH0gZnJvbSAnQC9saWIvaTE4bidcblxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoeyByZXF1ZXN0TG9jYWxlIH0pID0+IHtcbiAgLy8gVHlwaWNhbGx5IGNvcnJlc3BvbmRzIHRvIHRoZSBgW2xvY2FsZV1gIHNlZ21lbnRcbiAgY29uc3QgcmVxdWVzdGVkID0gYXdhaXQgcmVxdWVzdExvY2FsZVxuICBjb25zdCBsb2NhbGUgPSBoYXNMb2NhbGUobmV4dEludGxSb3V0aW5nLmxvY2FsZXMsIHJlcXVlc3RlZClcbiAgICA/IHJlcXVlc3RlZFxuICAgIDogbmV4dEludGxSb3V0aW5nLmRlZmF1bHRMb2NhbGVcblxuICByZXR1cm4ge1xuICAgIGxvY2FsZSxcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdCxcbiAgfVxufSlcbiJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwiaGFzTG9jYWxlIiwibmV4dEludGxSb3V0aW5nIiwicmVxdWVzdExvY2FsZSIsInJlcXVlc3RlZCIsImxvY2FsZSIsImxvY2FsZXMiLCJkZWZhdWx0TG9jYWxlIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/next-intl-requests.ts\n");

/***/ }),

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./cn.json": "(rsc)/./messages/cn.json",
	"./en.json": "(rsc)/./messages/en.json"
};

function webpackAsyncContext(req) {
	return Promise.resolve().then(() => {
		if(!__webpack_require__.o(map, req)) {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		}

		var id = map[req];
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./messages/cn.json":
/*!**************************!*\
  !*** ./messages/cn.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"nav":{"docs":"文档","blog":"博客"},"fuma":{"search":"搜索","searchNoResult":"未找到结果","toc":"目录","tocNoHeadings":"没有标题","lastUpdate":"最后更新","chooseLanguage":"选择语言","nextPage":"下一页","previousPage":"上一页","chooseTheme":"选择主题","editOnGithub":"在 GitHub 上编辑"},"blog":{"latestPosts":"最新博客文章","discoverLatestArticles":"发现我们关于 TEN framework、AI 开发等方面的最新文章","readMore":"阅读更多","backToBlog":"返回博客","writtenBy":"作者","publishedOn":"发布于"},"homePage":{"titlePrefix":"搭建","titleRealtime":"实时","titleMultimodal":"可定制","titleLowlantency":"低延迟","titleHighperformance":"高性能","titleEdgeCloud":"可边缘云","titleSuffix":"语音 AI Agent","readLaunchArticle":"阅读我们的博客了解更多","heroDescription":"TEN 是一个用于搭建实时多模态的对话式 AI 引擎的开源框架","heroBtnTryTenAgent":"体验 TEN Agent","heroBtnReadDoc":"文档","bannerAnnouncement":"欢迎 VAD 和 Turn Detection 加入 TEN 开源全家桶!","huggingFaceSpace":"体验语音检测和打断","supportedBy":"共同支持来自 TEN 社区"}}');

/***/ }),

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"nav":{"docs":"Documentation","blog":"Blog"},"fuma":{"search":"Search","searchNoResult":"No results found","toc":"Table of Contents","tocNoHeadings":"No headings found","lastUpdate":"Last Updated","chooseLanguage":"Choose Language","nextPage":"Next Page","previousPage":"Previous Page","chooseTheme":"Choose Theme","editOnGithub":"Edit on GitHub"},"blog":{"latestPosts":"Latest Blog Posts","discoverLatestArticles":"Discover our latest articles about TEN Framework, TEN Agent and Conversational AI","readMore":"Read more","backToBlog":"Back to Blog","writtenBy":"Written by","publishedOn":"Published on"},"homePage":{"titlePrefix":"Build","titleLowlantency":"Real-Time","titleMultimodal":"Customizable","titleHighperformance":"High-performance","titleEdgeCloud":"Low-Latency","titleSuffix":"Voice AI Agents","readLaunchArticle":"Read more on our blog","heroDescription":"An open-source framework designed for building conversational AI","supportedBy":"supported by the TEN community and","heroBtnTryTenAgent":"Talk to TEN Agent","huggingFaceSpace":"Try VAD and Turn Detection","heroBtnReadDoc":"Documentation","bannerAnnouncement":"are now part of the TEN open-source ecosystem!"}}');

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/layout.tsx */ \"(rsc)/./app/[lang]/layout.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/layout.tsx */ \"(rsc)/./app/[lang]/(home)/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(rsc)/./app/[lang]/(home)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[lang]',\n        {\n        children: [\n        '(home)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[lang]/(home)/page\",\n        pathname: \"/[lang]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(rsc)/./app/[lang]/(home)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lMjAoMSklNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q2FwcCU1QyU1QyU1QmxhbmclNUQlNUMlNUMoaG9tZSklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW4gKDEpXFxcXHBvcnRhbC1tYWluXFxcXGFwcFxcXFxbbGFuZ11cXFxcKGhvbWUpXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-core/dist/link.js */ \"(rsc)/./node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"2000x2000\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPW1keCZwYWdlRXh0ZW5zaW9ucz1tZCZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIyMDAweDIwMDBcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/_components.tsx":
/*!*******************************************!*\
  !*** ./app/[lang]/(home)/_components.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero),\n/* harmony export */   ProjectsShowcase: () => (/* binding */ ProjectsShowcase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! canvas-confetti */ \"(ssr)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/next-intl-navigation */ \"(ssr)/./lib/next-intl-navigation.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants */ \"(ssr)/./constants/index.ts\");\n/* harmony import */ var _sample_projects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./sample-projects */ \"(ssr)/./app/[lang]/(home)/sample-projects.ts\");\n/* __next_internal_client_entry_do_not_use__ ProjectsShowcase,Hero auto */ \n\n\n\n\n\n\n\n\n\n\nconst TITLES = [\n    'titleLowlantency',\n    'titleMultimodal',\n    'titleEdgeCloud'\n];\nconst titleVariants = {\n    visible: {\n        y: 0,\n        opacity: 1\n    },\n    hidden: (direction)=>({\n            y: direction > 0 ? -150 : 150,\n            opacity: 0\n        })\n};\nconst createConfetti = (e)=>{\n    const count = 88;\n    const defaults = {\n        origin: {\n            x: e.clientX / window.innerWidth,\n            y: (e.clientY + 50) / window.innerHeight\n        },\n        scalar: 0.6\n    };\n    function fire(particleRatio, opts = {}) {\n        (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            ...defaults,\n            ...opts,\n            particleCount: Math.floor(count * particleRatio)\n        });\n    }\n    fire(0.25, {\n        spread: 20,\n        startVelocity: 20\n    });\n    fire(0.2, {\n        spread: 35,\n        startVelocity: 15\n    });\n    fire(0.35, {\n        spread: 30,\n        decay: 0.91,\n        scalar: 0.4,\n        startVelocity: 15\n    });\n    fire(0.1, {\n        spread: 40,\n        startVelocity: 10,\n        decay: 0.92,\n        scalar: 0.8\n    });\n    fire(0.1, {\n        spread: 40,\n        startVelocity: 10\n    });\n};\nfunction ProjectsShowcase(props) {\n    const { className } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('w-full py-20 bg-gray-50/50 dark:bg-gray-900/50', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-4xl font-bold tracking-tight text-black\",\n                            children: \"From the Community\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-black mx-auto max-w-2xl text-lg\",\n                            children: \"Discover amazing projects built with TEN Framework\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: _sample_projects__WEBPACK_IMPORTED_MODULE_7__.SAMPLE_PROJECTS.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                            href: project.href,\n                            className: \"group relative overflow-hidden rounded-2xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video w-full overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-full items-center justify-center text-gray-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: \"Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block rounded-full bg-blue-100 dark:bg-blue-900/30 px-3 py-1 text-xs font-medium text-blue-700 dark:text-blue-300\",\n                                                    children: project.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        project.remixes,\n                                                        \" Remixes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-2 text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                            children: project.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4 text-sm text-gray-600 dark:text-gray-300 line-clamp-2\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: project.author.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: project.author\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        size: \"lg\",\n                        className: \"gap-2\",\n                        children: [\n                            \"View All Projects\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nfunction Hero(props) {\n    const { className } = props;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)('homePage');\n    const [titleNumber, setTitleNumber] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Hero.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"Hero.useEffect.timeoutId\": ()=>{\n                    if (titleNumber === TITLES.length - 1) {\n                        setTitleNumber(0);\n                    } else {\n                        setTitleNumber(titleNumber + 1);\n                    }\n                }\n            }[\"Hero.useEffect.timeoutId\"], 2000);\n            return ({\n                \"Hero.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        titleNumber\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('text-foreground w-full', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center gap-8 pt-4 pb-20 lg:pt-8 lg:pb-60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"secondary\",\n                            size: \"sm\",\n                            className: \"gap-2 bg-blue-600/[0.05] text-blue-600 transition-all duration-600 hover:scale-105 hover:bg-blue-600/[0.08] hover:text-blue-500 py-7 sm:py-0\",\n                            asChild: true,\n                            onClick: (e)=>createConfetti(e),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    \"\\uD83C\\uDF89\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                        href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_VAD,\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2\",\n                                        children: \"TEN VAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base\",\n                                        children: \"and\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                        href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_TURN_DETECTION,\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2\",\n                                        children: \"TEN Turn Detection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base\",\n                                        children: \"are now part of the TEN open-source ecosystem!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"font-regular text-center text-5xl tracking-tighter md:text-7xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-spektr-cyan-50 font-medium\",\n                                        children: t('titlePrefix')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative flex w-full justify-center overflow-hidden text-center leading-tight md:leading-normal\",\n                                        children: [\n                                            \"\\xa0\",\n                                            TITLES.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_10__.motion.span, {\n                                                    className: \"absolute font-bold\",\n                                                    initial: \"hidden\",\n                                                    animate: titleNumber === index ? 'visible' : 'hidden',\n                                                    variants: titleVariants,\n                                                    custom: titleNumber > index ? 1 : -1,\n                                                    transition: {\n                                                        type: 'spring',\n                                                        stiffness: 35,\n                                                        duration: 0.5\n                                                    },\n                                                    children: t(title)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-spektr-cyan-50 font-medium\",\n                                        children: t('titleSuffix')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground max-w-2xl text-center text-lg leading-relaxed font-medium tracking-tight md:text-xl dark:text-gray-300\",\n                                children: t('heroDescription')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"gap-4\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                    href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_AGENT,\n                                    target: \"_blank\",\n                                    children: [\n                                        t('heroBtnTryTenAgent'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"gap-4\",\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                    href: _constants__WEBPACK_IMPORTED_MODULE_6__.HUGGING_FACE_SPACE,\n                                    target: \"_blank\",\n                                    children: [\n                                        t('huggingFaceSpace'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground/100 max-w-2xl text-center text-sm leading-relaxed font-normal tracking-tight md:text-base dark:text-gray-300\",\n                        children: [\n                            t('supportedBy'),\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                href: \"https://www.agora.io/en/\",\n                                target: \"_blank\",\n                                className: \"text-spektr-cyan-100 underline decoration-gray-300 underline-offset-5 hover:text-[] hover:decoration-[#13C2FF]\",\n                                children: \"Agora\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/_components.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/page.tsx":
/*!************************************!*\
  !*** ./app/[lang]/(home)/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lang_home_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[lang]/(home)/_components */ \"(ssr)/./app/[lang]/(home)/_components.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundVideo = ()=>{\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BackgroundVideo.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"BackgroundVideo.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BackgroundVideo.useEffect\": ()=>{\n            if (videoRef.current) {\n                // Reset loaded state when theme changes\n                setIsLoaded(false);\n                // Reset the video to start playing from beginning\n                videoRef.current.currentTime = 0;\n                videoRef.current.load();\n                videoRef.current.play();\n            }\n        }\n    }[\"BackgroundVideo.useEffect\"], [\n        resolvedTheme\n    ]);\n    if (!mounted) return null;\n    const videoSrc = resolvedTheme === 'dark' ? 'https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg-dark.mp4' : 'https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg2.mp4';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n        ref: videoRef,\n        autoPlay: true,\n        loop: true,\n        muted: true,\n        playsInline: true,\n        onLoadedData: ()=>setIsLoaded(true),\n        className: `absolute inset-0 z-0 h-full w-full object-cover transition-opacity duration-700 ${isLoaded ? 'opacity-37 dark:opacity-57' : 'opacity-0'}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                src: videoSrc,\n                type: \"video/mp4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            \"Your browser does not support the video tag.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex h-[calc(100dvh-56px)] flex-1 flex-col justify-center overflow-hidden text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundVideo, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lang_home_components__WEBPACK_IMPORTED_MODULE_3__.Hero, {\n                    className: \"relative z-10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/sample-projects.ts":
/*!**********************************************!*\
  !*** ./app/[lang]/(home)/sample-projects.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SAMPLE_PROJECTS: () => (/* binding */ SAMPLE_PROJECTS)\n/* harmony export */ });\n// Sample project data for the ProjectsShowcase component\nconst SAMPLE_PROJECTS = [\n    {\n        id: 1,\n        title: \"TEN Agent Voice Assistant\",\n        description: \"Real-time multimodal AI agent with voice capabilities\",\n        category: \"AI Agent\",\n        author: \"TEN Team\",\n        remixes: \"2.1k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 2,\n        title: \"Real-time Translation Bot\",\n        description: \"Multi-language translation with voice synthesis\",\n        category: \"Translation\",\n        author: \"Community\",\n        remixes: \"1.8k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 3,\n        title: \"Smart Meeting Assistant\",\n        description: \"AI-powered meeting transcription and summarization\",\n        category: \"Productivity\",\n        author: \"DevTeam\",\n        remixes: \"1.5k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 4,\n        title: \"Interactive Storyteller\",\n        description: \"AI that creates and narrates interactive stories\",\n        category: \"Entertainment\",\n        author: \"StoryAI\",\n        remixes: \"2.3k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 5,\n        title: \"Code Review Assistant\",\n        description: \"AI-powered code analysis and suggestions\",\n        category: \"Developer Tools\",\n        author: \"CodeBot\",\n        remixes: \"1.9k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 6,\n        title: \"Health Monitoring Agent\",\n        description: \"Personal health assistant with voice interaction\",\n        category: \"Healthcare\",\n        author: \"HealthTech\",\n        remixes: \"1.2k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/sample-projects.ts\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n            destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n            outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n            secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n            ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n            link: 'text-primary underline-offset-4 hover:underline'\n        },\n        size: {\n            default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n            sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n            lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n            icon: 'size-9'\n        }\n    },\n    defaultVariants: {\n        variant: 'default',\n        size: 'default'\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : 'button';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main (1)\\\\portal-main\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNhO0FBQ3NCO0FBRWpDO0FBRWhDLE1BQU1JLGlCQUFpQkYsNkRBQUdBLENBQ3hCLCtiQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUNFO1lBQ0ZDLGFBQ0U7WUFDRkMsU0FDRTtZQUNGQyxXQUNFO1lBQ0ZDLE9BQ0U7WUFDRkMsTUFBTTtRQUNSO1FBQ0FDLE1BQU07WUFDSk4sU0FBUztZQUNUTyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZYLFNBQVM7UUFDVE8sTUFBTTtJQUNSO0FBQ0Y7QUFHRixTQUFTSyxPQUFPLEVBQ2RDLFNBQVMsRUFDVGIsT0FBTyxFQUNQTyxJQUFJLEVBQ0pPLFVBQVUsS0FBSyxFQUNmLEdBQUdDLE9BSUY7SUFDRCxNQUFNQyxPQUFPRixVQUFVbkIsc0RBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUNxQjtRQUNDQyxhQUFVO1FBQ1ZKLFdBQVdoQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTTztZQUFNTTtRQUFVO1FBQ3ZELEdBQUdFLEtBQUs7Ozs7OztBQUdmO0FBRWlDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXGNvbXBvbmVudHNcXHVpXFxidXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgU2xvdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1zbG90J1xuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gJ2NsYXNzLXZhcmlhbmNlLWF1dGhvcml0eSdcblxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgJ2JnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXByaW1hcnkvOTAnLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICAnYmctZGVzdHJ1Y3RpdmUgdGV4dC13aGl0ZSBzaGFkb3cteHMgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAgZm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6Zm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzQwIGRhcms6YmctZGVzdHJ1Y3RpdmUvNjAnLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgICdib3JkZXIgYmctYmFja2dyb3VuZCBzaGFkb3cteHMgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBkYXJrOmJvcmRlci1pbnB1dCBkYXJrOmhvdmVyOmJnLWlucHV0LzUwJyxcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgICdiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBzaGFkb3cteHMgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwJyxcbiAgICAgICAgZ2hvc3Q6XG4gICAgICAgICAgJ2hvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6aG92ZXI6YmctYWNjZW50LzUwJyxcbiAgICAgICAgbGluazogJ3RleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lJyxcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6ICdoLTkgcHgtNCBweS0yIGhhcy1bPnN2Z106cHgtMycsXG4gICAgICAgIHNtOiAnaC04IHJvdW5kZWQtbWQgZ2FwLTEuNSBweC0zIGhhcy1bPnN2Z106cHgtMi41JyxcbiAgICAgICAgbGc6ICdoLTEwIHJvdW5kZWQtbWQgcHgtNiBoYXMtWz5zdmddOnB4LTQnLFxuICAgICAgICBpY29uOiAnc2l6ZS05JyxcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6ICdkZWZhdWx0JyxcbiAgICAgIHNpemU6ICdkZWZhdWx0JyxcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEJ1dHRvbih7XG4gIGNsYXNzTmFtZSxcbiAgdmFyaWFudCxcbiAgc2l6ZSxcbiAgYXNDaGlsZCA9IGZhbHNlLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8J2J1dHRvbic+ICZcbiAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4gJiB7XG4gICAgYXNDaGlsZD86IGJvb2xlYW5cbiAgfSkge1xuICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiAnYnV0dG9uJ1xuXG4gIHJldHVybiAoXG4gICAgPENvbXBcbiAgICAgIGRhdGEtc2xvdD1cImJ1dHRvblwiXG4gICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IEJ1dHRvbiwgYnV0dG9uVmFyaWFudHMgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwiQ29tcCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./constants/index.ts":
/*!****************************!*\
  !*** ./constants/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOG: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.BLOG),\n/* harmony export */   HUGGING_FACE_SPACE: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.HUGGING_FACE_SPACE),\n/* harmony export */   URL_TEN_AGENT: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_AGENT),\n/* harmony export */   URL_TEN_FAMILY: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_FAMILY),\n/* harmony export */   URL_TEN_FRAMEWORK_DOC: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_FRAMEWORK_DOC),\n/* harmony export */   URL_TEN_TURN_DETECTION: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_TURN_DETECTION),\n/* harmony export */   URL_TEN_VAD: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_VAD)\n/* harmony export */ });\n/* harmony import */ var _constants_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants/url */ \"(ssr)/./constants/url.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25zdGFudHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW4gKDEpXFxwb3J0YWwtbWFpblxcY29uc3RhbnRzXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICdAL2NvbnN0YW50cy91cmwnXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./constants/index.ts\n");

/***/ }),

/***/ "(ssr)/./constants/url.ts":
/*!**************************!*\
  !*** ./constants/url.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOG: () => (/* binding */ BLOG),\n/* harmony export */   HUGGING_FACE_SPACE: () => (/* binding */ HUGGING_FACE_SPACE),\n/* harmony export */   URL_TEN_AGENT: () => (/* binding */ URL_TEN_AGENT),\n/* harmony export */   URL_TEN_FAMILY: () => (/* binding */ URL_TEN_FAMILY),\n/* harmony export */   URL_TEN_FRAMEWORK_DOC: () => (/* binding */ URL_TEN_FRAMEWORK_DOC),\n/* harmony export */   URL_TEN_TURN_DETECTION: () => (/* binding */ URL_TEN_TURN_DETECTION),\n/* harmony export */   URL_TEN_VAD: () => (/* binding */ URL_TEN_VAD)\n/* harmony export */ });\nconst URL_TEN_AGENT = 'https://agent.theten.ai';\nconst BLOG = '/blog';\nconst URL_TEN_FRAMEWORK_DOC = '/docs/ten_framework/concept_overview';\nconst HUGGING_FACE_SPACE = 'https://huggingface.co/spaces/TEN-framework/ten-agent-demo';\nconst URL_TEN_TURN_DETECTION = 'https://github.com/ten-framework/ten-turn-detection';\nconst URL_TEN_FAMILY = 'https://github.com/ten-framework?view_as=public';\nconst URL_TEN_VAD = 'https://github.com/ten-framework/ten-vad';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25zdGFudHMvdXJsLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsMEJBQXlCO0FBQy9DLE1BQU1DLE9BQU8sUUFBTztBQUNwQixNQUFNQyx3QkFBd0IsdUNBQXNDO0FBQ3BFLE1BQU1DLHFCQUNYLDZEQUE0RDtBQUN2RCxNQUFNQyx5QkFDWCxzREFBcUQ7QUFDaEQsTUFBTUMsaUJBQWlCLGtEQUFpRDtBQUN4RSxNQUFNQyxjQUFjLDJDQUEwQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxjb25zdGFudHNcXHVybC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVVJMX1RFTl9BR0VOVCA9ICdodHRwczovL2FnZW50LnRoZXRlbi5haSdcbmV4cG9ydCBjb25zdCBCTE9HID0gJy9ibG9nJ1xuZXhwb3J0IGNvbnN0IFVSTF9URU5fRlJBTUVXT1JLX0RPQyA9ICcvZG9jcy90ZW5fZnJhbWV3b3JrL2NvbmNlcHRfb3ZlcnZpZXcnXG5leHBvcnQgY29uc3QgSFVHR0lOR19GQUNFX1NQQUNFID1cbiAgJ2h0dHBzOi8vaHVnZ2luZ2ZhY2UuY28vc3BhY2VzL1RFTi1mcmFtZXdvcmsvdGVuLWFnZW50LWRlbW8nXG5leHBvcnQgY29uc3QgVVJMX1RFTl9UVVJOX0RFVEVDVElPTiA9XG4gICdodHRwczovL2dpdGh1Yi5jb20vdGVuLWZyYW1ld29yay90ZW4tdHVybi1kZXRlY3Rpb24nXG5leHBvcnQgY29uc3QgVVJMX1RFTl9GQU1JTFkgPSAnaHR0cHM6Ly9naXRodWIuY29tL3Rlbi1mcmFtZXdvcms/dmlld19hcz1wdWJsaWMnXG5leHBvcnQgY29uc3QgVVJMX1RFTl9WQUQgPSAnaHR0cHM6Ly9naXRodWIuY29tL3Rlbi1mcmFtZXdvcmsvdGVuLXZhZCdcbiJdLCJuYW1lcyI6WyJVUkxfVEVOX0FHRU5UIiwiQkxPRyIsIlVSTF9URU5fRlJBTUVXT1JLX0RPQyIsIkhVR0dJTkdfRkFDRV9TUEFDRSIsIlVSTF9URU5fVFVSTl9ERVRFQ1RJT04iLCJVUkxfVEVOX0ZBTUlMWSIsIlVSTF9URU5fVkFEIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./constants/url.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   nextIntlRouting: () => (/* binding */ nextIntlRouting)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst LOCALES = [\n    'en',\n    'cn'\n];\nconst DEFAULT_LOCALE = 'en';\nconst i18n = {\n    defaultLanguage: DEFAULT_LOCALE,\n    languages: LOCALES,\n    hideLocale: 'default-locale'\n};\nconst nextIntlRouting = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: LOCALES,\n    // Used when no locale matches\n    defaultLocale: DEFAULT_LOCALE\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDaUQ7QUFFakQsTUFBTUMsVUFBVTtJQUFDO0lBQU07Q0FBSztBQUM1QixNQUFNQyxpQkFBaUI7QUFFaEIsTUFBTUMsT0FBbUI7SUFDOUJDLGlCQUFpQkY7SUFDakJHLFdBQVdKO0lBQ1hLLFlBQVk7QUFDZCxFQUFDO0FBRU0sTUFBTUMsa0JBQWtCUCw2REFBYUEsQ0FBQztJQUMzQywyQ0FBMkM7SUFDM0NRLFNBQVNQO0lBQ1QsOEJBQThCO0lBQzlCUSxlQUFlUDtBQUNqQixHQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXGxpYlxcaTE4bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEkxOG5Db25maWcgfSBmcm9tICdmdW1hZG9jcy1jb3JlL2kxOG4nXG5pbXBvcnQgeyBkZWZpbmVSb3V0aW5nIH0gZnJvbSAnbmV4dC1pbnRsL3JvdXRpbmcnXG5cbmNvbnN0IExPQ0FMRVMgPSBbJ2VuJywgJ2NuJ11cbmNvbnN0IERFRkFVTFRfTE9DQUxFID0gJ2VuJ1xuXG5leHBvcnQgY29uc3QgaTE4bjogSTE4bkNvbmZpZyA9IHtcbiAgZGVmYXVsdExhbmd1YWdlOiBERUZBVUxUX0xPQ0FMRSxcbiAgbGFuZ3VhZ2VzOiBMT0NBTEVTLFxuICBoaWRlTG9jYWxlOiAnZGVmYXVsdC1sb2NhbGUnLFxufVxuXG5leHBvcnQgY29uc3QgbmV4dEludGxSb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XG4gIC8vIEEgbGlzdCBvZiBhbGwgbG9jYWxlcyB0aGF0IGFyZSBzdXBwb3J0ZWRcbiAgbG9jYWxlczogTE9DQUxFUyxcbiAgLy8gVXNlZCB3aGVuIG5vIGxvY2FsZSBtYXRjaGVzXG4gIGRlZmF1bHRMb2NhbGU6IERFRkFVTFRfTE9DQUxFLFxufSlcbiJdLCJuYW1lcyI6WyJkZWZpbmVSb3V0aW5nIiwiTE9DQUxFUyIsIkRFRkFVTFRfTE9DQUxFIiwiaTE4biIsImRlZmF1bHRMYW5ndWFnZSIsImxhbmd1YWdlcyIsImhpZGVMb2NhbGUiLCJuZXh0SW50bFJvdXRpbmciLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/next-intl-navigation.ts":
/*!*************************************!*\
  !*** ./lib/next-intl-navigation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   getPathname: () => (/* binding */ getPathname),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n\n\n// Lightweight wrappers around Next.js' navigation\n// APIs that consider the routing configuration\nconst { Link, redirect, usePathname, useRouter, getPathname } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvbmV4dC1pbnRsLW5hdmlnYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1RDtBQUNYO0FBRTVDLGtEQUFrRDtBQUNsRCwrQ0FBK0M7QUFDeEMsTUFBTSxFQUFFRSxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBRSxHQUNsRU4sZ0VBQWdCQSxDQUFDQyxzREFBZUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpbiAoMSlcXHBvcnRhbC1tYWluXFxsaWJcXG5leHQtaW50bC1uYXZpZ2F0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZU5hdmlnYXRpb24gfSBmcm9tICduZXh0LWludGwvbmF2aWdhdGlvbidcbmltcG9ydCB7IG5leHRJbnRsUm91dGluZyB9IGZyb20gJ0AvbGliL2kxOG4nXG5cbi8vIExpZ2h0d2VpZ2h0IHdyYXBwZXJzIGFyb3VuZCBOZXh0LmpzJyBuYXZpZ2F0aW9uXG4vLyBBUElzIHRoYXQgY29uc2lkZXIgdGhlIHJvdXRpbmcgY29uZmlndXJhdGlvblxuZXhwb3J0IGNvbnN0IHsgTGluaywgcmVkaXJlY3QsIHVzZVBhdGhuYW1lLCB1c2VSb3V0ZXIsIGdldFBhdGhuYW1lIH0gPVxuICBjcmVhdGVOYXZpZ2F0aW9uKG5leHRJbnRsUm91dGluZylcbiJdLCJuYW1lcyI6WyJjcmVhdGVOYXZpZ2F0aW9uIiwibmV4dEludGxSb3V0aW5nIiwiTGluayIsInJlZGlyZWN0IiwidXNlUGF0aG5hbWUiLCJ1c2VSb3V0ZXIiLCJnZXRQYXRobmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/next-intl-navigation.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluICgxKVxccG9ydGFsLW1haW5cXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSAnY2xzeCdcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tICd0YWlsd2luZC1tZXJnZSdcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(ssr)/./app/[lang]/(home)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lMjAoMSklNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q2FwcCU1QyU1QyU1QmxhbmclNUQlNUMlNUMoaG9tZSklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW4gKDEpXFxcXHBvcnRhbC1tYWluXFxcXGFwcFxcXFxbbGFuZ11cXFxcKGhvbWUpXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-core/dist/link.js */ \"(ssr)/./node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(ssr)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(ssr)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lMjAoMSklNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q2Z1bWFkb2NzLXVpJTVDJTVDZGlzdCU1QyU1Q3Byb3ZpZGVyJTVDJTVDaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSb290UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRGVlcEluc2lnaHQlNUMlNUNEb3dubG9hZHMlNUMlNUNwb3J0YWwtbWFpbiUyMCgxKSU1QyU1Q3BvcnRhbC1tYWluJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q2RldmVsb3BtZW50JTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRGVlcEluc2lnaHQlNUMlNUNEb3dubG9hZHMlNUMlNUNwb3J0YWwtbWFpbiUyMCgxKSU1QyU1Q3BvcnRhbC1tYWluJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNzY3JpcHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lMjAoMSklNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUMlNUJsYW5nJTVEJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRGVlcEluc2lnaHQlNUMlNUNEb3dubG9hZHMlNUMlNUNwb3J0YWwtbWFpbiUyMCgxKSU1QyU1Q3BvcnRhbC1tYWluJTVDJTVDYXBwJTVDJTVDZ2xvYmFsLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQTJMO0FBQzNMO0FBQ0Esd1FBQXFOO0FBQ3JOO0FBQ0Esb01BQWlKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJSb290UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW4gKDEpXFxcXHBvcnRhbC1tYWluXFxcXG5vZGVfbW9kdWxlc1xcXFxmdW1hZG9jcy11aVxcXFxkaXN0XFxcXHByb3ZpZGVyXFxcXGluZGV4LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcRGVlcEluc2lnaHRcXFxcRG93bmxvYWRzXFxcXHBvcnRhbC1tYWluICgxKVxcXFxwb3J0YWwtbWFpblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXGRldmVsb3BtZW50XFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW4gKDEpXFxcXHBvcnRhbC1tYWluXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXHNjcmlwdC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%20(1)%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/fumadocs-core","vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/fumadocs-ui","vendor-chunks/lucide-react","vendor-chunks/@formatjs","vendor-chunks/tailwind-merge","vendor-chunks/@floating-ui","vendor-chunks/use-intl","vendor-chunks/next-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/get-nonce","vendor-chunks/motion","vendor-chunks/canvas-confetti"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%20(1)%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();