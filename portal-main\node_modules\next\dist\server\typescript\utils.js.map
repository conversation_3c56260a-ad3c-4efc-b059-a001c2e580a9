{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "sourcesContent": ["import type { VirtualTypeScriptEnvironment } from 'next/dist/compiled/@typescript/vfs'\nimport {\n  createFSBackedSystem,\n  createDefaultMapFromNodeModules,\n  createVirtualTypeScriptEnvironment,\n} from 'next/dist/compiled/@typescript/vfs'\n\nimport path, { join } from 'path'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\ntype TypeScript = typeof import('typescript/lib/tsserverlibrary')\n\nlet ts: TypeScript\nlet info: tsModule.server.PluginCreateInfo\nlet appDirRegExp: RegExp\nexport let virtualTsEnv: VirtualTypeScriptEnvironment\n\nexport function log(message: string) {\n  info.project.projectService.logger.info('[next] ' + message)\n}\n\n// This function has to be called initially.\nexport function init(opts: {\n  ts: TypeScript\n  info: tsModule.server.PluginCreateInfo\n}): boolean {\n  const projectDir = opts.info.project.getCurrentDirectory()\n  ts = opts.ts\n  info = opts.info\n  appDirRegExp = new RegExp(\n    '^' + (projectDir + '(/src)?/app').replace(/[\\\\/]/g, '[\\\\/]')\n  )\n\n  log('Initializing Next.js TypeScript plugin: ' + projectDir)\n\n  const compilerOptions = info.project.getCompilerOptions()\n  const fsMap = createDefaultMapFromNodeModules(\n    compilerOptions,\n    ts,\n    join(projectDir, 'node_modules/typescript/lib')\n  )\n  const system = createFSBackedSystem(fsMap, projectDir, ts)\n\n  virtualTsEnv = createVirtualTypeScriptEnvironment(\n    system,\n    [],\n    ts,\n    compilerOptions\n  )\n\n  if (!virtualTsEnv) {\n    log(\n      'Failed to create virtual TypeScript environment. This is a bug in Next.js TypeScript plugin. Please report it by opening an issue at https://github.com/vercel/next.js/issues.'\n    )\n    return false\n  }\n\n  log('Successfully initialized Next.js TypeScript plugin!')\n  return true\n}\n\nexport function getTs() {\n  return ts\n}\n\nexport function getInfo() {\n  return info\n}\n\nexport function getTypeChecker() {\n  return info.languageService.getProgram()?.getTypeChecker()\n}\n\nexport function getSource(fileName: string) {\n  return info.languageService.getProgram()?.getSourceFile(fileName)\n}\n\nexport function getSourceFromVirtualTsEnv(fileName: string) {\n  if (virtualTsEnv.sys.fileExists(fileName)) {\n    return virtualTsEnv.getSourceFile(fileName)\n  }\n  return getSource(fileName)\n}\n\nexport function removeStringQuotes(str: string): string {\n  return str.replace(/^['\"`]|['\"`]$/g, '')\n}\n\nexport const isPositionInsideNode = (position: number, node: tsModule.Node) => {\n  const start = node.getFullStart()\n  return start <= position && position <= node.getFullWidth() + start\n}\n\nexport const isDefaultFunctionExport = (\n  node: tsModule.Node\n): node is tsModule.FunctionDeclaration => {\n  if (ts.isFunctionDeclaration(node)) {\n    let hasExportKeyword = false\n    let hasDefaultKeyword = false\n\n    if (node.modifiers) {\n      for (const modifier of node.modifiers) {\n        if (modifier.kind === ts.SyntaxKind.ExportKeyword) {\n          hasExportKeyword = true\n        } else if (modifier.kind === ts.SyntaxKind.DefaultKeyword) {\n          hasDefaultKeyword = true\n        }\n      }\n    }\n\n    // `export default function`\n    if (hasExportKeyword && hasDefaultKeyword) {\n      return true\n    }\n  }\n  return false\n}\n\nexport const isInsideApp = (filePath: string) => {\n  return appDirRegExp.test(filePath)\n}\nexport const isAppEntryFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^(page|layout)\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\nexport const isPageFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^page\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\n\n// Check if a module is a client entry.\nexport function getEntryInfo(\n  fileName: string,\n  throwOnInvalidDirective?: boolean\n) {\n  const source = getSource(fileName)\n  if (source) {\n    let isDirective = true\n    let isClientEntry = false\n    let isServerEntry = false\n\n    ts.forEachChild(source!, (node) => {\n      if (\n        ts.isExpressionStatement(node) &&\n        ts.isStringLiteral(node.expression)\n      ) {\n        if (node.expression.text === 'use client') {\n          if (isDirective) {\n            isClientEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use client\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        } else if (node.expression.text === 'use server') {\n          if (isDirective) {\n            isServerEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use server\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        }\n\n        if (isClientEntry && isServerEntry) {\n          const e = {\n            messageText:\n              'Cannot use both \"use client\" and \"use server\" directives in the same file.',\n            start: node.expression.getStart(),\n            length: node.expression.getWidth(),\n          }\n          throw e\n        }\n      } else {\n        isDirective = false\n      }\n    })\n\n    return { client: isClientEntry, server: isServerEntry }\n  }\n\n  return { client: false, server: false }\n}\n"], "names": ["getEntryInfo", "getInfo", "getSource", "getSourceFromVirtualTsEnv", "getTs", "getType<PERSON><PERSON>cker", "init", "isAppEntryFile", "isDefaultFunctionExport", "isInsideApp", "isPageFile", "isPositionInsideNode", "log", "removeStringQuotes", "virtualTsEnv", "ts", "info", "appDirRegExp", "message", "project", "projectService", "logger", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "compilerOptions", "getCompilerOptions", "fsMap", "createDefaultMapFromNodeModules", "join", "system", "createFSBackedSystem", "createVirtualTypeScriptEnvironment", "languageService", "getProgram", "fileName", "getSourceFile", "sys", "fileExists", "str", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "filePath", "test", "path", "basename", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuIgBA,YAAY;eAAZA;;IAtEAC,OAAO;eAAPA;;IAQAC,SAAS;eAATA;;IAIAC,yBAAyB;eAAzBA;;IAhBAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;IA/CAC,IAAI;eAAJA;;IAmGHC,cAAc;eAAdA;;IA5BAC,uBAAuB;eAAvBA;;IAyBAC,WAAW;eAAXA;;IASAC,UAAU;eAAVA;;IAvCAC,oBAAoB;eAApBA;;IAvEGC,GAAG;eAAHA;;IAmEAC,kBAAkB;eAAlBA;;IArELC,YAAY;eAAZA;;;qBAVJ;8DAEoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK3B,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACG,IAAIH;AAEJ,SAASF,IAAIM,OAAe;IACjCF,KAAKG,OAAO,CAACC,cAAc,CAACC,MAAM,CAACL,IAAI,CAAC,YAAYE;AACtD;AAGO,SAASZ,KAAKgB,IAGpB;IACC,MAAMC,aAAaD,KAAKN,IAAI,CAACG,OAAO,CAACK,mBAAmB;IACxDT,KAAKO,KAAKP,EAAE;IACZC,OAAOM,KAAKN,IAAI;IAChBC,eAAe,IAAIQ,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAGvDd,IAAI,6CAA6CW;IAEjD,MAAMI,kBAAkBX,KAAKG,OAAO,CAACS,kBAAkB;IACvD,MAAMC,QAAQC,IAAAA,oCAA+B,EAC3CH,iBACAZ,IACAgB,IAAAA,UAAI,EAACR,YAAY;IAEnB,MAAMS,SAASC,IAAAA,yBAAoB,EAACJ,OAAON,YAAYR;IAEvDD,eAAeoB,IAAAA,uCAAkC,EAC/CF,QACA,EAAE,EACFjB,IACAY;IAGF,IAAI,CAACb,cAAc;QACjBF,IACE;QAEF,OAAO;IACT;IAEAA,IAAI;IACJ,OAAO;AACT;AAEO,SAASR;IACd,OAAOW;AACT;AAEO,SAASd;IACd,OAAOe;AACT;AAEO,SAASX;QACPW;IAAP,QAAOA,mCAAAA,KAAKmB,eAAe,CAACC,UAAU,uBAA/BpB,iCAAmCX,cAAc;AAC1D;AAEO,SAASH,UAAUmC,QAAgB;QACjCrB;IAAP,QAAOA,mCAAAA,KAAKmB,eAAe,CAACC,UAAU,uBAA/BpB,iCAAmCsB,aAAa,CAACD;AAC1D;AAEO,SAASlC,0BAA0BkC,QAAgB;IACxD,IAAIvB,aAAayB,GAAG,CAACC,UAAU,CAACH,WAAW;QACzC,OAAOvB,aAAawB,aAAa,CAACD;IACpC;IACA,OAAOnC,UAAUmC;AACnB;AAEO,SAASxB,mBAAmB4B,GAAW;IAC5C,OAAOA,IAAIf,OAAO,CAAC,kBAAkB;AACvC;AAEO,MAAMf,uBAAuB,CAAC+B,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE;AAEO,MAAMpC,0BAA0B,CACrCmC;IAEA,IAAI5B,GAAGgC,qBAAqB,CAACJ,OAAO;QAClC,IAAIK,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIN,KAAKO,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYR,KAAKO,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAKrC,GAAGsC,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAKrC,GAAGsC,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEO,MAAMxC,cAAc,CAAC+C;IAC1B,OAAOvC,aAAawC,IAAI,CAACD;AAC3B;AACO,MAAMjD,iBAAiB,CAACiD;IAC7B,OACEvC,aAAawC,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAE9D;AACO,MAAM9C,aAAa,CAAC8C;IACzB,OACEvC,aAAawC,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAErD;AAGO,SAASxD,aACdqC,QAAgB,EAChBuB,uBAAiC;IAEjC,MAAMC,SAAS3D,UAAUmC;IACzB,IAAIwB,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpBjD,GAAGkD,YAAY,CAACJ,QAAS,CAAClB;YACxB,IACE5B,GAAGmD,qBAAqB,CAACvB,SACzB5B,GAAGoD,eAAe,CAACxB,KAAKyB,UAAU,GAClC;gBACA,IAAIzB,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI3B,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;wBAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC"}