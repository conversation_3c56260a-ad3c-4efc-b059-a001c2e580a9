{"version": 3, "sources": ["../../../src/server/route-matchers/locale-route-matcher.ts"], "sourcesContent": ["import type { LocaleAnalysisResult } from '../lib/i18n-provider'\nimport type { LocaleRouteDefinition } from '../route-definitions/locale-route-definition'\nimport type { LocaleRouteMatch } from '../route-matches/locale-route-match'\nimport { RouteMatcher } from './route-matcher'\n\nexport type LocaleMatcherMatchOptions = {\n  /**\n   * If defined, this indicates to the matcher that the request should be\n   * treated as locale-aware. If this is undefined, it means that this\n   * application was not configured for additional locales.\n   */\n  i18n?: LocaleAnalysisResult\n}\n\nexport class LocaleRouteMatcher<\n  D extends LocaleRouteDefinition = LocaleRouteDefinition,\n> extends RouteMatcher<D> {\n  /**\n   * Identity returns the identity part of the matcher. This is used to compare\n   * a unique matcher to another. This is also used when sorting dynamic routes,\n   * so it must contain the pathname part as well.\n   */\n  public get identity(): string {\n    return `${this.definition.pathname}?__nextLocale=${this.definition.i18n?.locale}`\n  }\n\n  /**\n   * Match will attempt to match the given pathname against this route while\n   * also taking into account the locale information.\n   *\n   * @param pathname The pathname to match against.\n   * @param options The options to use when matching.\n   * @returns The match result, or `null` if there was no match.\n   */\n  public match(\n    pathname: string,\n    options?: LocaleMatcherMatchOptions\n  ): LocaleRouteMatch<D> | null {\n    // This is like the parent `match` method but instead this injects the\n    // additional `options` into the\n    const result = this.test(pathname, options)\n    if (!result) return null\n\n    return {\n      definition: this.definition,\n      params: result.params,\n      detectedLocale:\n        // If the options have a detected locale, then use that, otherwise use\n        // the route's locale.\n        options?.i18n?.detectedLocale ?? this.definition.i18n?.locale,\n    }\n  }\n\n  /**\n   * Test will attempt to match the given pathname against this route while\n   * also taking into account the locale information.\n   *\n   * @param pathname The pathname to match against.\n   * @param options The options to use when matching.\n   * @returns The match result, or `null` if there was no match.\n   */\n  public test(pathname: string, options?: LocaleMatcherMatchOptions) {\n    // If this route has locale information and we have detected a locale, then\n    // we need to compare the detected locale to the route's locale.\n    if (this.definition.i18n && options?.i18n) {\n      // If we have detected a locale and it does not match this route's locale,\n      // then this isn't a match!\n      if (\n        this.definition.i18n.locale &&\n        options.i18n.detectedLocale &&\n        this.definition.i18n.locale !== options.i18n.detectedLocale\n      ) {\n        return null\n      }\n\n      // Perform regular matching against the locale stripped pathname now, the\n      // locale information matches!\n      return super.test(options.i18n.pathname)\n    }\n\n    // If we don't have locale information, then we can just perform regular\n    // matching.\n    return super.test(pathname)\n  }\n}\n"], "names": ["LocaleRouteMatcher", "RouteMatcher", "identity", "definition", "pathname", "i18n", "locale", "match", "options", "result", "test", "params", "detectedLocale"], "mappings": ";;;;+BAcaA;;;eAAAA;;;8BAXgB;AAWtB,MAAMA,2BAEHC,0BAAY;IACpB;;;;GAIC,GACD,IAAWC,WAAmB;YACuB;QAAnD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC,cAAc,GAAE,wBAAA,IAAI,CAACD,UAAU,CAACE,IAAI,qBAApB,sBAAsBC,MAAM,EAAE;IACnF;IAEA;;;;;;;GAOC,GACD,AAAOC,MACLH,QAAgB,EAChBI,OAAmC,EACP;YAUxB,sEAAsE;QACtE,sBAAsB;QACtBA,eAAiC;QAXrC,sEAAsE;QACtE,gCAAgC;QAChC,MAAMC,SAAS,IAAI,CAACC,IAAI,CAACN,UAAUI;QACnC,IAAI,CAACC,QAAQ,OAAO;QAEpB,OAAO;YACLN,YAAY,IAAI,CAACA,UAAU;YAC3BQ,QAAQF,OAAOE,MAAM;YACrBC,gBAGEJ,CAAAA,4BAAAA,gBAAAA,QAASH,IAAI,qBAAbG,cAAeI,cAAc,OAAI,wBAAA,IAAI,CAACT,UAAU,CAACE,IAAI,qBAApB,sBAAsBC,MAAM;QACjE;IACF;IAEA;;;;;;;GAOC,GACD,AAAOI,KAAKN,QAAgB,EAAEI,OAAmC,EAAE;QACjE,2EAA2E;QAC3E,gEAAgE;QAChE,IAAI,IAAI,CAACL,UAAU,CAACE,IAAI,KAAIG,2BAAAA,QAASH,IAAI,GAAE;YACzC,0EAA0E;YAC1E,2BAA2B;YAC3B,IACE,IAAI,CAACF,UAAU,CAACE,IAAI,CAACC,MAAM,IAC3BE,QAAQH,IAAI,CAACO,cAAc,IAC3B,IAAI,CAACT,UAAU,CAACE,IAAI,CAACC,MAAM,KAAKE,QAAQH,IAAI,CAACO,cAAc,EAC3D;gBACA,OAAO;YACT;YAEA,yEAAyE;YACzE,8BAA8B;YAC9B,OAAO,KAAK,CAACF,KAAKF,QAAQH,IAAI,CAACD,QAAQ;QACzC;QAEA,wEAAwE;QACxE,YAAY;QACZ,OAAO,KAAK,CAACM,KAAKN;IACpB;AACF"}