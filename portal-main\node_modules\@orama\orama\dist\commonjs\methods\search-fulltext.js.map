{"version": 3, "file": "search-fulltext.js", "sourceRoot": "", "sources": ["../../../src/methods/search-fulltext.ts"], "names": [], "mappings": ";;;AAqBA,kDAuEC;AAGD,wCA4FC;AA3LD,uDAAmD;AACnD,uDAAmD;AACnD,qDAAwE;AACxE,+FAAmF;AAEnF,4CAA0C;AAW1C,0CAAgG;AAChG,uCAAiC;AACjC,2CAAwE;AAGxE,SAAgB,mBAAmB,CACjC,KAAQ,EACR,MAAoI,EACpI,QAA8B;IAE9B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,MAAM,CAAA;IAEnC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAA;IAC9B,mCAAmC;IACnC,IAAI,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAa,CAAA;IACvE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,MAAM,2BAA2B,GAAG,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAA;QAEvF,kBAAkB,GAAG,KAAK,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;QAC/D,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAC9D,2BAA2B,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CACvD,CAAA;QAED,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,kBAAkB,CAAA;IACzD,CAAC;IAED,IAAI,UAAU,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAc,CAAC,EAAE,CAAC;gBACjD,MAAM,IAAA,uBAAW,EAAC,eAAe,EAAE,IAAc,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YACnF,CAAC;QACH,CAAC;QAED,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAE,UAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IAC3G,CAAC;IAGD,0FAA0F;IAC1F,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;IAC7D,IAAI,eAAwC,CAAA;IAC5C,IAAI,UAAU,EAAE,CAAC;QACf,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,KAAM,EAAE,QAAQ,CAAC,CAAA;IACpG,CAAC;IAGD,IAAI,aAA2B,CAAA;IAC/B,oCAAoC;IACpC,0BAA0B;IAC1B,oCAAoC;IACpC,yGAAyG;IACzG,IAAI,IAAI,IAAI,UAAU,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAA,eAAK,EAAC,KAAK,CAAC,CAAA;QAC9B,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAChC,KAAK,EACL,IAAI,IAAI,EAAE,EACV,KAAK,CAAC,SAAS,EACf,QAAQ,EACR,kBAAkB,EAClB,MAAM,CAAC,KAAK,IAAI,KAAK,EACrB,MAAM,CAAC,SAAS,IAAI,CAAC,EACrB,MAAM,CAAC,KAAK,IAAI,EAAE,EAClB,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,EAC9B,SAAS,EACT,eAAe,EACf,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CACnF,CAAA;IAGH,CAAC;SAAM,CAAC;QACN,sEAAsE;QACtE,+BAA+B;QAC/B,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACxH,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAe,CAAC,CAAA;IAC1D,CAAC;IAED,OAAO,aAAa,CAAA;AACtB,CAAC;AAGD,SAAgB,cAAc,CAC5B,KAAQ,EACR,MAA+C,EAC/C,QAAiB;IAEjB,MAAM,SAAS,GAAG,IAAA,6BAAkB,GAAE,CAAA;IAEtC,SAAS,kBAAkB;QACzB,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QACpE,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;QACpF,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,UAAU,EAAE,cAAc,GAAG,KAAK,EAAE,GAAG,MAAM,CAAA;QAC7E,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,KAAK,IAAI,CAAA;QAE7C,IAAI,eAAe,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;QAElE,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;gBAC7C,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;gBACnE,MAAM,kBAAkB,GAA+C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAE;iBACH,CAAC,CAAA;gBACF,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;gBACtC,eAAe,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAA;YACxE,CAAC;iBAAM,CAAC;gBACN,eAAe,GAAG,KAAK,CAAC,MAAM;qBAC3B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC;qBAC1D,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,IAAA,qDAAqB,EAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;YAC5F,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,kCAAuB,CAAC,CAAA;QACjE,CAAC;QAED,IAAI,OAAO,CAAA;QACX,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,GAAG,UAAU;gBAClB,CAAC,CAAC,IAAA,sCAA0B,EAAC,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC;gBAC/E,CAAC,CAAC,IAAA,0BAAc,EAAC,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,YAAY,GAA4B;YAC5C,OAAO,EAAE;gBACP,SAAS,EAAE,EAAE;gBACb,GAAG,EAAE,CAAC;aACP;YACD,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,eAAe,CAAC,MAAM;SAC9B,CAAA;QAED,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;YACnC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC3C,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,IAAA,gCAAqB,EAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;YACvD,CAAC;QACH,CAAC;QAED,IAAI,qBAAqB,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAA,qBAAS,EAAC,KAAK,EAAE,eAAe,EAAE,MAAM,CAAC,MAAO,CAAC,CAAA;YAChE,YAAY,CAAC,MAAM,GAAG,MAAM,CAAA;QAC9B,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,YAAY,CAAC,MAAM,GAAG,IAAA,qBAAS,EAAoB,KAAK,EAAE,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;QAC5F,CAAC;QAED,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAA,6BAAkB,GAAE,GAAG,SAAS,CAAgB,CAAA;QAE/F,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,KAAK,UAAU,kBAAkB;QAC/B,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAA,0BAAe,EAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;QACpE,CAAC;QAED,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAA;QAEzC,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAA,yBAAc,EAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAA;QAChF,CAAC;QAED,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,EAAE,MAAM,IAAI,KAAK,CAAC,WAAW,EAAE,MAAM,CAAA;IAC3E,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,kBAAkB,EAAE,CAAA;IAC7B,CAAC;IAED,OAAO,kBAAkB,EAAE,CAAA;AAC7B,CAAC;AAGY,QAAA,iBAAiB,GAAe;IAC3C,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,IAAI;IACP,CAAC,EAAE,GAAG;CACP,CAAA;AACD,SAAS,YAAY,CAAC,aAA0B;IAC9C,MAAM,CAAC,GAAG,aAAa,IAAI,EAAE,CAAA;IAC7B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,yBAAiB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,yBAAiB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,yBAAiB,CAAC,CAAC,CAAC;IACjC,OAAO,CAAyB,CAAA;AAClC,CAAC"}