"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.internals = exports.components = exports.AnswerSession = exports.updateMultiple = exports.update = exports.save = exports.load = exports.searchVector = exports.search = exports.removeMultiple = exports.remove = exports.insertMultiple = exports.insert = exports.getByID = exports.count = exports.create = void 0;
var create_js_1 = require("./methods/create.js");
Object.defineProperty(exports, "create", { enumerable: true, get: function () { return create_js_1.create; } });
var docs_js_1 = require("./methods/docs.js");
Object.defineProperty(exports, "count", { enumerable: true, get: function () { return docs_js_1.count; } });
Object.defineProperty(exports, "getByID", { enumerable: true, get: function () { return docs_js_1.getByID; } });
var insert_js_1 = require("./methods/insert.js");
Object.defineProperty(exports, "insert", { enumerable: true, get: function () { return insert_js_1.insert; } });
Object.defineProperty(exports, "insertMultiple", { enumerable: true, get: function () { return insert_js_1.insertMultiple; } });
var remove_js_1 = require("./methods/remove.js");
Object.defineProperty(exports, "remove", { enumerable: true, get: function () { return remove_js_1.remove; } });
Object.defineProperty(exports, "removeMultiple", { enumerable: true, get: function () { return remove_js_1.removeMultiple; } });
var search_js_1 = require("./methods/search.js");
Object.defineProperty(exports, "search", { enumerable: true, get: function () { return search_js_1.search; } });
var search_vector_js_1 = require("./methods/search-vector.js");
Object.defineProperty(exports, "searchVector", { enumerable: true, get: function () { return search_vector_js_1.searchVector; } });
var serialization_js_1 = require("./methods/serialization.js");
Object.defineProperty(exports, "load", { enumerable: true, get: function () { return serialization_js_1.load; } });
Object.defineProperty(exports, "save", { enumerable: true, get: function () { return serialization_js_1.save; } });
var update_js_1 = require("./methods/update.js");
Object.defineProperty(exports, "update", { enumerable: true, get: function () { return update_js_1.update; } });
Object.defineProperty(exports, "updateMultiple", { enumerable: true, get: function () { return update_js_1.updateMultiple; } });
var answer_session_js_1 = require("./methods/answer-session.js");
Object.defineProperty(exports, "AnswerSession", { enumerable: true, get: function () { return answer_session_js_1.AnswerSession; } });
__exportStar(require("./types.js"), exports);
exports.components = __importStar(require("./components.js"));
exports.internals = __importStar(require("./internals.js"));
//# sourceMappingURL=index.js.map