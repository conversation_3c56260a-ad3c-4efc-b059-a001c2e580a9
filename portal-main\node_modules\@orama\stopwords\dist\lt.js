export const stopwords=["\xe1","\xe1kypai","\xe1stri\xfeai","\xf0\xe1j\xe1","\xf0alia","\xf0e","\xf0i\xe0j\xe0","\xf0i\xe0ja","\xf0i\xe0sias","\xf0i\xf8j\xf8","\xf0i\xf8j\xf8","\xf0i","\xf0iaisiais","\xf0iajai","\xf0iajam","\xf0iajame","\xf0iapus","\xf0iedvi","\xf0ieji","\xf0iesiems","\xf0ioji","\xf0iojo","\xf0iojoje","\xf0iokia","\xf0ioks","\xf0iosiomis","\xf0iosioms","\xf0iosios","\xf0iosios","\xf0iosiose","\xf0is","\xf0isai","\xf0it","\xf0ita","\xf0itas","\xf0itiedvi","\xf0itokia","\xf0itoks","\xf0ituodu","\xf0iuodu","\xf0iuoju","\xf0iuosiuose","\xf0iuosius","\xf0tai","\xfeemiau","a\xf0","abi","abidvi","abiej\xf8","abiejose","abiejuose","abiem","abigaliai","abipus","abu","abudu","ai","an\xe0j\xe0","an\xe0j\xe1","an\xe0ja","an\xe0sias","an\xf8j\xf8","an\xf8j\xf8","ana","ana","anaiptol","anaisiais","anajai","anajam","anajame","anapus","anas","anasai","anasis","anei","aniedvi","anieji","aniesiems","anoji","anojo","anojoje","anokia","anoks","anosiomis","anosioms","anosios","anosios","anosiose","anot","ant","antai","anuodu","anuoju","anuosiuose","anuosius","apie","aplink","ar","ar","arba","argi","arti","auk\xf0\xe8iau","be","be","bei","beje","bema\xfe","bent","bet","betgi","beveik","d\xebka","d\xebl","d\xeblei","d\xeblto","dar","dargi","daugma\xfe","deja","ech","et","gal","galb\xfbt","galgi","gan","gana","gi","greta","i\xf0","i\xf0ilgai","i\xf0vis","idant","iki","iki","ir","irgi","it","itin","j\xe0j\xe0","j\xe0ja","j\xe0sias","j\xe1j\xe1","j\xf8j\xf8","j\xf8j\xf8","j\xfbs\xf8","j\xfbs","j\xfbsi\xf0k\xeb","j\xfbsi\xf0kis","jaisiais","jajai","jajam","jajame","jei","jeigu","ji","jiedu","jiedvi","jieji","jiesiems","jinai","jis","jisai","jog","joji","jojo","jojoje","jokia","joks","josiomis","josioms","josios","josios","josiose","judu","judvi","juk","jumis","jums","jumyse","juodu","juoju","juosiuose","juosius","jus","ka\xfein","ka\xfekas","ka\xfekatra","ka\xfekatras","ka\xfekokia","ka\xfekoks","ka\xfekuri","ka\xfekuris","kad","kada","kadangi","kai","kaip","kaip","kaipgi","kas","katra","katras","katriedvi","katruodu","kiaurai","kiek","kiekvienas","kieno","kita","kitas","kitokia","kitoks","kod\xebl","kokia","koks","kol","kolei","kone","kuomet","kur","kurgi","kuri","kuriedvi","kuris","kuriuodu","lai","lig","ligi","link","lyg","m\xfbs\xf8","m\xfbsi\xf0k\xeb","m\xfbsi\xf0kis","ma\xfedaug","ma\xfene","man\xe0j\xe0","man\xe0j\xe1","man\xe0ja","man\xe0sias","man\xe6s","man\xf8j\xf8","man\xf8j\xf8","man","manaisiais","manajai","manajam","manajame","manas","manasai","manasis","mane","mani\xf0k\xeb","mani\xf0kis","manieji","maniesiems","manim","manimi","mano","manoji","manojo","manojoje","manosiomis","manosioms","manosios","manosios","manosiose","manuoju","manuosiuose","manuosius","manyje","mat","mes","mudu","mudvi","mumis","mums","mumyse","mus","n\xeb","na","nagi","ne","nebe","nebent","nebent","negi","negu","nei","nei","nejau","nejaugi","nekaip","nelyginant","nes","net","netgi","netoli","neva","nors","nors","nuo","o","ogi","ogi","oi","pa\xe8i\xf8","pa\xe8iais","pa\xe8iam","pa\xe8iame","pa\xe8iu","pa\xe8iuose","pa\xe8ius","paeiliui","pagal","pakeliui","palaipsniui","palei","pas","pasak","paskos","paskui","paskum","pat\xe1","pat","pati","patiems","paties","pats","patys","per","per","pernelyg","pirm","pirma","pirmiau","po","prie\xf0","prie\xf0ais","prie","pro","pusiau","rasi","rodos","sau","sav\xe0j\xe0","sav\xe0j\xe1","sav\xe0ja","sav\xe0sias","sav\xe6s","sav\xf8j\xf8","sav\xf8j\xf8","savaisiais","savajai","savajam","savajame","savas","savasai","savasis","save","savi\xf0k\xeb","savi\xf0kis","savieji","saviesiems","savimi","savo","savoji","savojo","savojoje","savosiomis","savosioms","savosios","savosios","savosiose","savuoju","savuosiuose","savuosius","savyje","skersai","skrad\xfeiai","sta\xe8iai","su","sulig","t\xe0j\xe0","t\xe0j\xe1","t\xe0ja","t\xe0sias","t\xf8j\xf8","t\xf8j\xf8","t\xfblas","ta\xe8iau","ta","tad","tai","tai","taigi","taigi","taip","taipogi","taisiais","tajai","tajam","tajame","tamsta","tarp","tarsi","tarsi","tartum","tartum","tarytum","tas","tasai","tau","tav\xe0j\xe0","tav\xe0j\xe1","tav\xe0ja","tav\xe0sias","tav\xe6s","tav\xf8j\xf8","tav\xf8j\xf8","tavaisiais","tavajai","tavajam","tavajame","tavas","tavasai","tavasis","tave","tavi\xf0k\xeb","tavi\xf0kis","tavieji","taviesiems","tavimi","tavo","tavoji","tavojo","tavojoje","tavosiomis","tavosioms","tavosios","tavosios","tavosiose","tavuoju","tavuosiuose","tavuosius","tavyje","te","tegu","tegu","tegul","tegul","tiedvi","tieji","ties","tiesiems","tiesiog","tik","tik","tikriausiai","tiktai","tiktai","toji","tojo","tojoje","tokia","toks","tol","tolei","toliau","tosiomis","tosioms","tosios","tosios","tosiose","tu","tuodu","tuoju","tuosiuose","tuosius","turb\xfbt","u\xfe","u\xfetat","u\xfevis","v\xebl","v\xeblgi","va","vai","viduj","vidury","vien","vienas","vienokia","vienoks","vietoj","vir\xf0","vir\xf0uj","vir\xf0um","vis","vis d\xeblto","visa","visas","visgi","visokia","visoks","vos","vos","ypa\xe8"];