{"version": 3, "file": "english-stemmer.js", "sourceRoot": "", "sources": ["../../../../src/components/tokenizer/english-stemmer.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,cAAc;AAEd,MAAM,SAAS,GAAG;IAChB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,MAAM;IACd,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,KAAK;IACX,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,KAAK;IACZ,GAAG,EAAE,GAAG;IACR,KAAK,EAAE,KAAK;IACZ,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,KAAK;IACZ,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,KAAK;CACZ,CAAA;AAED,MAAM,SAAS,GAAG;IAChB,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,IAAI,EAAE,IAAI;IACV,GAAG,EAAE,EAAE;IACP,IAAI,EAAE,EAAE;CACT,CAAA;AAED,YAAY;AACZ,MAAM,CAAC,GAAG,UAAU,CAAA;AACpB,QAAQ;AACR,MAAM,CAAC,GAAG,UAAU,CAAA;AACpB,qBAAqB;AACrB,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAA;AAC1B,iBAAiB;AACjB,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAA;AAExB,kBAAkB;AAClB,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;AACpC,kBAAkB;AAClB,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAA;AACtD,oBAAoB;AACpB,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AAC5C,gBAAgB;AAChB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA;AAE/B,MAAM,UAAU,OAAO,CAAC,CAAC;IACvB,IAAI,IAAI,CAAA;IACR,IAAI,MAAM,CAAA;IACV,IAAI,EAAE,CAAA;IACN,IAAI,GAAG,CAAA;IACP,IAAI,GAAG,CAAA;IACP,IAAI,GAAG,CAAA;IAEP,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjB,OAAO,CAAC,CAAA;IACV,CAAC;IAED,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACjC,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,EAAE,GAAG,iBAAiB,CAAA;IACtB,GAAG,GAAG,gBAAgB,CAAA;IAEtB,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAC3B,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACvB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAC5B,CAAC;IAED,EAAE,GAAG,YAAY,CAAA;IACjB,GAAG,GAAG,iBAAiB,CAAA;IACvB,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,EAAE,GAAG,IAAI,CAAA;YACT,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACvB,CAAC;IACH,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACvB,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACtB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QACZ,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAA;QACrB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnB,CAAC,GAAG,IAAI,CAAA;YACR,GAAG,GAAG,aAAa,CAAA;YACnB,GAAG,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,CAAA;YACtC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAA;YAC9C,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;YACb,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,EAAE,GAAG,IAAI,CAAA;gBACT,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;YACvB,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;YACb,CAAC;QACH,CAAC;IACH,CAAC;IAED,EAAE,GAAG,UAAU,CAAA;IACf,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAA;QACpB,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,CAAC,GAAG,IAAI,GAAG,GAAG,CAAA;QAChB,CAAC;IACH,CAAC;IAED,EAAE;QACA,0IAA0I,CAAA;IAC5I,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,MAAM,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QAChB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,6DAA6D;YAC7D,aAAa;YACb,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,EAAE,GAAG,gDAAgD,CAAA;IACrD,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,MAAM,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QAChB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,6DAA6D;QAC7D,aAAa;QACb,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,6DAA6D;YAC7D,aAAa;YACb,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,EAAE,GAAG,qFAAqF,CAAA;IAC1F,GAAG,GAAG,mBAAmB,CAAA;IACzB,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,CAAC,GAAG,IAAI,CAAA;QACV,CAAC;IACH,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACvB,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACtB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACpC,GAAG,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnB,CAAC,GAAG,IAAI,CAAA;QACV,CAAC;IACH,CAAC;IAED,EAAE,GAAG,UAAU,CAAA;IACf,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACrB,GAAG,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;QACtB,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAA;QAC9C,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACnE,CAAC,GAAG,IAAI,CAAA;QACV,CAAC;IACH,CAAC;IAED,EAAE,GAAG,KAAK,CAAA;IACV,GAAG,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA;IACtB,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,EAAE,GAAG,IAAI,CAAA;QACT,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IACvB,CAAC;IAED,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,OAAO,CAAC,CAAA;AACV,CAAC"}