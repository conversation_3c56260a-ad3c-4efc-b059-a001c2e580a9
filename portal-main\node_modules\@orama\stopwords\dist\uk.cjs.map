{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'з',\n    'й',\n    'що',\n    'та',\n    'Із',\n    'але',\n    'цей',\n    'коли',\n    'як',\n    'чого',\n    'хоча',\n    'нам',\n    'яко╞',\n    'чи',\n    'це',\n    'в╡д',\n    'про',\n    '╡',\n    '╞х',\n    '╙',\n    'Інших',\n    'ти',\n    'вІн',\n    'вона',\n    'воно',\n    'ми',\n    'ви',\n    'вони'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,IACA,IACA,KACA,KACA,KACA,MACA,MACA,OACA,KACA,OACA,OACA,MACA,OACA,KACA,KACA,MACA,MACA,IACA,KACA,IACA,QACA,KACA,MACA,OACA,OACA,KACA,KACA,OACH"}