{"version": 3, "sources": ["<anon>"], "sourcesContent": ["export const stopwords = [\n    'i',\n    'me',\n    'my',\n    'myself',\n    'we',\n    'us',\n    'our',\n    'ours',\n    'ourselves',\n    'you',\n    'your',\n    'yours',\n    'yourself',\n    'yourselves',\n    'he',\n    'him',\n    'his',\n    'himself',\n    'she',\n    'her',\n    'hers',\n    'herself',\n    'it',\n    'its',\n    'itself',\n    'they',\n    'them',\n    'their',\n    'theirs',\n    'themselves',\n    'what',\n    'which',\n    'who',\n    'whom',\n    'this',\n    'that',\n    'these',\n    'those',\n    'am',\n    'is',\n    'are',\n    'was',\n    'were',\n    'be',\n    'been',\n    'being',\n    'have',\n    'has',\n    'had',\n    'having',\n    'do',\n    'does',\n    'did',\n    'doing',\n    'will',\n    'would',\n    'shall',\n    'should',\n    'can',\n    'could',\n    'may',\n    'might',\n    'must',\n    'ought',\n    \"i'm\",\n    \"you're\",\n    \"he's\",\n    \"she's\",\n    \"it's\",\n    \"we're\",\n    \"they're\",\n    \"i've\",\n    \"you've\",\n    \"we've\",\n    \"they've\",\n    \"i'd\",\n    \"you'd\",\n    \"he'd\",\n    \"she'd\",\n    \"we'd\",\n    \"they'd\",\n    \"i'll\",\n    \"you'll\",\n    \"he'll\",\n    \"she'll\",\n    \"we'll\",\n    \"they'll\",\n    \"isn't\",\n    \"aren't\",\n    \"wasn't\",\n    \"weren't\",\n    \"hasn't\",\n    \"haven't\",\n    \"hadn't\",\n    \"doesn't\",\n    \"don't\",\n    \"didn't\",\n    \"won't\",\n    \"wouldn't\",\n    \"shan't\",\n    \"shouldn't\",\n    \"can't\",\n    'cannot',\n    \"couldn't\",\n    \"mustn't\",\n    \"let's\",\n    \"that's\",\n    \"who's\",\n    \"what's\",\n    \"here's\",\n    \"there's\",\n    \"when's\",\n    \"where's\",\n    \"why's\",\n    \"how's\",\n    'an',\n    'the',\n    'and',\n    'but',\n    'if',\n    'or',\n    'because',\n    'as',\n    'until',\n    'while',\n    'of',\n    'at',\n    'by',\n    'for',\n    'with',\n    'about',\n    'against',\n    'between',\n    'into',\n    'through',\n    'during',\n    'before',\n    'after',\n    'above',\n    'below',\n    'to',\n    'from',\n    'up',\n    'down',\n    'in',\n    'out',\n    'on',\n    'off',\n    'over',\n    'under',\n    'again',\n    'further',\n    'then',\n    'once',\n    'here',\n    'there',\n    'when',\n    'where',\n    'why',\n    'how',\n    'all',\n    'any',\n    'both',\n    'each',\n    'few',\n    'more',\n    'most',\n    'other',\n    'some',\n    'such',\n    'no',\n    'nor',\n    'not',\n    'only',\n    'own',\n    'same',\n    'so',\n    'than',\n    'too',\n    'very'\n];\n"], "names": ["stopwords"], "mappings": "AAAA,OAAO,MAAMA,UAAY,CACrB,IACA,KACA,KACA,SACA,KACA,KACA,MACA,OACA,YACA,MACA,OACA,QACA,WACA,aACA,KACA,MACA,MACA,UACA,MACA,MACA,OACA,UACA,KACA,MACA,SACA,OACA,OACA,QACA,SACA,aACA,OACA,QACA,MACA,OACA,OACA,OACA,QACA,QACA,KACA,KACA,MACA,MACA,OACA,KACA,OACA,QACA,OACA,MACA,MACA,SACA,KACA,OACA,MACA,QACA,OACA,QACA,QACA,SACA,MACA,QACA,MACA,QACA,OACA,QACA,MACA,SACA,OACA,QACA,OACA,QACA,UACA,OACA,SACA,QACA,UACA,MACA,QACA,OACA,QACA,OACA,SACA,OACA,SACA,QACA,SACA,QACA,UACA,QACA,SACA,SACA,UACA,SACA,UACA,SACA,UACA,QACA,SACA,QACA,WACA,SACA,YACA,QACA,SACA,WACA,UACA,QACA,SACA,QACA,SACA,SACA,UACA,SACA,UACA,QACA,QACA,KACA,MACA,MACA,MACA,KACA,KACA,UACA,KACA,QACA,QACA,KACA,KACA,KACA,MACA,OACA,QACA,UACA,UACA,OACA,UACA,SACA,SACA,QACA,QACA,QACA,KACA,OACA,KACA,OACA,KACA,MACA,KACA,MACA,OACA,QACA,QACA,UACA,OACA,OACA,OACA,QACA,OACA,QACA,MACA,MACA,MACA,MACA,OACA,OACA,MACA,OACA,OACA,QACA,OACA,OACA,KACA,MACA,MACA,OACA,MACA,OACA,KACA,OACA,MACA,OACH,AAAC"}