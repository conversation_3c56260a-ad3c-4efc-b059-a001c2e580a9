"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),Object.defineProperty(exports,"stopwords",{enumerable:!0,get:function(){return stopwords}});let stopwords=["de","a","o","que","e","do","da","em","um","para","com","n\xe3o","uma","os","no","se","na","por","mais","as","dos","como","mas","ao","ele","das","\xe0","seu","sua","ou","quando","muito","nos","j\xe1","eu","tamb\xe9m","s\xf3","pelo","pela","at\xe9","isso","ela","entre","depois","sem","mesmo","aos","seus","quem","nas","me","esse","eles","voc\xea","essa","num","nem","suas","meu","\xe0s","minha","numa","pelos","elas","qual","n\xf3s","lhe","deles","essas","esses","pelas","este","dele","tu","te","voc\xeas","vos","lhes","meus","minhas","teu","tua","teus","tuas","nosso","nossa","nossos","nossas","dela","delas","esta","estes","estas","aquele","aquela","aqueles","aquelas","isto","aquilo","estou","est\xe1","estamos","est\xe3o","estive","esteve","estivemos","estiveram","estava","est\xe1vamos","estavam","estivera","estiv\xe9ramos","esteja","estejamos","estejam","estivesse","estiv\xe9ssemos","estivessem","estiver","estivermos","estiverem","hei","h\xe1","havemos","h\xe3o","houve","houvemos","houveram","houvera","houv\xe9ramos","haja","hajamos","hajam","houvesse","houv\xe9ssemos","houvessem","houver","houvermos","houverem","houverei","houver\xe1","houveremos","houver\xe3o","houveria","houver\xedamos","houveriam","sou","somos","s\xe3o","era","\xe9ramos","eram","fui","foi","fomos","foram","fora","f\xf4ramos","seja","sejamos","sejam","fosse","f\xf4ssemos","fossem","for","formos","forem","serei","ser\xe1","seremos","ser\xe3o","seria","ser\xedamos","seriam","tenho","tem","temos","t\xe9m","tinha","t\xednhamos","tinham","tive","teve","tivemos","tiveram","tivera","tiv\xe9ramos","tenha","tenhamos","tenham","tivesse","tiv\xe9ssemos","tivessem","tiver","tivermos","tiverem","terei","ter\xe1","teremos","ter\xe3o","teria","ter\xedamos","teriam"];