{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'á',\n    'ákypai',\n    'ástriþai',\n    'ðáj<PERSON>',\n    'ðalia',\n    'ðe',\n    'ðiàjà',\n    'ðiàja',\n    'ðiàsias',\n    'ðiøjø',\n    'ðiøjø',\n    'ði',\n    'ðiaisi<PERSON>',\n    'ðiajai',\n    'ðiajam',\n    'ðiajame',\n    'ðiapus',\n    'ðiedvi',\n    'ðieji',\n    'ðiesiems',\n    'ðioji',\n    'ðiojo',\n    'ðiojoje',\n    'ðiokia',\n    'ðioks',\n    'ðiosiomis',\n    'ðiosioms',\n    'ðiosios',\n    'ðiosios',\n    'ðiosiose',\n    'ðis',\n    'ðisai',\n    'ðit',\n    'ðita',\n    'ðitas',\n    'ðitiedvi',\n    'ðitokia',\n    'ðito<PERSON>',\n    'ðituodu',\n    'ðiuodu',\n    'ðiuoju',\n    'ðiu<PERSON>iuose',\n    'ðiu<PERSON><PERSON>',\n    'ðtai',\n    'þemiau',\n    'að',\n    'abi',\n    'abidvi',\n    'abiejø',\n    'abiejose',\n    'abiejuose',\n    'abiem',\n    'abigaliai',\n    'abipus',\n    'abu',\n    'abudu',\n    'ai',\n    'anàjà',\n    'anàjá',\n    'anàja',\n    'anàsias',\n    'anøjø',\n    'anøjø',\n    'ana',\n    'ana',\n    'anaiptol',\n    'anaisiais',\n    'anajai',\n    'anajam',\n    'anajame',\n    'anapus',\n    'anas',\n    'anasai',\n    'anasis',\n    'anei',\n    'aniedvi',\n    'anieji',\n    'aniesiems',\n    'anoji',\n    'anojo',\n    'anojoje',\n    'anokia',\n    'anoks',\n    'anosiomis',\n    'anosioms',\n    'anosios',\n    'anosios',\n    'anosiose',\n    'anot',\n    'ant',\n    'antai',\n    'anuodu',\n    'anuoju',\n    'anuosiuose',\n    'anuosius',\n    'apie',\n    'aplink',\n    'ar',\n    'ar',\n    'arba',\n    'argi',\n    'arti',\n    'aukðèiau',\n    'be',\n    'be',\n    'bei',\n    'beje',\n    'bemaþ',\n    'bent',\n    'bet',\n    'betgi',\n    'beveik',\n    'dëka',\n    'dël',\n    'dëlei',\n    'dëlto',\n    'dar',\n    'dargi',\n    'daugmaþ',\n    'deja',\n    'ech',\n    'et',\n    'gal',\n    'galbût',\n    'galgi',\n    'gan',\n    'gana',\n    'gi',\n    'greta',\n    'ið',\n    'iðilgai',\n    'iðvis',\n    'idant',\n    'iki',\n    'iki',\n    'ir',\n    'irgi',\n    'it',\n    'itin',\n    'jàjà',\n    'jàja',\n    'jàsias',\n    'jájá',\n    'jøjø',\n    'jøjø',\n    'jûsø',\n    'jûs',\n    'jûsiðkë',\n    'jûsiðkis',\n    'jaisiais',\n    'jajai',\n    'jajam',\n    'jajame',\n    'jei',\n    'jeigu',\n    'ji',\n    'jiedu',\n    'jiedvi',\n    'jieji',\n    'jiesiems',\n    'jinai',\n    'jis',\n    'jisai',\n    'jog',\n    'joji',\n    'jojo',\n    'jojoje',\n    'jokia',\n    'joks',\n    'josiomis',\n    'josioms',\n    'josios',\n    'josios',\n    'josiose',\n    'judu',\n    'judvi',\n    'juk',\n    'jumis',\n    'jums',\n    'jumyse',\n    'juodu',\n    'juoju',\n    'juosiuose',\n    'juosius',\n    'jus',\n    'kaþin',\n    'kaþkas',\n    'kaþkatra',\n    'kaþkatras',\n    'kaþkokia',\n    'kaþkoks',\n    'kaþkuri',\n    'kaþkuris',\n    'kad',\n    'kada',\n    'kadangi',\n    'kai',\n    'kaip',\n    'kaip',\n    'kaipgi',\n    'kas',\n    'katra',\n    'katras',\n    'katriedvi',\n    'katruodu',\n    'kiaurai',\n    'kiek',\n    'kiekvienas',\n    'kieno',\n    'kita',\n    'kitas',\n    'kitokia',\n    'kitoks',\n    'kodël',\n    'kokia',\n    'koks',\n    'kol',\n    'kolei',\n    'kone',\n    'kuomet',\n    'kur',\n    'kurgi',\n    'kuri',\n    'kuriedvi',\n    'kuris',\n    'kuriuodu',\n    'lai',\n    'lig',\n    'ligi',\n    'link',\n    'lyg',\n    'mûsø',\n    'mûsiðkë',\n    'mûsiðkis',\n    'maþdaug',\n    'maþne',\n    'manàjà',\n    'manàjá',\n    'manàja',\n    'manàsias',\n    'manæs',\n    'manøjø',\n    'manøjø',\n    'man',\n    'manaisiais',\n    'manajai',\n    'manajam',\n    'manajame',\n    'manas',\n    'manasai',\n    'manasis',\n    'mane',\n    'maniðkë',\n    'maniðkis',\n    'manieji',\n    'maniesiems',\n    'manim',\n    'manimi',\n    'mano',\n    'manoji',\n    'manojo',\n    'manojoje',\n    'manosiomis',\n    'manosioms',\n    'manosios',\n    'manosios',\n    'manosiose',\n    'manuoju',\n    'manuosiuose',\n    'manuosius',\n    'manyje',\n    'mat',\n    'mes',\n    'mudu',\n    'mudvi',\n    'mumis',\n    'mums',\n    'mumyse',\n    'mus',\n    'në',\n    'na',\n    'nagi',\n    'ne',\n    'nebe',\n    'nebent',\n    'nebent',\n    'negi',\n    'negu',\n    'nei',\n    'nei',\n    'nejau',\n    'nejaugi',\n    'nekaip',\n    'nelyginant',\n    'nes',\n    'net',\n    'netgi',\n    'netoli',\n    'neva',\n    'nors',\n    'nors',\n    'nuo',\n    'o',\n    'ogi',\n    'ogi',\n    'oi',\n    'paèiø',\n    'paèiais',\n    'paèiam',\n    'paèiame',\n    'paèiu',\n    'paèiuose',\n    'paèius',\n    'paeiliui',\n    'pagal',\n    'pakeliui',\n    'palaipsniui',\n    'palei',\n    'pas',\n    'pasak',\n    'paskos',\n    'paskui',\n    'paskum',\n    'patá',\n    'pat',\n    'pati',\n    'patiems',\n    'paties',\n    'pats',\n    'patys',\n    'per',\n    'per',\n    'pernelyg',\n    'pirm',\n    'pirma',\n    'pirmiau',\n    'po',\n    'prieð',\n    'prieðais',\n    'prie',\n    'pro',\n    'pusiau',\n    'rasi',\n    'rodos',\n    'sau',\n    'savàjà',\n    'savàjá',\n    'savàja',\n    'savàsias',\n    'savæs',\n    'savøjø',\n    'savøjø',\n    'savaisiais',\n    'savajai',\n    'savajam',\n    'savajame',\n    'savas',\n    'savasai',\n    'savasis',\n    'save',\n    'saviðkë',\n    'saviðkis',\n    'savieji',\n    'saviesiems',\n    'savimi',\n    'savo',\n    'savoji',\n    'savojo',\n    'savojoje',\n    'savosiomis',\n    'savosioms',\n    'savosios',\n    'savosios',\n    'savosiose',\n    'savuoju',\n    'savuosiuose',\n    'savuosius',\n    'savyje',\n    'skersai',\n    'skradþiai',\n    'staèiai',\n    'su',\n    'sulig',\n    'tàjà',\n    'tàjá',\n    'tàja',\n    'tàsias',\n    'tøjø',\n    'tøjø',\n    'tûlas',\n    'taèiau',\n    'ta',\n    'tad',\n    'tai',\n    'tai',\n    'taigi',\n    'taigi',\n    'taip',\n    'taipogi',\n    'taisiais',\n    'tajai',\n    'tajam',\n    'tajame',\n    'tamsta',\n    'tarp',\n    'tarsi',\n    'tarsi',\n    'tartum',\n    'tartum',\n    'tarytum',\n    'tas',\n    'tasai',\n    'tau',\n    'tavàjà',\n    'tavàjá',\n    'tavàja',\n    'tavàsias',\n    'tavæs',\n    'tavøjø',\n    'tavøjø',\n    'tavaisiais',\n    'tavajai',\n    'tavajam',\n    'tavajame',\n    'tavas',\n    'tavasai',\n    'tavasis',\n    'tave',\n    'taviðkë',\n    'taviðkis',\n    'tavieji',\n    'taviesiems',\n    'tavimi',\n    'tavo',\n    'tavoji',\n    'tavojo',\n    'tavojoje',\n    'tavosiomis',\n    'tavosioms',\n    'tavosios',\n    'tavosios',\n    'tavosiose',\n    'tavuoju',\n    'tavuosiuose',\n    'tavuosius',\n    'tavyje',\n    'te',\n    'tegu',\n    'tegu',\n    'tegul',\n    'tegul',\n    'tiedvi',\n    'tieji',\n    'ties',\n    'tiesiems',\n    'tiesiog',\n    'tik',\n    'tik',\n    'tikriausiai',\n    'tiktai',\n    'tiktai',\n    'toji',\n    'tojo',\n    'tojoje',\n    'tokia',\n    'toks',\n    'tol',\n    'tolei',\n    'toliau',\n    'tosiomis',\n    'tosioms',\n    'tosios',\n    'tosios',\n    'tosiose',\n    'tu',\n    'tuodu',\n    'tuoju',\n    'tuosiuose',\n    'tuosius',\n    'turbût',\n    'uþ',\n    'uþtat',\n    'uþvis',\n    'vël',\n    'vëlgi',\n    'va',\n    'vai',\n    'viduj',\n    'vidury',\n    'vien',\n    'vienas',\n    'vienokia',\n    'vienoks',\n    'vietoj',\n    'virð',\n    'virðuj',\n    'virðum',\n    'vis',\n    'vis dëlto',\n    'visa',\n    'visas',\n    'visgi',\n    'visokia',\n    'visoks',\n    'vos',\n    'vos',\n    'ypaè'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,OACA,YACA,iBACA,gBACA,WACA,QACA,iBACA,cACA,gBACA,iBACA,iBACA,QACA,eACA,YACA,YACA,aACA,YACA,YACA,WACA,cACA,WACA,WACA,aACA,YACA,WACA,eACA,cACA,aACA,aACA,cACA,SACA,WACA,SACA,UACA,WACA,cACA,aACA,YACA,aACA,YACA,YACA,gBACA,cACA,UACA,YACA,QACA,MACA,SACA,YACA,WACA,YACA,QACA,YACA,SACA,MACA,QACA,KACA,cACA,cACA,WACA,aACA,cACA,cACA,MACA,MACA,WACA,YACA,SACA,SACA,UACA,SACA,OACA,SACA,SACA,OACA,UACA,SACA,YACA,QACA,QACA,UACA,SACA,QACA,YACA,WACA,UACA,UACA,WACA,OACA,MACA,QACA,SACA,SACA,aACA,WACA,OACA,SACA,KACA,KACA,OACA,OACA,OACA,iBACA,KACA,KACA,MACA,OACA,WACA,OACA,MACA,QACA,SACA,UACA,SACA,WACA,WACA,MACA,QACA,aACA,OACA,MACA,KACA,MACA,YACA,QACA,MACA,OACA,KACA,QACA,QACA,aACA,WACA,QACA,MACA,MACA,KACA,OACA,KACA,OACA,aACA,UACA,YACA,aACA,aACA,aACA,aACA,SACA,mBACA,iBACA,WACA,QACA,QACA,SACA,MACA,QACA,KACA,QACA,SACA,QACA,WACA,QACA,MACA,QACA,MACA,OACA,OACA,SACA,QACA,OACA,WACA,UACA,SACA,SACA,UACA,OACA,QACA,MACA,QACA,OACA,SACA,QACA,QACA,YACA,UACA,MACA,WACA,YACA,cACA,eACA,cACA,aACA,aACA,cACA,MACA,OACA,UACA,MACA,OACA,OACA,SACA,MACA,QACA,SACA,YACA,WACA,UACA,OACA,aACA,QACA,OACA,QACA,UACA,SACA,WACA,QACA,OACA,MACA,QACA,OACA,SACA,MACA,QACA,OACA,WACA,QACA,WACA,MACA,MACA,OACA,OACA,MACA,aACA,mBACA,iBACA,aACA,WACA,eACA,eACA,YACA,cACA,WACA,eACA,eACA,MACA,aACA,UACA,UACA,WACA,QACA,UACA,UACA,OACA,gBACA,cACA,UACA,aACA,QACA,SACA,OACA,SACA,SACA,WACA,aACA,YACA,WACA,WACA,YACA,UACA,cACA,YACA,SACA,MACA,MACA,OACA,QACA,QACA,OACA,SACA,MACA,QACA,KACA,OACA,KACA,OACA,SACA,SACA,OACA,OACA,MACA,MACA,QACA,UACA,SACA,aACA,MACA,MACA,QACA,SACA,OACA,OACA,OACA,MACA,IACA,MACA,MACA,KACA,cACA,aACA,YACA,aACA,WACA,cACA,YACA,WACA,QACA,WACA,cACA,QACA,MACA,QACA,SACA,SACA,SACA,UACA,MACA,OACA,UACA,SACA,OACA,QACA,MACA,MACA,WACA,OACA,QACA,UACA,KACA,WACA,cACA,OACA,MACA,SACA,OACA,QACA,MACA,eACA,eACA,YACA,cACA,WACA,eACA,eACA,aACA,UACA,UACA,WACA,QACA,UACA,UACA,OACA,gBACA,cACA,UACA,aACA,SACA,OACA,SACA,SACA,WACA,aACA,YACA,WACA,WACA,YACA,UACA,cACA,YACA,SACA,UACA,eACA,aACA,KACA,QACA,aACA,aACA,UACA,YACA,aACA,aACA,WACA,YACA,KACA,MACA,MACA,MACA,QACA,QACA,OACA,UACA,WACA,QACA,QACA,SACA,SACA,OACA,QACA,QACA,SACA,SACA,UACA,MACA,QACA,MACA,eACA,eACA,YACA,cACA,WACA,eACA,eACA,aACA,UACA,UACA,WACA,QACA,UACA,UACA,OACA,gBACA,cACA,UACA,aACA,SACA,OACA,SACA,SACA,WACA,aACA,YACA,WACA,WACA,YACA,UACA,cACA,YACA,SACA,KACA,OACA,OACA,QACA,QACA,SACA,QACA,OACA,WACA,UACA,MACA,MACA,cACA,SACA,SACA,OACA,OACA,SACA,QACA,OACA,MACA,QACA,SACA,WACA,UACA,SACA,SACA,UACA,KACA,QACA,QACA,YACA,UACA,YACA,QACA,WACA,WACA,SACA,WACA,KACA,MACA,QACA,SACA,OACA,SACA,WACA,UACA,SACA,UACA,YACA,YACA,MACA,eACA,OACA,QACA,QACA,UACA,SACA,MACA,MACA,UACH"}