"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/canvas-confetti";
exports.ids = ["vendor-chunks/canvas-confetti"];
exports.modules = {

/***/ "(ssr)/./node_modules/canvas-confetti/dist/confetti.module.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/canvas-confetti/dist/confetti.module.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// canvas-confetti v1.9.3 built on 2024-04-30T22:19:17.794Z\nvar module = {};\n\n// source content\n/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(\n    global.Worker &&\n    global.Blob &&\n    global.Promise &&\n    global.OffscreenCanvas &&\n    global.OffscreenCanvasRenderingContext2D &&\n    global.HTMLCanvasElement &&\n    global.HTMLCanvasElement.prototype.transferControlToOffscreen &&\n    global.URL &&\n    global.URL.createObjectURL);\n\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = (function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n\n    return true;\n  })();\n\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n\n    func(noop, noop);\n\n    return null;\n  }\n\n  var bitmapMapper = (function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function(bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n\n        map.set(bitmap, canvas);\n\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  })(canDrawBitmap, new Map());\n\n  var raf = (function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n\n    return { frame: frame, cancel: cancel };\n  }());\n\n  var getWorker = (function () {\n    var worker;\n    var prom;\n    var resolves = {};\n\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({ options: options || {}, callback: callback });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({ canvas: offscreen }, [offscreen]);\n      };\n\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n\n        var id = Math.random().toString(36).slice(2);\n\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n\n            prom = null;\n\n            bitmapMapper.clear();\n\n            done();\n            resolve();\n          }\n\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n\n          resolves[id] = workerDone.bind(null, { data: { callback: id }});\n        });\n\n        return prom;\n      };\n\n      worker.reset = function resetWorker() {\n        worker.postMessage({ reset: true });\n\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n\n    return function () {\n      if (worker) {\n        return worker;\n      }\n\n      if (!isWorker && canUseWorker) {\n        var code = [\n          'var CONFETTI, SIZE = {}, module = {};',\n          '(' + main.toString() + ')(this, module, true, SIZE);',\n          'onmessage = function(msg) {',\n          '  if (msg.data.options) {',\n          '    CONFETTI(msg.data.options).then(function () {',\n          '      if (msg.data.callback) {',\n          '        postMessage({ callback: msg.data.callback });',\n          '      }',\n          '    });',\n          '  } else if (msg.data.reset) {',\n          '    CONFETTI && CONFETTI.reset();',\n          '  } else if (msg.data.resize) {',\n          '    SIZE.width = msg.data.resize.width;',\n          '    SIZE.height = msg.data.resize.height;',\n          '  } else if (msg.data.canvas) {',\n          '    SIZE.width = msg.data.canvas.width;',\n          '    SIZE.height = msg.data.canvas.height;',\n          '    CONFETTI = module.exports.create(msg.data.canvas);',\n          '  }',\n          '}',\n        ].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n\n          return null;\n        }\n\n        decorate(worker);\n      }\n\n      return worker;\n    };\n  })();\n\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: [\n      '#26ccff',\n      '#a25afd',\n      '#ff5e7e',\n      '#88ff5a',\n      '#fcff42',\n      '#ffa62d',\n      '#ff36ff'\n    ],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n\n  function prop(options, name, transform) {\n    return convert(\n      options && isOk(options[name]) ? options[name] : defaults[name],\n      transform\n    );\n  }\n\n  function onlyPositiveInt(number){\n    return number < 0 ? 0 : Math.floor(number);\n  }\n\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n\n    if (val.length < 6) {\n        val = val[0]+val[0]+val[1]+val[1]+val[2]+val[2];\n    }\n\n    return {\n      r: toDecimal(val.substring(0,2)),\n      g: toDecimal(val.substring(2,4)),\n      b: toDecimal(val.substring(4,6))\n    };\n  }\n\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n\n    return origin;\n  }\n\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n\n    return canvas;\n  }\n\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: (opts.startVelocity * 0.5) + (Math.random() * opts.startVelocity),\n      angle2D: -radAngle + ((0.5 * radSpread) - (Math.random() * radSpread)),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + (10 * fetti.scalar);\n      fetti.wobbleY = fetti.y + (10 * fetti.scalar);\n\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + ((10 * fetti.scalar) * Math.cos(fetti.wobble));\n      fetti.wobbleY = fetti.y + ((10 * fetti.scalar) * Math.sin(fetti.wobble));\n\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n\n    var progress = (fetti.tick++) / fetti.totalTicks;\n\n    var x1 = fetti.x + (fetti.random * fetti.tiltCos);\n    var y1 = fetti.y + (fetti.random * fetti.tiltSin);\n    var x2 = fetti.wobbleX + (fetti.random * fetti.tiltCos);\n    var y2 = fetti.wobbleY + (fetti.random * fetti.tiltSin);\n\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n\n    context.beginPath();\n\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(\n        fetti.shape.path,\n        fetti.shape.matrix,\n        fetti.x,\n        fetti.y,\n        Math.abs(x2 - x1) * 0.1,\n        Math.abs(y2 - y1) * 0.1,\n        Math.PI / 10 * fetti.wobble\n      ));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n\n      var matrix = new DOMMatrix([\n        Math.cos(rotation) * scaleX,\n        Math.sin(rotation) * scaleX,\n        -Math.sin(rotation) * scaleY,\n        Math.cos(rotation) * scaleY,\n        fetti.x,\n        fetti.y\n      ]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n\n      context.globalAlpha = (1 - progress);\n      context.fillStyle = pattern;\n      context.fillRect(\n        fetti.x - (width / 2),\n        fetti.y - (height / 2),\n        width,\n        height\n      );\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ?\n        context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) :\n        ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n\n    context.closePath();\n    context.fill();\n\n    return fetti.tick < fetti.totalTicks;\n  }\n\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n\n        done();\n        resolve();\n      }\n\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n\n        context.clearRect(0, 0, size.width, size.height);\n\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = (canvas && worker) ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n\n      var temp = particleCount;\n      var fettis = [];\n\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n\n      while (temp--) {\n        fettis.push(\n          randomPhysics({\n            x: startX,\n            y: startY,\n            angle: angle,\n            spread: spread,\n            startVelocity: startVelocity,\n            color: colors[temp % colors.length],\n            shape: shapes[randomInt(0, shapes.length)],\n            ticks: ticks,\n            decay: decay,\n            gravity: gravity,\n            drift: drift,\n            scalar: scalar,\n            flat: flat\n          })\n        );\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n\n      animationObj = animate(canvas, fettis, resizer, size , done);\n\n      return animationObj.promise;\n    }\n\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n\n      initialized = true;\n\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n\n          resizer(obj);\n\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n\n      function done() {\n        animationObj = null;\n\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas); \n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n\n      return fireLocal(options, size, done);\n    }\n\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, { useWorker: true, resize: true });\n    }\n    return defaultFire;\n  }\n\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([\n      Math.cos(rotation) * scaleX,\n      Math.sin(rotation) * scaleX,\n      -Math.sin(rotation) * scaleY,\n      Math.cos(rotation) * scaleY,\n      x,\n      y\n    ]));\n\n    return t2;\n  }\n\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n\n    var path, matrix;\n\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n\n      width = maxX - minX;\n      height = maxY - minY;\n\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize/width, maxDesiredSize/height);\n\n      matrix = [\n        scale, 0, 0, scale,\n        -Math.round((width/2) + minX) * scale,\n        -Math.round((height/2) + minY) * scale\n      ];\n    }\n\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n\n  function shapeFromText(textData) {\n    var text,\n        scalar = 1,\n        color = '#000000',\n        // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n        fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n\n    ctx.fillText(text, x, y);\n\n    var scale = 1 / scalar;\n\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n\n  module.exports = function() {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function() {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n}((function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n\n  return this || {};\n})(), module, false));\n\n// end source content\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (module.exports);\nvar create = module.exports.create;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/canvas-confetti/dist/confetti.module.mjs\n");

/***/ })

};
;