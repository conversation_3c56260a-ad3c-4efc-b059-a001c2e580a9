{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'de',\n    'en',\n    'van',\n    'ik',\n    'te',\n    'dat',\n    'die',\n    'in',\n    'een',\n    'hij',\n    'het',\n    'niet',\n    'zijn',\n    'is',\n    'was',\n    'op',\n    'aan',\n    'met',\n    'als',\n    'voor',\n    'had',\n    'er',\n    'maar',\n    'om',\n    'hem',\n    'dan',\n    'zou',\n    'of',\n    'wat',\n    'mijn',\n    'men',\n    'dit',\n    'zo',\n    'door',\n    'over',\n    'ze',\n    'zich',\n    'bij',\n    'ook',\n    'tot',\n    'je',\n    'mij',\n    'uit',\n    'der',\n    'daar',\n    'haar',\n    'naar',\n    'heb',\n    'hoe',\n    'heeft',\n    'hebben',\n    'deze',\n    'u',\n    'want',\n    'nog',\n    'zal',\n    'me',\n    'zij',\n    'nu',\n    'ge',\n    'geen',\n    'omdat',\n    'iets',\n    'worden',\n    'toch',\n    'al',\n    'waren',\n    'veel',\n    'meer',\n    'doen',\n    'toen',\n    'moet',\n    'ben',\n    'zonder',\n    'kan',\n    'hun',\n    'dus',\n    'alles',\n    'onder',\n    'ja',\n    'eens',\n    'hier',\n    'wie',\n    'werd',\n    'altijd',\n    'doch',\n    'wordt',\n    'wezen',\n    'kunnen',\n    'ons',\n    'zelf',\n    'tegen',\n    'na',\n    'reeds',\n    'wil',\n    'kon',\n    'niets',\n    'uw',\n    'iemand',\n    'geweest',\n    'andere'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,KACA,KACA,MACA,KACA,KACA,MACA,MACA,KACA,MACA,MACA,MACA,OACA,OACA,KACA,MACA,KACA,MACA,MACA,MACA,OACA,MACA,KACA,OACA,KACA,MACA,MACA,MACA,KACA,MACA,OACA,MACA,MACA,KACA,OACA,OACA,KACA,OACA,MACA,MACA,MACA,KACA,MACA,MACA,MACA,OACA,OACA,OACA,MACA,MACA,QACA,SACA,OACA,IACA,OACA,MACA,MACA,KACA,MACA,KACA,KACA,OACA,QACA,OACA,SACA,OACA,KACA,QACA,OACA,OACA,OACA,OACA,OACA,MACA,SACA,MACA,MACA,MACA,QACA,QACA,KACA,OACA,OACA,MACA,OACA,SACA,OACA,QACA,QACA,SACA,MACA,OACA,QACA,KACA,QACA,MACA,MACA,QACA,KACA,SACA,UACA,SACH"}