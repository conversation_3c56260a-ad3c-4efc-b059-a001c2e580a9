{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'olla',\n    'olen',\n    'olet',\n    'on',\n    'olemme',\n    'olette',\n    'ovat',\n    'ole',\n    'oli',\n    'olisi',\n    'olisit',\n    'olisin',\n    'olisimme',\n    'olisitte',\n    'olisivat',\n    'olit',\n    'olin',\n    'olimme',\n    'olitte',\n    'olivat',\n    'ollut',\n    'olleet',\n    'en',\n    'et',\n    'ei',\n    'emme',\n    'ette',\n    'eivät',\n    'minä',\n    'minun',\n    'minut',\n    'minua',\n    'minussa',\n    'minusta',\n    'minuun',\n    'minulla',\n    'minulta',\n    'minulle',\n    'sinä',\n    'sinun',\n    'sinut',\n    'sinua',\n    'sinussa',\n    'sinusta',\n    'sinuun',\n    'sinulla',\n    'sinulta',\n    'sinulle',\n    'hän',\n    'hänen',\n    'hänet',\n    'hänt<PERSON>',\n    'hä<PERSON><PERSON>',\n    'hänest<PERSON>',\n    'häneen',\n    'hänellä',\n    'häneltä',\n    'hänelle',\n    'me',\n    'meidän',\n    'meidät',\n    'meitä',\n    'meissä',\n    'meistä',\n    'meihin',\n    'meillä',\n    'meiltä',\n    'meille',\n    'te',\n    'teidän',\n    'teidät',\n    'teitä',\n    'teissä',\n    'teistä',\n    'teihin',\n    'teillä',\n    'teiltä',\n    'teille',\n    'he',\n    'heidän',\n    'heidät',\n    'heitä',\n    'heissä',\n    'heistä',\n    'heihin',\n    'heillä',\n    'heiltä',\n    'heille',\n    'tämä',\n    'tämän',\n    'tätä',\n    'tässä',\n    'tästä',\n    'tähän',\n    'tällä',\n    'tältä',\n    'tälle',\n    'tänä',\n    'täksi',\n    'tuo',\n    'tuon',\n    'tuota',\n    'tuossa',\n    'tuosta',\n    'tuohon',\n    'tuolla',\n    'tuolta',\n    'tuolle',\n    'tuona',\n    'tuoksi',\n    'se',\n    'sen',\n    'sitä',\n    'siinä',\n    'siitä',\n    'siihen',\n    'sillä',\n    'siltä',\n    'sille',\n    'sinä',\n    'siksi',\n    'nämä',\n    'näiden',\n    'näitä',\n    'näissä',\n    'näistä',\n    'näihin',\n    'näillä',\n    'näiltä',\n    'näille',\n    'näinä',\n    'näiksi',\n    'nuo',\n    'noiden',\n    'noita',\n    'noissa',\n    'noista',\n    'noihin',\n    'noilla',\n    'noilta',\n    'noille',\n    'noina',\n    'noiksi',\n    'ne',\n    'niiden',\n    'niitä',\n    'niissä',\n    'niistä',\n    'niihin',\n    'niillä',\n    'niiltä',\n    'niille',\n    'niinä',\n    'niiksi',\n    'kuka',\n    'kenen',\n    'kenet',\n    'ketä',\n    'kenessä',\n    'kenestä',\n    'keneen',\n    'kenellä',\n    'keneltä',\n    'kenelle',\n    'kenenä',\n    'keneksi',\n    'ketkä',\n    'keiden',\n    'ketkä',\n    'keitä',\n    'keissä',\n    'keistä',\n    'keihin',\n    'keillä',\n    'keiltä',\n    'keille',\n    'keinä',\n    'keiksi',\n    'mikä',\n    'minkä',\n    'minkä',\n    'mitä',\n    'missä',\n    'mistä',\n    'mihin',\n    'millä',\n    'miltä',\n    'mille',\n    'minä',\n    'miksi',\n    'mitkä',\n    'joka',\n    'jonka',\n    'jota',\n    'jossa',\n    'josta',\n    'johon',\n    'jolla',\n    'jolta',\n    'jolle',\n    'jona',\n    'joksi',\n    'jotka',\n    'joiden',\n    'joita',\n    'joissa',\n    'joista',\n    'joihin',\n    'joilla',\n    'joilta',\n    'joille',\n    'joina',\n    'joiksi',\n    'että',\n    'ja',\n    'jos',\n    'koska',\n    'kuin',\n    'mutta',\n    'niin',\n    'sekä',\n    'sillä',\n    'tai',\n    'vaan',\n    'vai',\n    'vaikka',\n    'kanssa',\n    'mukaan',\n    'noin',\n    'poikki',\n    'yli',\n    'kun',\n    'niin',\n    'nyt',\n    'itse'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,OACA,OACA,OACA,KACA,SACA,SACA,OACA,MACA,MACA,QACA,SACA,SACA,WACA,WACA,WACA,OACA,OACA,SACA,SACA,SACA,QACA,SACA,KACA,KACA,KACA,OACA,OACA,WACA,UACA,QACA,QACA,QACA,UACA,UACA,SACA,UACA,UACA,UACA,UACA,QACA,QACA,QACA,UACA,UACA,SACA,UACA,UACA,UACA,SACA,WACA,WACA,cACA,gBACA,gBACA,YACA,gBACA,gBACA,aACA,KACA,YACA,YACA,WACA,YACA,YACA,SACA,YACA,YACA,SACA,KACA,YACA,YACA,WACA,YACA,YACA,SACA,YACA,YACA,SACA,KACA,YACA,YACA,WACA,YACA,YACA,SACA,YACA,YACA,SACA,aACA,cACA,aACA,cACA,cACA,cACA,cACA,cACA,WACA,aACA,WACA,MACA,OACA,QACA,SACA,SACA,SACA,SACA,SACA,SACA,QACA,SACA,KACA,MACA,UACA,WACA,WACA,SACA,WACA,WACA,QACA,UACA,QACA,aACA,YACA,cACA,eACA,eACA,YACA,eACA,eACA,YACA,cACA,YACA,MACA,SACA,QACA,SACA,SACA,SACA,SACA,SACA,SACA,QACA,SACA,KACA,SACA,WACA,YACA,YACA,SACA,YACA,YACA,SACA,WACA,SACA,OACA,QACA,QACA,UACA,aACA,aACA,SACA,aACA,aACA,UACA,YACA,UACA,WACA,SACA,WACA,WACA,YACA,YACA,SACA,YACA,YACA,SACA,WACA,SACA,UACA,WACA,WACA,UACA,WACA,WACA,QACA,WACA,WACA,QACA,UACA,QACA,WACA,OACA,QACA,OACA,QACA,QACA,QACA,QACA,QACA,QACA,OACA,QACA,QACA,SACA,QACA,SACA,SACA,SACA,SACA,SACA,SACA,QACA,SACA,UACA,KACA,MACA,QACA,OACA,QACA,OACA,UACA,WACA,MACA,OACA,MACA,SACA,SACA,SACA,OACA,SACA,MACA,MACA,OACA,MACA,OACH"}