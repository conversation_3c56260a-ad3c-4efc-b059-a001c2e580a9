{"version": 3, "sources": ["../../../src/shared/lib/zod.ts"], "sourcesContent": ["import type { ZodError } from 'next/dist/compiled/zod'\nimport { ZodParsedType, util, type ZodIssue } from 'next/dist/compiled/zod'\nimport { fromZodError } from 'next/dist/compiled/zod-validation-error'\nimport * as Log from '../../build/output/log'\n\nfunction processZodErrorMessage(issue: ZodIssue) {\n  let message = issue.message\n\n  let path: string\n\n  if (issue.path.length > 0) {\n    if (issue.path.length === 1) {\n      const identifier = issue.path[0]\n      if (typeof identifier === 'number') {\n        // The first identifier inside path is a number\n        path = `index ${identifier}`\n      } else {\n        path = `\"${identifier}\"`\n      }\n    } else {\n      // joined path to be shown in the error message\n      path = `\"${issue.path.reduce<string>((acc, cur) => {\n        if (typeof cur === 'number') {\n          // array index\n          return `${acc}[${cur}]`\n        }\n        if (cur.includes('\"')) {\n          // escape quotes\n          return `${acc}[\"${cur.replaceAll('\"', '\\\\\"')}\"]`\n        }\n        // dot notation\n        const separator = acc.length === 0 ? '' : '.'\n        return acc + separator + cur\n      }, '')}\"`\n    }\n  } else {\n    path = ''\n  }\n\n  if (\n    issue.code === 'invalid_type' &&\n    issue.received === ZodParsedType.undefined\n  ) {\n    // Missing key in object.\n    return `${path} is missing, expected ${issue.expected}`\n  }\n\n  if (issue.code === 'invalid_enum_value') {\n    // Remove \"Invalid enum value\" prefix from zod default error message\n    return `Expected ${util.joinValues(issue.options)}, received '${\n      issue.received\n    }' at ${path}`\n  }\n\n  return message + (path ? ` at ${path}` : '')\n}\n\nexport function normalizeZodErrors(error: ZodError) {\n  return error.issues.flatMap((issue) => {\n    const issues = [{ issue, message: processZodErrorMessage(issue) }]\n    if ('unionErrors' in issue) {\n      for (const unionError of issue.unionErrors) {\n        issues.push(...normalizeZodErrors(unionError))\n      }\n    }\n\n    return issues\n  })\n}\n\nexport function formatZodError(prefix: string, error: ZodError) {\n  return new Error(fromZodError(error, { prefix: prefix }).toString())\n}\n\nexport function reportZodError(prefix: string, error: ZodError) {\n  Log.error(formatZodError(prefix, error).message)\n}\n"], "names": ["formatZodError", "normalizeZodErrors", "reportZodError", "processZodErrorMessage", "issue", "message", "path", "length", "identifier", "reduce", "acc", "cur", "includes", "replaceAll", "separator", "code", "received", "ZodParsedType", "undefined", "expected", "util", "joinValues", "options", "error", "issues", "flatMap", "unionError", "unionErrors", "push", "prefix", "Error", "fromZodError", "toString", "Log"], "mappings": ";;;;;;;;;;;;;;;;IAsEgBA,cAAc;eAAdA;;IAbAC,kBAAkB;eAAlBA;;IAiBAC,cAAc;eAAdA;;;;qBAzEmC;oCACtB;+DACR;AAErB,SAASC,uBAAuBC,KAAe;IAC7C,IAAIC,UAAUD,MAAMC,OAAO;IAE3B,IAAIC;IAEJ,IAAIF,MAAME,IAAI,CAACC,MAAM,GAAG,GAAG;QACzB,IAAIH,MAAME,IAAI,CAACC,MAAM,KAAK,GAAG;YAC3B,MAAMC,aAAaJ,MAAME,IAAI,CAAC,EAAE;YAChC,IAAI,OAAOE,eAAe,UAAU;gBAClC,+CAA+C;gBAC/CF,OAAO,AAAC,WAAQE;YAClB,OAAO;gBACLF,OAAO,AAAC,MAAGE,aAAW;YACxB;QACF,OAAO;YACL,+CAA+C;YAC/CF,OAAO,AAAC,MAAGF,MAAME,IAAI,CAACG,MAAM,CAAS,CAACC,KAAKC;gBACzC,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,cAAc;oBACd,OAAO,AAAGD,MAAI,MAAGC,MAAI;gBACvB;gBACA,IAAIA,IAAIC,QAAQ,CAAC,MAAM;oBACrB,gBAAgB;oBAChB,OAAO,AAAGF,MAAI,OAAIC,IAAIE,UAAU,CAAC,KAAK,SAAO;gBAC/C;gBACA,eAAe;gBACf,MAAMC,YAAYJ,IAAIH,MAAM,KAAK,IAAI,KAAK;gBAC1C,OAAOG,MAAMI,YAAYH;YAC3B,GAAG,MAAI;QACT;IACF,OAAO;QACLL,OAAO;IACT;IAEA,IACEF,MAAMW,IAAI,KAAK,kBACfX,MAAMY,QAAQ,KAAKC,kBAAa,CAACC,SAAS,EAC1C;QACA,yBAAyB;QACzB,OAAO,AAAGZ,OAAK,2BAAwBF,MAAMe,QAAQ;IACvD;IAEA,IAAIf,MAAMW,IAAI,KAAK,sBAAsB;QACvC,oEAAoE;QACpE,OAAO,AAAC,cAAWK,SAAI,CAACC,UAAU,CAACjB,MAAMkB,OAAO,IAAE,iBAChDlB,MAAMY,QAAQ,GACf,UAAOV;IACV;IAEA,OAAOD,UAAWC,CAAAA,OAAO,AAAC,SAAMA,OAAS,EAAC;AAC5C;AAEO,SAASL,mBAAmBsB,KAAe;IAChD,OAAOA,MAAMC,MAAM,CAACC,OAAO,CAAC,CAACrB;QAC3B,MAAMoB,SAAS;YAAC;gBAAEpB;gBAAOC,SAASF,uBAAuBC;YAAO;SAAE;QAClE,IAAI,iBAAiBA,OAAO;YAC1B,KAAK,MAAMsB,cAActB,MAAMuB,WAAW,CAAE;gBAC1CH,OAAOI,IAAI,IAAI3B,mBAAmByB;YACpC;QACF;QAEA,OAAOF;IACT;AACF;AAEO,SAASxB,eAAe6B,MAAc,EAAEN,KAAe;IAC5D,OAAO,qBAA6D,CAA7D,IAAIO,MAAMC,IAAAA,gCAAY,EAACR,OAAO;QAAEM,QAAQA;IAAO,GAAGG,QAAQ,KAA1D,qBAAA;eAAA;oBAAA;sBAAA;IAA4D;AACrE;AAEO,SAAS9B,eAAe2B,MAAc,EAAEN,KAAe;IAC5DU,KAAIV,KAAK,CAACvB,eAAe6B,QAAQN,OAAOlB,OAAO;AACjD"}