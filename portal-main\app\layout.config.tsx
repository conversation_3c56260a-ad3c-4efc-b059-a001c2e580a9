import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared'
import { Logo } from '@/components/ui/logo'

import en from '@/messages/en.json'
import cn from '@/messages/cn.json'

const getMessages = (locale?: string) => {
  switch (locale) {
    case 'cn':
      return cn
    case 'en':
    default:
      return en
  }
}

export function baseOptions(locale: string): BaseLayoutProps {
  const messages = getMessages(locale)
  return {
    // 移除 i18n 配置以隐藏语言切换 UI，但保留语言设置
    nav: {
      title: (
        <div className="flex items-center gap-2 mt-0.5">
          <Logo height={33} width={66} />
        </div>
      ),
      url: `/${locale}`,
    },
    links: [
      {
        text: messages.nav.docs,
        url: `/${locale}/docs`,
        active: 'nested-url',
      },
    ],
    themeSwitch: {
      enabled: true,
      mode: 'light-dark-system',
    },
  }
}
