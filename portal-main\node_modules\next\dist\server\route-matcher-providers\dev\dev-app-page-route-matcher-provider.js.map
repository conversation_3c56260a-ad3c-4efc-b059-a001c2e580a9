{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-app-page-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './helpers/file-reader/file-reader'\nimport { AppPageRouteMatcher } from '../../route-matchers/app-page-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\n\nimport { DevAppNormalizers } from '../../normalizers/built/app'\nimport { normalizeCatchAllRoutes } from '../../../build/normalize-catchall-routes'\n\nexport class DevAppPageRouteMatcherProvider extends FileCacheRouteMatcherProvider<AppPageRouteMatcher> {\n  private readonly expression: RegExp\n  private readonly normalizers: DevAppNormalizers\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    reader: FileReader\n  ) {\n    super(appDir, reader)\n\n    this.normalizers = new DevAppNormalizers(appDir, extensions)\n\n    // Match any page file that ends with `/page.${extension}` or `/default.${extension}` under the app\n    // directory.\n    this.expression = new RegExp(\n      `[/\\\\\\\\](page|default)\\\\.(?:${extensions.join('|')})$`\n    )\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<AppPageRouteMatcher>> {\n    // Collect all the app paths for each page. This could include any parallel\n    // routes.\n    const cache = new Map<\n      string,\n      { page: string; pathname: string; bundlePath: string }\n    >()\n    const routeFilenames = new Array<string>()\n    let appPaths: Record<string, string[]> = {}\n    for (const filename of files) {\n      // If the file isn't a match for this matcher, then skip it.\n      if (!this.expression.test(filename)) continue\n\n      const page = this.normalizers.page.normalize(filename)\n\n      // Validate that this is not an ignored page.\n      if (page.includes('/_')) continue\n\n      // This is a valid file that we want to create a matcher for.\n      routeFilenames.push(filename)\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n\n      // Save the normalization results.\n      cache.set(filename, { page, pathname, bundlePath })\n\n      if (pathname in appPaths) appPaths[pathname].push(page)\n      else appPaths[pathname] = [page]\n    }\n\n    normalizeCatchAllRoutes(appPaths)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPaths = Object.fromEntries(\n      Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n    )\n\n    const matchers: Array<AppPageRouteMatcher> = []\n    for (const filename of routeFilenames) {\n      // Grab the cached values (and the appPaths).\n      const cached = cache.get(filename)\n      if (!cached) {\n        throw new Error('Invariant: expected filename to exist in cache')\n      }\n      const { pathname, page, bundlePath } = cached\n\n      matchers.push(\n        new AppPageRouteMatcher({\n          kind: RouteKind.APP_PAGE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n          appPaths: appPaths[pathname],\n        })\n      )\n    }\n    return matchers\n  }\n}\n"], "names": ["DevAppPageRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "DevAppNormalizers", "expression", "RegExp", "join", "transform", "files", "cache", "Map", "routeFilenames", "Array", "appPaths", "filename", "test", "page", "normalize", "includes", "push", "pathname", "bundlePath", "set", "normalizeCatchAllRoutes", "Object", "fromEntries", "entries", "map", "k", "v", "sort", "matchers", "cached", "get", "Error", "AppPageRouteMatcher", "kind", "RouteKind", "APP_PAGE"], "mappings": ";;;;+BAQaA;;;eAAAA;;;qCAPuB;2BACV;+CACoB;qBAEZ;yCACM;AAEjC,MAAMA,uCAAuCC,4DAA6B;IAI/EC,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACC,WAAW,GAAG,IAAIC,sBAAiB,CAACJ,QAAQC;QAEjD,mGAAmG;QACnG,aAAa;QACb,IAAI,CAACI,UAAU,GAAG,IAAIC,OACpB,CAAC,2BAA2B,EAAEL,WAAWM,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1D;IAEA,MAAgBC,UACdC,KAA4B,EACiB;QAC7C,2EAA2E;QAC3E,UAAU;QACV,MAAMC,QAAQ,IAAIC;QAIlB,MAAMC,iBAAiB,IAAIC;QAC3B,IAAIC,WAAqC,CAAC;QAC1C,KAAK,MAAMC,YAAYN,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACD,WAAW;YAErC,MAAME,OAAO,IAAI,CAACd,WAAW,CAACc,IAAI,CAACC,SAAS,CAACH;YAE7C,6CAA6C;YAC7C,IAAIE,KAAKE,QAAQ,CAAC,OAAO;YAEzB,6DAA6D;YAC7DP,eAAeQ,IAAI,CAACL;YAEpB,MAAMM,WAAW,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAACH,SAAS,CAACH;YACrD,MAAMO,aAAa,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACJ,SAAS,CAACH;YAEzD,kCAAkC;YAClCL,MAAMa,GAAG,CAACR,UAAU;gBAAEE;gBAAMI;gBAAUC;YAAW;YAEjD,IAAID,YAAYP,UAAUA,QAAQ,CAACO,SAAS,CAACD,IAAI,CAACH;iBAC7CH,QAAQ,CAACO,SAAS,GAAG;gBAACJ;aAAK;QAClC;QAEAO,IAAAA,gDAAuB,EAACV;QAExB,sEAAsE;QACtEA,WAAWW,OAAOC,WAAW,CAC3BD,OAAOE,OAAO,CAACb,UAAUc,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;QAGxD,MAAMC,WAAuC,EAAE;QAC/C,KAAK,MAAMjB,YAAYH,eAAgB;YACrC,6CAA6C;YAC7C,MAAMqB,SAASvB,MAAMwB,GAAG,CAACnB;YACzB,IAAI,CAACkB,QAAQ;gBACX,MAAM,qBAA2D,CAA3D,IAAIE,MAAM,mDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0D;YAClE;YACA,MAAM,EAAEd,QAAQ,EAAEJ,IAAI,EAAEK,UAAU,EAAE,GAAGW;YAEvCD,SAASZ,IAAI,CACX,IAAIgB,wCAAmB,CAAC;gBACtBC,MAAMC,oBAAS,CAACC,QAAQ;gBACxBlB;gBACAJ;gBACAK;gBACAP;gBACAD,UAAUA,QAAQ,CAACO,SAAS;YAC9B;QAEJ;QACA,OAAOW;IACT;AACF"}