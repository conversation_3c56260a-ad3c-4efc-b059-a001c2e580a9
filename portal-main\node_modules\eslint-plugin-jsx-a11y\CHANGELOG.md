# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v6.10.2](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.10.1...v6.10.2) - 2024-10-25

### Fixed

- [patch] `no-redundandant-roles`: allow `&lt;img src="*.svg" role="img" /&gt;` [`#936`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/936)

### Commits

- [meta] fix changelog URLs [`0d01a1a`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0d01a1aff796802bca70183b24978ffebdbef073)
- [<PERSON>factor] remove no-longer-needed `es-iterator-helpers` [`aa075bd`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/aa075bd1897e0b0bb61683e4da68558f38511dae)
- [Refactor] avoid spreading things that are already arrays [`d15d3ab`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/d15d3abace7afe833d0988eaf3fb7cc166c05c06)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `@babel/register` [`5dad7c4`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/5dad7c4fdfdc56c6e488fab54f25f602430dad30)
- [Tests] `aria-role`: Add valid test for `&lt;svg role="img" /&gt;` [`daba189`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/daba18974473bce1301ffaafb822558a9e1738ae)
- [Docs] `label-has-associated-control`: add line breaks for readability [`0bc6378`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0bc63781c8f284ac6bef00321763c13c5bea6b9f)
- [Tests] `label-has-associated-control`: add additional test cases [`30d2318`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/30d23189d0b6354750c31a622204a9dc412dc2c1)
- [Tests] Add tests to reinforce required attributes for role="heading" [`d92446c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/d92446c6e608617a4c75173e8b0917c06be14dc6)

## [v6.10.1](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.10.0...v6.10.1) - 2024-10-20

### Commits

- [Fix] handle interactive/noninteractive changes from aria-query [`4925ba8`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/4925ba8d0bf80a4b1d8e8645d310590bf1b40b64)
- [Docs] Use consistent spelling of 'screen reader' [`cb6788c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/cb6788c56d7108f2faa102d041dfa37dbc0e9f59)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `@babel/register`, `auto-changelog`, `eslint-plugin-import`, `tape` [`518a77e`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/518a77e1e23fb2b1df2e8a97d14bfdd08aded797)
- [Deps] update `es-iterator-helpers`, `string.prototype.includes` [`eed03a3`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/eed03a31f313442158cf7d702443723ad4800dec)
- [meta] package.json - Update jscodeshift & remove babel-jest [`2ee940c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/2ee940c5ae87bcd0d384b6dc565d630ea70e103a)
- [Docs] Remove accidental whitespace in CONTRIBUTING.md [`a262131`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/a26213142854ffe3d931fab35f0ee4e6ae101875)
- [Deps] unpin `aria-query` [`e517937`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/e51793729e5e366e30d2f3bf0f253f2bdb9e6523)

## [v6.10.0](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.9.0...v6.10.0) - 2024-09-03

### Fixed

- [New] `label-has-associated-control`: add additional error message [`#1005`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/1005)
- [Fix] `label-has-associated-control`: ignore undetermined label text [`#966`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/966)

### Commits

- [Tests] switch from jest to tape [`a284cbf`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/a284cbf4eb21292c4cff87f02be0bfb82764757f)
- [New] add eslint 9 support [`deac4fd`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/deac4fd06eff4c0f5da27611c2a44a009b7e7fda)
- [New] add `attributes` setting [`a1ee7f8`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/a1ee7f8810efafe416eb5d7f6eb0505b52873495)
- [New] allow polymorphic linting to be restricted [`6cd1a70`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6cd1a7011446e3925f2b49c51ff26246a21491d1)
- [Tests] remove duplicate tests [`74d5dec`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/74d5decb6f2e42c05ce40a45630041fd695a2e7f)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types` [`6eca235`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6eca2359f5457af72dbfba265b73297c9232cb3e)
- [readme] remove deprecated travis ci badge; add github actions badge [`0be7ea9`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0be7ea95f560c6afc6817d381054d914ebd0b2ca)
- [Tests] use `npm audit` instead of `aud` [`05a5e49`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/05a5e4992900e0d5d61e29e13046c90797b68a7c)
- [Deps] update `axobject-query` [`912e98c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/912e98c425ef9fcc2d7d22b45b4f7e3b445112a5)
- [Deps] unpin `axobject-query` [`75147aa`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/75147aa68888fc150a4efea5b99809969bdc32b2)
- [Deps] update `axe-core` [`27ff7cb`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/27ff7cbf562bf2685fd5a6062e58eb4727cb85c6)
- [readme] fix jsxA11y import name [`ce846e0`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/ce846e00414c41676a6a8601022059878bcc0b89)
- [readme] fix typo in shareable config section in readme [`cca288b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/cca288b73a39fa0932a57c02a7a88de68fc971fc)

## [v6.9.0](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.8.0...v6.9.0) - 2024-06-19

### Fixed

- [Fix] `img-redundant-alt`: fixed multibyte character support [`#969`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/969)
- [meta] fix changelog links [`#960`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/960)

### Commits

- [New] add support for Flat Config [`6b5f096`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6b5f096f10b47326d68e2893152a48a79c8555b4)
- Revert "[Fix] `isNonInteractiveElement`: Upgrade aria-query to 5.3.0 and axobject-query to 3.2.1" [`75d5dd7`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/75d5dd722bd67186d97afa7b151fd6fee5885c70)
- [Robustness] use `safe-regex-test` [`4c7e781`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/4c7e7815c12a797587bb8e3cdced7f3003848964)
- [actions] update actions/checkout [`51a1ca7`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/51a1ca7b4d83d4fbd1ea62888f7f2dc21ece6788)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `@babel/register`, `eslint-doc-generator`, `object.entries` [`1271ac1`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/1271ac1d6e5dcf9a2bc2c086faaf062335629171)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/register`, `aud`, `eslint-plugin-import`, `npmignore`, `object.assign` [`540cb7a`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/540cb7aefead582f237071d55a40f098d0885478)
- [Deps] update `@babel/runtime`, `array-includes`, `es-iterator-helpers`, `hasown`, `object.fromentries`, `safe-regex-test` [`5d14408`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/5d1440825a8838ae10dc94cc3a4a7e1e967644b4)
- [Deps] pin `aria-query` and `axobject-query`, add `ls-engines` test to CI [`32fd82c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/32fd82c628d7f3e4ec8c06a1994f4eca1be2be4f)
- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `eslint-doc-generator` [`d1b4114`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/d1b41142248a7cca45bb5f0b96ff23ee87fb9411)
- [Fix] ensure `summary` remains non-interactive [`6a048da`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6a048dacf2b98eaa204e2a5a70dc7e3d48d9463a)
- [Deps] remove `@babel/runtime` [`0a98ad8`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0a98ad83ffa7f4b66458cc1c39db2ef32bb2c480)
- [New] `no-noninteractive-element-to-interactive-role`: allow `menuitemradio` and `menuitemcheckbox` on &lt;li&gt; [`c0733f9`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/c0733f94031fe3eec6b4d54176afe47929bb0a84)
- [Deps] update `@babel/runtime`, `safe-regex-test` [`0d5321a`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0d5321a5457c5f0da0ca216053cc5b4f571b53ae)
- [actions] pin codecov to v3.1.5 [`961817f`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/961817f61fa56cd7815c6940c27ef08469b1516b)
- [Deps] unpin `axe-core` [`b3559cf`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/b3559cf89be6b5352cd77ffa025831b3d793d565)
- [Deps] move `object.entries` to dev deps [`1be7b70`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/1be7b709eececd83f1d5f67a60b2c97cfe9a561d)
- [Deps] update `@babel/runtime` [`2a48abb`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/2a48abb5effa911e7d1a8575e1c9768c947a33f1)
- [Deps] update `@babel/runtime` [`1adec35`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/1adec3517fc2c9797212ca4d38858deed917e7be)

## [v6.8.0](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.7.1...v6.8.0) - 2023-11-01

### Merged

- Allow `title` attribute or `aria-label` attribute instead of accessible child in the "anchor-has-content" rule [`#727`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/pull/727)

### Fixed

- [Docs] `aria-activedescendant-has-tabindex`: align with changes from #708 [`#924`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/924)
- [Fix] `control-has-associated-label`: don't accept whitespace as an accessible label [`#918`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/918)

### Commits

- [Tests] migrate helper parsers function from `eslint-plugin-react` [`ce4d57f`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/ce4d57f853ce7f71bd31edaa524eeb3ff1d27cf1)
- [Refactor] use `es-iterator-helpers` [`52de824`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/52de82403752bb2ccbcac3379925650a0112d4af)
- [New] `mouse-events-have-key-events`: add `hoverInHandlers`/`hoverOutHandlers` config [`db64898`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/db64898fa591f17827053ad3c2ddeafdf7297dd6)
- [New] add `polymorphicPropName` setting for polymorphic components [`fffb05b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/fffb05b38c8eee926ee758e9ceb9eae4e697fbdd)
- [Fix] `isNonInteractiveElement`: Upgrade aria-query to 5.3.0 and axobject-query to 3.2.1 [`64bfea6`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/64bfea6352a704470a760fa6ea25cfc5a50414db)
- [Refactor] use `hasown` instead of `has` [`9a8edde`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/9a8edde7f2e80b7d104dd576f91526c6c4cbebb9)
- [actions] update used actions [`10c061a`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/10c061a70cac067641e3a084d0fb464960544505)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `@babel/register`, `aud`, `eslint-doc-generator`, `eslint-plugin-import`, `minimist` [`6d5022d`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6d5022d4894fa88d3c15c8b858114e8b2a8a440f)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/register`, `eslint-doc-generator`, `eslint-plugin-import` [`4dc7f1e`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/4dc7f1e5c611aeea2f81dc50d4ec0b206566181a)
- [New] `anchor-has-content`: Allow title attribute OR aria-label attribute [`e6bfd5c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/e6bfd5cb7c060fcaf54ede85a1be74ebe2f60d1e)
- [patch] `mouse-events-have-key-events`: rport the attribute, not the node [`eadd70c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/eadd70cb1d0478c24538ee7604cf5493a96c0715)
- [Deps] update `@babel/runtime`, `array-includes`, `array.prototype.flatmap`, `object.entries`, `object.fromentries` [`46ffbc3`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/46ffbc38512be4ed3db2f0fcd7d21af830574f63)
- [Deps] update `@babel/runtime`, `axobject-query`, `jsx-ast-utils`, `semver` [`5999555`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/5999555714f594c0fccfeeab2063c2658d9e4392)
- [Fix] pin `aria-query` and `axe-core` to fix failing tests on main [`8d8f016`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/8d8f0169dbaaa28143cf936cba3046c6e53fa134)
- [patch] move `semver` from Deps to Dev Deps [`4da13e7`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/4da13e79743ad2e1073fc2bb682197e1ba6dbea3)
- [Deps] update `ast-types-flow` [`b755318`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/b755318e675e73a33b1bb7ee809abc88c1927408)
- [Dev Deps] update `eslint-plugin-import` [`f1c976b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/f1c976b6af2d4f5237b481348868a5216e169296)
- [Deps] unpin `language-tags` [`3d1d26d`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/3d1d26d89d492947cbf69f439deec9e7cfaf9867)
- [Docs] `no-static-element-interactions`: tabIndex is written tabindex [`1271153`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/1271153653ada3f8d95b8e39f0164d5b255abea0)
- [Deps] Upgrade ast-types-flow to mitigate Docker user namespacing problems [`f0d2ddb`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/f0d2ddb65f21278ad29be43fb167a1092287b4b1)
- [Dev Deps] pin `jackspeak` since 2.1.2+ depends on npm aliases, which kill the install process in npm &lt; 6 [`0c278f4`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0c278f4805ec18d8ee4d3e8dfa2f603a28d7e113)

## [v6.7.1](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.7.0...v6.7.1) - 2023-01-11

### Commits

- [Fix] `no-aria-hidden-on-focusable` rule's missing export [`b01219e`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/b01219edc2eb289c7a068b4fa195f2ac04e915fa)

## [v6.7.0](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/compare/v6.6.1...v6.7.0) - 2023-01-09

### Merged

- New rule: prefer-tag-over-role [`#833`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/pull/833)

### Fixed

- [Tests] `aria-role`: add now-passing test [`#756`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/756)
- [Docs] `control-has-associated-label`: fix metadata [`#892`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/892)
- [New] add `no-aria-hidden-on-focusable` rule [`#881`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/881)

### Commits

- [Docs] automate docs with `eslint-doc-generator` [`6d7a857`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6d7a857eccceb58dabfa244f6a196ad1697c01a4)
- [Refactor] use fromEntries, flatMap, etc; better use iteration methods [`3d77c84`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/3d77c845a98b6fc8cf10c810996278c02e308f35)
- [New] add `anchor-ambiguous-text` rule [`7f6463e`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/7f6463e5cffd1faa5cf22e3b0d33465e22bd10e1)
- [New] add `getAccessibleChildText` util [`630116b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/630116b334e22db853a95cd64e20b7df9f2b6dc8)
- [New] Add `isFocusable` utils method [`e199d17`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/e199d17db0b6bf1d917dab13a9690876ef6f77e3)
- [Docs] update `eslint-doc-generator` to v1.0.0 [`6b9855b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6b9855b9c3633308004960594327a10bc2551ad2)
- [Fix] `no-noninteractive-element-interactions`: Ignore contenteditable elements in no-noninteractive-element-interactions [`9aa878b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/9aa878bc39769f6c7b31c72bd1140c1370d202f1)
- [New] `anchor-ambiguous-text`: ignore punctuation [`bbae2c4`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/bbae2c46ab4ae94122be6c898f2ef313c6154c27)
- [New] `anchor-ambiguous-text`, `getAccessibleChildText`: Implements check for `alt` tags on `&lt;img /&gt;` elements [`bb84abc`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/bb84abc793435a25398160242c5f2870b83b72ca)
- [meta] use `npmignore` to autogenerate an npmignore file [`6ad2312`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/6ad23124582741385df50e98d5ed0d070f86eafe)
- [meta] add `auto-changelog` [`283817b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/283817b82252ef4a6395c22585d8681f97305ca0)
- [Docs] missing descriptions in some rules [`79b975a`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/79b975ab7185cc4fbf6a3adea45c78fac2162d77)
- [Deps] update `aria-query`, `axobject-query` [`7b3cda3`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/7b3cda3854451affe20b2e4f2dd57cf317dd7d1b)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `aud`, `object.assign` [`0852947`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0852947cfd57a34353a97c67f6de28dbcc8be0e3)
- [meta] move `.eslintignore` to `ignorePatterns` [`65be35b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/65be35b0f6c6cf8b79e9a748cb657a64b78c6535)
- [Dev Deps] update `@babel/cli`, `@babel/core`, `aud`, `eslint-doc-generator` [`60c2df5`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/60c2df5388a3f841a7780eafe1a0fbb44056743d)
- [Deps] update `@babel/runtime`, `array-includes`, `axe-core` [`4abc751`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/4abc751d87a8491219a9a3d2dacd80ea8adcb79b)
- [Deps] update `@babel/runtime`, `axe-core` [`89f766c`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/89f766cd40fd32ada2020856b251ad6e34a6f365)
- [meta] run the build in prepack, not prepublish [`e411ce3`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/e411ce35cfa58181d375544ba5204c35db83678c)
- [Dev Deps] update `@babel/core`, `minimist` [`cccdb62`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/cccdb625d6237538fb4443349870293e8df818eb)
- [Dev Deps] update `markdown-magic` [`3382059`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/3382059feb5367c79e049943772e3a6e27e77609)
- [Fix] expose `prefer-tag-over-role` [`38d52f8`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/38d52f856a18d444e6db7d16d373e0d18c5b287d)
- [Docs] `label-has-for`: reran generate-list-of-rules [`9a2af01`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/9a2af0172cefad7fdce869401b2df42536812152)
- [Deps] pin `language-tags` to `v1.0.5` [`f84bb74`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/f84bb746857cfbc075f8e7104b3a16dddb66be7c)
- [Dev Deps] update `@babel/core` [`cf3f8d0`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/cf3f8d0a6bde6dc5ad39a96a6ed1912c1ad80e89)
- [Deps] update `axe-core` [`0a73cf4`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0a73cf4ad0adca0bef0a383a10a14597acef5713)
- [Deps] update `@babel/runtime` [`053f04d`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/053f04da8b60d259e4c92f214ffba07a14f3ec61)
- [Deps] update `@babel/runtime` [`bccf0ae`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/bccf0aeab8dd337c5f134f892a6d3588fbc29bdf)
- [Deps] update `jsx-ast-utils` [`c9687cc`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/c9687cc2a1b7f5f72c8181a9fd6a47f49c373240)
- [readme] Preventing code repetition in user's eslint config file [`8b889bf`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/8b889bff2731c9db6988c88c0d76bdbff17bd3c5)
- [Docs] `prefer-tag-over-role`: rename docs file [`0bdf95b`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/commit/0bdf95b41cce32c8b7916367e7c8c663411d881c)

<!-- auto-changelog-above -->

6.6.1 / 2022-07-21
==================
- 38405ad [Fix] `no-interactive-tabindex`: allow role assignments using a ternary with literals on both sides
- 7524e0c [Fix] `no-static-element-interactions`: allow role assignments using a ternary with literals on both sides (#865)
- 1c06306 [readme] properly describe rule settings in builtin configs
- 0c19f02 [Docs] `no-noninteractive-tabindex`, `no-static-element-interactions`: document `allowExpressionValues` (#870)
- 2362832 [readme] added link to redirect eslint to relevant docs (#862)
- 2c6926c [Deps] unpin `axe-core`
- b78f19d [Deps] pin `axe-core` to v4.4.1, due to a breaking change in a patch
- 768910e [Deps] update `@babel/runtime`
- f0e04ce [Deps] update `@babel/runtime`, `jsx-ast-utils`
- 93b2a9d [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `@babel/register`
- a962211 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/plugin-transform-flow-strip-types`, `@babel/register`
- 0d2bc43 [Tests] `no-noninteractive-element-interactions`: add passing test cases (#876)
- ffefbad [Tests] `no-noninteractive-element-interactions`: add passing tests for form with onSubmit (#871)
- e7d405d [Tests] `no-static-element-interactions`: add passing test cases

6.6.0 / 2022-06-23
==================
- 566011b [New] `aria-role`: add `allowedInvalidRoles` option (#828)
- 64dcac6 [New] Introduce a plugin-wide setting for custom components. (#844)
- ce2c328 [Fix] `no-redundant-roles`, `role-supports-aria-props`: Remove implicit role from dl element (#848)
- fb20bc4 [Refactor] `role-supports-aria-props`: clean up the logic a bit
- 1826628 [Refactor] reduce egregious use of array spread, in favor of `[].concat` idiom
- 0f1615a [Docs] `no-static-element-interactions`: Update error message (#843)
- 9980d1d [Docs] Add infrastructure for auto-generating markdown table and list (#837)
- f878d3b [Docs] Update project readme (#831)
- aea7671 [Deps] update `@babel/runtime`, `array-includes`, `axe-core`, `jsx-ast-utils`
- d74173a [Deps] update `jsx-ast-utils`
- f6ba03c [Deps] update `@babel/runtime`, `jsx-ast-utils`
- 547dab4 [Deps] update `@babel/runtime`, `axe-core`, `minimatch`
- baaf791 [Deps] update `@babel/runtime`, `minimatch`, `semver`
- c015fef [Deps] update `@babel/runtime`, `axe-core`, `damerau-levenshtein`
- 832cbd6 [meta] add `export default` instead of `module.exports` (#861)
- ee933a2 [meta] Add CONTRIBUTING.md to solicit contributions (#846)
- fa3c869 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `aud`, `eslint-plugin-eslint-plugin`, `eslint-plugin-flowtype`, `eslint-plugin-import`
- fb3d51e [Dev Deps] update `@babel/core`, `@babel/register`, `eslint-plugin-import`, `minimist`
- 8c1df4d [Dev Deps] pin `@technote-space/doctoc` because v2.5 is a breaking change
- fb071ab [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `eslint-plugin-eslint-plugin`
- 5e966e5 [Dev Deps] update `@babel/cli`
- f597f5b [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`
- 287854a [Tests] Fix `npm run flow` (#856)
- 112261c [Tests] skip fragment tests in eslint < 6
- ea877c4 [Tests] `img-redundant-alt-test`: add passing tests (#832)
- 685426d test: align usage of jest expect across tests (#827)
- c460a8b [Tests] move invalid test case to valid; changed in axe-core v4.4

6.5.1 / 2021-11-10
==================
- 8f7d0b0 [Fix] properly build `module.exports` (#824)
- 2fd2087 [Dev Deps] update `eslint-plugin-import`

6.5.0 / 2021-11-09
==================
- 0f5f582 [New] support ESLint 8.x (#810)
- 1dbc416 [Deps] update `@babel/runtime`, `axe-core`
- 4043d31 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `@babel/plugin-transform-flow-strip-types`, `eslint-config-airbnb-base`
- d143cba [Docs] HTTP => HTTPS (#823)
- 309b040 [Docs] `anchor-has-content`: add missing close / for jsx succeed example (#821)
- ba1e312 [eslint] simplify eslint command
- 0269025 [meta] change all `master` references in URLs to `HEAD`
- f1414cf [Dev Deps] add `eslint-plugin-eslint-plugin` (#818)
- f44fc05 [meta] update URLs
- df34872 [Refactor] switch to `export default` syntax for exporting rules (#819)
- ff26b82 [meta] fix prepublish scripts
- d4a57d8 [Deps] update `@babel/runtime`, `array-includes`, `axe-core`, `jsx-ast-utils`
- bd1dec6 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/eslint-parser`, `eslint-plugin-import`, `estraverse`, `safe-publish-latest`
- 434c4cf [Tests] do not test eslint 7 on node 11
- aed7a20 [Tests] use `@babel/eslint-parser` instead of `babel-eslint` (#811)
- 0021489 [actions] use codecov action
- 1251088 [meta] delete FUNDING.yml in favor of `.github` repo
- ecf7a27 [Docs] `scope`: replace duplicate `scope` word (#799)
- 952af25 [Fix] `no-access-key`: Fix wording and grammar (#800)
- 6cf7ac0 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/plugin-transform-flow-strip-types`, `aud`, `eslint-plugin-flowtype`, `eslint-plugin-import`
- 79a35d4 [Deps] update `@babel/runtime`, `axe-core`, `damerau-levenshtein`
- 2a9ab71 [Tests] delete `src/util/getComputedRole-test.js` test in node 6
- 0c1c587 [Tests] `autocomplete-valid`: move some failed tests to passing
- 8830902 [Tests] fix eslint < 7 not understanding `import type`
- d57887c [Tests] ensure all tests run
- 55e5c11 Support img role for canvas (#796)
- 36102cd [meta] use `prepublishOnly` script for npm 7+
- 2501a7f Remove the link-button focus css from the anchor-is-valid doc (#662)
- d927625 Update recommended config to allow fieldset to have the radiogroup role (#746)
- 5aa8db9 [Docs] Clarify the title of the strictness table in the main doc (#786)
- df3c7ad [Docs] Document the similarity between html-has-lang and lang (#778)
- 426d4c2 Fix Flow warnings (#785)
- ecec8e4 Fully deprecate accessible-emoji rule (#782)
- 8a0e43c [Tests] remove .travis.yml
- f88bf6b [Dev Deps] update `flow-bin` to support aarch64 (#784)
- 369f9db [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/plugin-transform-flow-strip-types`, `aud`, `eslint-plugin-flowtype`, `jscodeshift`
- ce0785f [Deps] update `@babel/runtime`, `array-includes`, `axe-core`, `emoji-regex`
- 2c2a2ad [actions] update to use `node/install` action
- c275964 [Docs] `anchor-is-valid`: general cleanup (#728)
- 3df059e [Docs] `no-redundant-roles`: Adds missing closing square bracket (#775)
- 42ce5b7 [Docs] `anchor-is-valid`: Add Next.js case (#769)
- 2e5df91 [Tests] fix tests breaking on npm 7
- 066ccff [Docs] `no-noninteractive-tabindex`: Add example for tabIndex on seemingly non-interactive element (#760)
- 6b19aa5 [Tests] migrate tests to Github Actions (#764)
- 7e158e3 [meta] run `aud` in `posttest`
- 71f390f [Tests] stop using coveralls
- e54b466 [meta] add Automatic Rebase and Require Allow Edits workflows
- 7d5511d [New] `label-has-associated-control`: Add glob support (#749)
- 854da0c Ran npm update; latest packages (#763)
- 8637aa7 (source/pr/734, fork/pr/26) [patch] `strict` config: Turn off `label-has-for` (#734)
- d85ce54 [doc] Add link to MDN Aria documentation (#762)
- 20b48a4 [patch] `no-onchange`: Remove rule from recommended/strict configs, and deprecate (#757)

6.4.1 / 2020-10-26
==================
- f8a4496 Upgrade jsx-ast-utils to v3.1.0

6.4.0 / 2020-10-26
==================

- 83e4ff2 [Deps] update `axe-core`, `jsx-ast-utils`
- eb92b07 [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/plugin-transform-flow-strip-types`, `eslint-plugin-flowtype`, `eslint-plugin-import`, `estraverse`, `expect`, `object.assign`
- 3d98d7a [Deps] update `@babel/runtime`, `axe-core`
- f702f62 [readme] add Spanish translation
- c2ae092 [Docs] `no-static-element-interactions`: Fixed rule name in comments
- b90e20d Fix screenreader -> screen reader
- 645900a Fixed rule name in comments
- 381b9d6 [fix:634] Ignore control elements that are hidden
- 2c47f0a [Fix] `autocomplete-valid`: workaround for axe not being able to handle `null`
- 00bd6d8 Add failing test for autocomplete with dynamic type
- 3c49c9a Add WCAG guidelines to rule documentation
- 4ecaf35 Add a testcase for tablist to interactive supports focus
- dac6864 Deprecate the accessible-emoji rule
- 5191053 Update to axobject-query@2.2.0
- b315698 Allow negative tabindex in aria-activedescendant-has-tabindex
- 8e6fcd0 docs: fix travis badge now points to correct location at travis-ci.com
- 2234df7 Account for additional control elements in label-has-associated-control
- 5cbb718 Adding test cases for label tests
- 66c425c Additional test case for no-redundant-roles

6.3.1 / 2020-06-19
==================

- 765da0f Update to aria-query 4.2.2
- d528e8c Fix aria-level allowed on elements wit role heading (#704)
- 29c6859 [meta] remove yarn registry from npmrc, so publishing works
- f52c206 chore(package): update estraverse to version 5.0.0

6.3.0 / 2020-06-18
==================

- cce838a Update aria-query to 4.2.0
- 121e8a4 Add two test cases found while upgrading to ARIA 1.2
- 8059f51 Fix test failures raised by the upgrade to ARIA 1.2
- 0d24e3a Update package.json
- b1f412a Fix test failures in role-has-required-aria-props due to ARIA 1.2 updates
- 74cec6e Fix test failures in no-noninteractive-element-interactions due to ARIA 1.2 updates
- 835b89e Fix test failures in role-supports-aria-props-test due to ARIA 1.2 updates
- 730319b Account for the null semantic generic role in ARIA 1.2
- 7dfa7c9 Update aria-query from 4.0.1 to 4.0.2
- 42098b9 [Refactor] `img-redundant-alt`: removing a use of `some`
- a910d83 [Tests] `label-has-associated-control`: add test for <div><label /><input /></div>
- b273fe5 [New] Support ESLint 7.x
- 1a97632 [Deps] update `@babel/runtime`, `array-includes`, `axe-core`, `axobject-query`, `damerau-levenshtein`, `jsx-ast-utils`
- b36976f [Dev Deps] update `@babel/cli`, `@babel/core`, `@babel/plugin-transform-flow-types`, `babel-eslint`, `babel-jest`, `coveralls`, `eslint-config-airbnb-base`, `eslint-plugin-flowtype`, `eslint-plugin-import`, `estraverse`, `in-publish`, `jest`, `minimist`, `rimraf`, `safe-publish-latest`
- 89acdc4 fix: removing the use of the some function
- 410ae43 chore(package): update eslint-plugin-flowtype to version 5.0.0
- a87f83d fix(package): update emoji-regex to version 9.0.0
- 71940e6 chore(package): update babel-preset-airbnb to version 5.0.0
- d471f54 docs: Fix 404 links to WAI-ARIA spec
- 42a2016 Fixes #669: use the `language-tags` package to check the `lang` rule
- 7bcea20 [Tests] update axe-core
- f13dc38 [Deps] Pin axe-core version
- 33670bb fix: require missing 'autocomplete-valid' rule
- aca4c37 chore(mouse-event): revert unrelated formatting changes
- df1e275 fix(mouse-event): remove check from custom elements
- 1a16a1c chore(package): update jscodeshift to version 0.7.0
- 7a55cdd chore(package): update flow-bin to version 0.113.0
- 8e0d22b Update aria-query and axobject-query to the latest versions
- dd49060 Added test cases for an empty or undefined value of aria-label and aria-labelledby in alt-text
- 1a7b94f Updated dependencies including eslint-config-airbnb-base
- 3aea217 chore: replace ignoreNonDOM with inputComponents
- 1848d00 feat(autocomplete-valid): add to recommended & strict config
- 8703840 refactor: use to axe-cre 3.4 SerialVirtualNode format
- 3519c7b chore: Remove axe VirtualNode abstraction
- 9ac55c4 autocomplete-valid: Add inline comment
- 44c6098 Update axe-core to 3.3.0
- 9916990 new autocomplete-valid rule
- 82f598e [Docs] examples: add language in code block for syntax highlight
- 2529ad3 fixing casing issue on aria-props
- 00926f2 Update README.md
- ce5d121 Update README.md
- 031574e chore(package): update flow-bin to version 0.103.0
- e00e1db [meta] add FUNDING.yml
- e1e5fae Fix readme file

6.2.3 / 2019-06-30
=================
- [617] Add @babel/runtime to the dependencies

6.2.2 / 2019-06-29
=================
- Update jsx-ast-utils to v2.2.1
- Add @babel/cli to the dev dependencies
- Update ESLint to v6
- Update jsx-ast-utils to 2.2.0
- Update flow-bin to version 0.102.0
- [589] Allow expression statements for attribute values in no-noninteractive-tabindexlow-bin-0.101.0
- [583] Allow expression values in attributes by configurationrror
- [596] Adding a test case for no-static-element-interactionseper/flow-bin-0.101.0) Merge branch 'master' into greenkeeper/flow-bin-0.101.0
- Only run branch test coverage on the master branch
- chore(package): update flow-bin to version 0.100.0
- Allow select as a valid child of label.
- Allow Node 4 / ESLint 3 failure to unblock ESLint upgrade in PR #568
- chore(package): update flow-bin to version 0.99.0
- Remove rootDir from Jest path configs
- (fix) Template literals with undefined evaluate to the string undefined.
- adds more tests to “anchor-is-valid”
- Fixes “anchor-is-valid” false positive for hrefs starting with the word “javascript”
- chore(package): update eslint-plugin-flowtype to version 3.5.0
- Modified no-static-element-interactions to pass on non-literal roles.
- Added isNonLiteralProperty util method
- [#399] Account for spread in parser options
- [552] control-has-associated-label should allow generic links
- [issue 392] ul role='list' test case
- chore(package): update eslint to version 5.15.2
- chore(package): update flow-bin to version 0.95.0
- chore(package): update expect to version 24.3.1
- Fix typo: defintions > definitions
- docs: add proper title to links to axe website for media-has-caption
- docs: removes deprecated rule label-has-for
- docs: fix typo and couple grammatical errors in Readme
- Ignore null/undefined values in role-supports-aria-props rule
- Ignore undefined values in aria-proptypes rule
- Ignore null values in aria-proptypes rule
- set target for node 4

6.2.1 / 2019-02-03
=================
- 9980e45 [fix] Prevent Error when JSXSpreadAttribute is passed to isSemanticRoleElement

6.2.0 / 2019-01-25
=================
- 5650674 [new rule] control-has-associated-label checks interactives for a label
- f234698 [docs] add How to manage IDs
- 9924d03 [docs] document jsx-a11y/label-has-associated-control assert option
- 77b9870 [docs] Add newlines below headings
- 8244e43 [docs] Add syntax highlighting to example
- 26f41c8 [docs] Change explanation for role="presentation" escape hatch
- 33a1f94 [fix] - Purely decorative emojis do not need descriptions.
- 29d20f7 [fix] (package): update emoji-regex to version 7.0.2
- 0b63f73 [chore] (package): update flow-bin to version 0.88.0
- baa1344 [fix] Disable jsx-a11y/label-has-for in recommended
- 2c5fb06 [chore] (package): update jscodeshift to version 0.6.0
- 87debc0 [fix] corrected no-noninteractive-element-to-interactive-role.md file
- d56265b [chore] (package): update flow-bin to version 0.87.0
- 477966f [fix] Update test for implicit role of `img`
- f484ce3 [fix] No implicit role for `<img>` with `alt=""`
- 6c33bcb [fix] Add select to the list of default control elements in label-has-associated-control
- 011f8d9 [fix] Dialog and Alert roles can host keyboard listeners
- 0f6a8af [fix] More easier `plugin:jsx-a11y/{recommended,strict}` configs
- 3844248 [fix] Mark the replacement for label-has-for
- 93265cb [fix] normalizedValues to values
- 651366c [fix] Make aria-role case sensitive
- 56d3b9a [fix] [484] Fix role-has-required-aria-props for semantic elements like input[checkbox]
- 46e9abd [fix] Handle the type={truthy} case in jsx

6.1.2 / 2018-10-05
=================
- [fix] Add link-type styling recommendation to anchor-is-valid #486
- [fix] `label-has-for`: `textarea`s are inputs too #470

6.1.1 / 2018-07-03
==================
- [fix] aria-proptypes support for idlist, #454
- [fix] Image with expanded props throws 'The prop must be a JSXAttribute collected by the AST parser.', #459
- [fix] label-has-for: broken in v6.1.0, #455

6.1.0 / 2018-06-26
==================
- [new] Support for eslint v5, #451
- [new] aria-query updated to latest version
- [new] eslint-config-airbnb-base updated to the latest version
- [deprecate] The rule label-has-for is deprecated and replaced with label-has-associated-control
- [fix] heading-has-content updated to work with custom components, #431
- [fix] aria-errormessage prop is now a valid ARIA property, #424

6.0.2 / 2017-06-28
==================
- [fix] Prefix directories in `.npmignore` with `/` so it only matches the top-level directory


6.0.1 / 2017-06-28
==================
- [temporary] Remove `src` and `flow` from package to resolve flow issues for consuming packages.


6.0.0 / 2017-06-05
=================
- [new] Add rule `anchor-is-valid`. See documentation for configuration options. Thanks @AlmeroSteyn.
- [breaking] `href-no-hash` replaced with `anchor-is-valid` in the recommended and strict configs. Use the `invalidHref` aspect (active by default) in `anchor-is-valid` to continue to apply the behavior provided by `href-no-hash`.
- [breaking] Removed support for ESLint peer dependency at version ^2.10.2.
- [update] The rule `label-has-for` now allows inputs nested in label tags. Previously it was strict about requiring a `for` attribute. Thanks @ignatiusreza and @mjaltamirano.
- [update] New configuration for `interactive-supports-focus`. Recommended and strict configs for now contain a trimmed-down whitelist of roles that will be checked.
- [fix] Incompatibility between node version 4 and 5. Thanks @evilebottnawi.
- [fix] Missing README entry for `media-has-caption`. Thanks @ismail-syed.
- [fix] README updates explaining recommended and strict configs. Thanks @Donaldini.
- [fix] Updated to aria-query@0.7.0, which includes new ARIA 1.1 properties. Previously, the `aria-props` rule incorrectly threw errors for these new properties.

5.1.1 / 2017-07-03
==================
 - [fix] revert v6 breaking changes unintentionally added in v5.1 (#283)

5.1.0 / 2017-06-26
==================
 - [new] Support eslint v4. (#267)
 - [new] `label-has-for`: add "required" option to allow customization (#240)
 - [new] add `anchor-is-valid` (#224)
 - [new] `interactive-supports-focus`: Split interactive supports focus into tabbable and focusable cases (#236)
 - [new] `anchor-is-valid`: add `aspects` option (#251)
 - [Deps] Bump aria-query to 0.7.0

5.0.3 / 2017-05-16
==================
- [fix] Remove `flow` directory from `.npmignore` to accommodate explicit imports from `v5.0.2`.


5.0.2 / 2017-05-16
==================
- [fix] Explicitly import flow types to resolve flow failures in consuming projects.


5.0.1 / 2017-05-07
==================
- [fix] Polyfill Array.includes for node < 6 support.


5.0.0 / 2017-05-05
==================
- [breaking] Refactor `img-has-alt` rule into `alt-text` rule
- [breaking] Rule `onclick-has-role` is removed. Replaced with `no-static-element-interactions` and `no-noninteractive-element-interactions`.
- [breaking] Rule `onclick-has-focus` is removed. Replaced with `interactive-supports-focus`.
- [new] - Add rule `media-has-caption` rule
- [new] - Add `ignoreNonDOM` option to `no-autofocus`.
- [new] - Add rule `no-interactive-element-to-noninteractive-role`
- [new] - Add rule `no-noninteractive-element-to-interactive-role`
- [new] - Add rule `no-noninteractive-tabindex`
- [new] - Configs split into "recommended" and "strict".
- [enhanced] - Configuration options added to `no-static-element-interactions` and `no-noninteractive-element-interactions`. Options allow for fine-tuning of elements and event handlers to check.


4.0.0 / 2017-02-04
==================
Add new rules:
- `jsx-a11y/accessible-emoji`
- `jsx-a11y/aria-activedescendant-has-tabindex`
- `jsx-a11y/iframe-has-title`
- `jsx-a11y/no-autofocus`
- `jsx-a11y/no-distracting-elements` *(breaking: consolidated no-marquee and no-blink into this rule.)*
- `jsx-a11y/no-redundant-roles`
- [fix] - redundant-alt to only check full words
- [docs] - Documentation upgrades across the board.
- [new] - Add `ignoreNonDom`
- [dev] - Add script to scaffold new rule creation.


3.0.2 / 2016-12-14
==================
- [fix] - make `aria-invalid` values true and false pass for rule `aria-proptypes`

3.0.1 / 2016-10-11
==================
- [breaking] - Update all rule schemas to accept objects. This allows a future schema expansion to not be a breaking change.
- [breaking] - All rules with schemas that accepted a string OR array, now only allows an array.
- [new] - `href-no-hash` accepts new schema property `specialLink` to check for custom `href` properties on elements. (fixes [#76](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/issues/76))
- [breaking][fix] - `img-has-alt` now prefers `alt=""` over `role="presentation"`. You can set both, but not just `role="presentation"` by itself to ensure a11y across all devices.

Note - see [rule documentation](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/tree/HEAD/docs/rules) for updated schemas.

2.2.3 / 2016-10-08
==================
- [fix] - Add `switch` aria role.
- [devDependencies] - Updgrade dev dependencies and fix linting issues.


2.2.2 / 2016-09-12
==================
- [fix] `x-has-content` rules now pass with children prop set.


2.2.1 / 2016-08-31
==================
- [fix] Update `tablist` role to include missing property `aria-multiselectable`.


2.2.0 / 2016-08-26
==================
- [new] Add `click-events-have-key-events` rule.
- [new] Add `no-static-element-interactions` rule.
- [devDependencies] Upgrade `eslint`, `eslint-config-airbnb`, `mocha` to latest.
- [lint] Fix all new linting errors with upgrade
- [nit] Use `error` syntax over `2` syntax in recommended config.


2.1.0 / 2016-08-10
==================
- [fix] Require `aria-checked` for roles that are subclasses of `checkbox`
- [new] Add `anchor-has-content` rule.
- [refactor] Use new eslint rule syntax
- [new] Add support for custom words in `img-redundant-alt` (mainly for i18n).


2.0.1 / 2016-07-13
==================
- [fix] JSXElement support in expression handlers for prop types.
- [fix] `heading-has-content`: dangerouslySetInnerHTML will pass.


2.0.0 / 2016-07-12
==================
- [breaking] Scope `no-onchange` rule to select menu elements only.


1.5.5 / 2016-07-05
==================
- [fix] Add `eslint` v3 as a `peerDependency`.


1.5.4 / 2016-07-05
==================
- [fix] Add `eslint` as a `peerDependency`.


1.5.3 / 2016-06-16
==================
- [fix] Fix crash when ``<ELEMENT role />`` for `role-supports-aria-props`.


1.5.2 / 2016-06-16
==================
- [fix] Fix `img-redundant-alt` rule to use `getLiteralPropValue` from `jsx-ast-utils`.


1.5.1 / 2016-06-16
==================
- [fix] Fix checking for undefined in `heading-has-content` for children content.


1.5.0 / 2016-06-16
==================
- [new] Add [heading-has-content](docs/rules/heading-has-content.md) rule.
- [new] Add [html-has-lang](docs/rules/html-has-lang.md) rule.
- [new] Add [lang](docs/rules/lang.md) rule.
- [new] Add [no-marquee](docs/rules/no-marquee.md) rule.
- [new] Add [scope](docs/rules/scope.md) rule.


1.4.2 / 2016-06-10
==================
- [new] Integrate with latest `jsx-ast-utils` to use `propName` function. More support for namespaced names on attributes and elements.


1.4.1 / 2016-06-10
==================
- [fix] Handle spread props in `aria-unsupported-elements` and `role-supports-aria-props` when reporting.


1.4.0 / 2016-06-10
==================
- [dependency] Integrate [jsx-ast-utils](https://github.com/jsx-eslint/jsx-ast-utils)
- [fix] Better error reporting for aria-unsupported-elements indicating which prop to remove.


1.3.0 / 2016-06-05
==================
- [new] Spelling suggestions for incorrect `aria-*` props
- [fix] Ensure `role` value is a string before converting to lowercase in `img-has-alt` rule.


1.2.3 / 2016-06-02
==================
- [fix] Handle dynamic `tabIndex` expression values, but still retain validation logic for literal `tabIndex` values.


1.2.2 / 2016-05-20
==================
- [fix] Fix checks involving the tabIndex attribute that do not account for integer literals


1.2.1 / 2016-05-19
==================
- [fix] Avoid testing interactivity of wrapper components with same name but different casing
as DOM elements (such as `Button` vs `button`).


1.2.0 / 2016-05-06
==================
- [new] Import all roles from DPUB-ARIA.


1.1.0 / 2016-05-06
==================
- [new] Add expression value handler for `BinaryExpression` type.
- [new] Add expression value handler for `NewExpression` type.
- [new] Add expression value handler for `ObjectExpression` type.
- [fix] Throws error when getting an expression of type without a handler function.
	- This is for more graceful error handling and better issue reporting.


1.0.4 / 2016-04-28
==================
- [fix] Add expression value handler for `ConditionalExpression` type.


1.0.3 / 2016-04-25
==================
- [fix] Fix typo in recommended rules for `onclick-has-focus`.


1.0.2 / 2016-04-20
==================
- [fix] Add expression value handler for `ThisExpression` type.


1.0.1 / 2016-04-19
==================
- [fix] Fix build to copy source JSON files to build output.


1.0.0 / 2016-04-19
==================
- [breaking] Rename `img-uses-alt` to `img-has-alt`
- [breaking] Rename `onlick-uses-role` to `onclick-has-role`
- [breaking] Rename `mouse-events-map-to-key-events` to `mouse-events-have-key-events`
- [breaking] Rename `use-onblur-not-onchange` to `no-onchange`
- [breaking] Rename `label-uses-for` to `label-has-for`
- [breaking] Rename `redundant-alt` to `img-redundant-alt`
- [breaking] Rename `no-hash-href` to `href-no-hash`
- [breaking] Rename `valid-aria-role` to `aria-role`

- [new] Implement `aria-props` rule
- [new] Implement `aria-proptypes` rule
- [new] Implement `aria-unsupported-elements` rule
- [new] Implement `onclick-has-focus` rule
- [new] Implement `role-has-required-aria-props` rule
- [new] Implement `role-supports-aria-props` rule
- [new] Implement `tabindex-no-positive` rule


0.6.2 / 2016-04-08
==================
- [fix] Fix rule details for img-uses-alt: allow alt="" or role="presentation".


0.6.1 / 2016-04-07
==================
- [fix] Do not infer interactivity of components that are not low-level DOM elements.


0.6.0 / 2016-04-06
==================
- [breaking] Allow alt="" when role="presentation" on img-uses-alt rule.
- [new] More descriptive error messaging for img-uses-alt rule.


0.5.2 / 2016-04-05
==================
- [fix] Handle token lists for valid-aria-role.


0.5.1 / 2016-04-05
==================
- [fix] Handle null valued props for valid-aria-role.


0.5.0 / 2016-04-02
==================
- [new] Implement valid-aria-role rule. Based on [AX_ARIA_01](https://github.com/GoogleChrome/accessibility-developer-tools/wiki/Audit-Rules#ax_aria_01)


0.4.3 / 2016-03-29
==================
- [fix] Handle LogicalExpression attribute types when extracting values. LogicalExpressions are of form `<Component prop={foo || "foobar"} />`


0.4.2 / 2016-03-24
==================
- [fix] Allow component names of form `Object.Property` i.e. `UX.Layout`


0.3.0 / 2016-03-02
==================
- [new] Implement [no-hash-href](docs/rules/no-hash-href.md) rule.
- [fix] Fixed TemplateLiteral AST value building to get more exact values from template strings.


0.2.0 / 2016-03-01
==================
- [new] Implement [redunant-alt](docs/rules/redundant-alt.md) rule.


0.1.2 / 2016-03-01
==================
- Initial pre-release.
