---
title: 配置属性
---

本指南将帮助您在 TEN-Agent Playground 中配置属性。

## 配置属性

1. 打开 [localhost:3000](http://localhost:3000) 上的 Playground 以配置您的智能体。
2. 选择一种图表类型（例如，语音智能体、实时智能体）。
3. 单击图表选择右侧的按钮以打开属性配置。
4. 从下拉列表中，您可以找到图中使用的所有扩展节点。
5. 选择一个扩展节点以配置其属性。例如：stt / llm / tts / v2v / tool。
6. 您将看到可以为所选扩展节点配置的属性列表。
7. 您可以通过单击输入字段或开关（如果是布尔值）来更改属性的值。
8. 单击“保存更改”以将属性应用于扩展节点。
9. 如果您看到成功提示，则表示该属性已成功应用于扩展节点。

## 添加更多属性

有些属性在扩展节点中定义，但目前没有值。这些属性不会显示在属性配置中。您可以按照以下步骤添加更多属性：

1. 在属性列表抽屉中，单击“添加属性”按钮。
2. 将打开一个新抽屉，其中包含可用属性的下拉列表。
3. 从下拉列表中选择一个属性。
4. 单击“添加”以将该属性添加到扩展节点。
5. 配置属性的值。
6. 单击“保存更改”以将属性应用于扩展节点。
7. 如果您看到成功提示，则表示该属性已成功应用于扩展节点。
