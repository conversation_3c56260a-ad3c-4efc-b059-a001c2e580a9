import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';
import Image from 'next/image';

/**
 * Shared layout configurations
 *
 * you can customise layouts individually from:
 * Home Layout: app/(home)/layout.tsx
 * Docs Layout: app/docs/layout.tsx
 */
export const baseOptions: BaseLayoutProps = {
  nav: {
    title: (
      <div className="flex items-center gap-2 mt-0.5 text-[#2563EB]">
        {/* 使用Deepinsight.png作为logo */}
        <Image
          src="/Deepinsight.png"
          alt="DeepInsight Logo"
          width={24}
          height={24}
          className="w-6 h-6"
        />
        
        Help Center | DeepBI
      </div>
    ),
    url: '/docs',
  },
  links: [
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
    {
      text: 'Blog',
      url: '/blog',
      active: 'nested-url',
    },
  ],
  themeSwitch: {
    enabled: true,
    mode: 'light-dark-system',
  },
};
