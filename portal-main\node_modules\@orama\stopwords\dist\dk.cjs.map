{"version": 3, "sources": ["<anon>"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"stopwords\", {\n    enumerable: true,\n    get: function() {\n        return stopwords;\n    }\n});\nconst stopwords = [\n    'og',\n    'i',\n    'jeg',\n    'det',\n    'at',\n    'en',\n    'den',\n    'til',\n    'er',\n    'som',\n    'på',\n    'de',\n    'med',\n    'han',\n    'af',\n    'for',\n    'ikke',\n    'der',\n    'var',\n    'mig',\n    'sig',\n    'men',\n    'et',\n    'har',\n    'om',\n    'vi',\n    'min',\n    'havde',\n    'ham',\n    'hun',\n    'nu',\n    'over',\n    'da',\n    'fra',\n    'du',\n    'ud',\n    'sin',\n    'dem',\n    'os',\n    'op',\n    'man',\n    'hans',\n    'hvor',\n    'eller',\n    'hvad',\n    'skal',\n    'selv',\n    'her',\n    'alle',\n    'vil',\n    'blev',\n    'kunne',\n    'ind',\n    'når',\n    'være',\n    'dog',\n    'noget',\n    'ville',\n    'jo',\n    'deres',\n    'efter',\n    'ned',\n    'skulle',\n    'denne',\n    'end',\n    'dette',\n    'mit',\n    'også',\n    'under',\n    'have',\n    'dig',\n    'anden',\n    'hende',\n    'mine',\n    'alt',\n    'meget',\n    'sit',\n    'sine',\n    'vor',\n    'mod',\n    'disse',\n    'hvis',\n    'din',\n    'nogle',\n    'hos',\n    'blive',\n    'mange',\n    'ad',\n    'bliver',\n    'hendes',\n    'været',\n    'thi',\n    'jer',\n    'sådan'\n];\n"], "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "stopwords"], "mappings": "AAAA,aACAA,OAAOC,cAAc,CAACC,QAAS,aAAc,CACzCC,MAAO,CAAA,CACX,GACAH,OAAOC,cAAc,CAACC,QAAS,YAAa,CACxCE,WAAY,CAAA,EACZC,IAAK,WACD,OAAOC,SACX,CACJ,GACA,IAAMA,UAAY,CACd,KACA,IACA,MACA,MACA,KACA,KACA,MACA,MACA,KACA,MACA,QACA,KACA,MACA,MACA,KACA,MACA,OACA,MACA,MACA,MACA,MACA,MACA,KACA,MACA,KACA,KACA,MACA,QACA,MACA,MACA,KACA,OACA,KACA,MACA,KACA,KACA,MACA,MACA,KACA,KACA,MACA,OACA,OACA,QACA,OACA,OACA,OACA,MACA,OACA,MACA,OACA,QACA,MACA,SACA,UACA,MACA,QACA,QACA,KACA,QACA,QACA,MACA,SACA,QACA,MACA,QACA,MACA,UACA,QACA,OACA,MACA,QACA,QACA,OACA,MACA,QACA,MACA,OACA,MACA,MACA,QACA,OACA,MACA,QACA,MACA,QACA,QACA,KACA,SACA,SACA,WACA,MACA,MACA,WACH"}