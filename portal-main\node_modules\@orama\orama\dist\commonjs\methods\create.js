"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.create = create;
const defaults_js_1 = require("../components/defaults.js");
const documents_store_js_1 = require("../components/documents-store.js");
const plugins_js_1 = require("../components/plugins.js");
const hooks_js_1 = require("../components/hooks.js");
const index_js_1 = require("../components/index.js");
const internal_document_id_store_js_1 = require("../components/internal-document-id-store.js");
const sorter_js_1 = require("../components/sorter.js");
const index_js_2 = require("../components/tokenizer/index.js");
const errors_js_1 = require("../errors.js");
const utils_js_1 = require("../utils.js");
function validateComponents(components) {
    const defaultComponents = {
        formatElapsedTime: defaults_js_1.formatElapsedTime,
        getDocumentIndexId: defaults_js_1.getDocumentIndexId,
        getDocumentProperties: defaults_js_1.getDocumentProperties,
        validateSchema: defaults_js_1.validateSchema
    };
    for (const rawKey of hooks_js_1.FUNCTION_COMPONENTS) {
        const key = rawKey;
        if (components[key]) {
            if (typeof components[key] !== 'function') {
                throw (0, errors_js_1.createError)('COMPONENT_MUST_BE_FUNCTION', key);
            }
        }
        else {
            // @ts-expect-error TSC is unable to resolve this
            components[key] = defaultComponents[key];
        }
    }
    for (const rawKey of Object.keys(components)) {
        if (!hooks_js_1.OBJECT_COMPONENTS.includes(rawKey) && !hooks_js_1.FUNCTION_COMPONENTS.includes(rawKey)) {
            throw (0, errors_js_1.createError)('UNSUPPORTED_COMPONENT', rawKey);
        }
    }
}
function create({ schema, sort, language, components, id, plugins }) {
    if (!components) {
        components = {};
    }
    for (const plugin of plugins ?? []) {
        if (!('getComponents' in plugin)) {
            continue;
        }
        if (typeof plugin.getComponents !== 'function') {
            continue;
        }
        const pluginComponents = plugin.getComponents(schema);
        const keys = Object.keys(pluginComponents);
        for (const key of keys) {
            if (components[key]) {
                throw (0, errors_js_1.createError)('PLUGIN_COMPONENT_CONFLICT', key, plugin.name);
            }
        }
        components = {
            ...components,
            ...pluginComponents
        };
    }
    if (!id) {
        id = (0, utils_js_1.uniqueId)();
    }
    let tokenizer = components.tokenizer;
    let index = components.index;
    let documentsStore = components.documentsStore;
    let sorter = components.sorter;
    if (!tokenizer) {
        // Use the default tokenizer
        tokenizer = (0, index_js_2.createTokenizer)({ language: language ?? 'english' });
    }
    else if (!tokenizer.tokenize) {
        // If there is no tokenizer function, we assume this is a TokenizerConfig
        tokenizer = (0, index_js_2.createTokenizer)(tokenizer);
    }
    else {
        const customTokenizer = tokenizer;
        tokenizer = customTokenizer;
    }
    if (components.tokenizer && language) {
        // Accept language only if a tokenizer is not provided
        throw (0, errors_js_1.createError)('NO_LANGUAGE_WITH_CUSTOM_TOKENIZER');
    }
    const internalDocumentStore = (0, internal_document_id_store_js_1.createInternalDocumentIDStore)();
    index ||= (0, index_js_1.createIndex)();
    sorter ||= (0, sorter_js_1.createSorter)();
    documentsStore ||= (0, documents_store_js_1.createDocumentsStore)();
    // Validate all other components
    validateComponents(components);
    // Assign only recognized components and hooks
    const { getDocumentProperties, getDocumentIndexId, validateSchema, formatElapsedTime } = components;
    const orama = {
        data: {},
        caches: {},
        schema,
        tokenizer,
        index,
        sorter,
        documentsStore,
        internalDocumentIDStore: internalDocumentStore,
        getDocumentProperties,
        getDocumentIndexId,
        validateSchema,
        beforeInsert: [],
        afterInsert: [],
        beforeRemove: [],
        afterRemove: [],
        beforeUpdate: [],
        afterUpdate: [],
        beforeSearch: [],
        afterSearch: [],
        beforeInsertMultiple: [],
        afterInsertMultiple: [],
        beforeRemoveMultiple: [],
        afterRemoveMultiple: [],
        afterUpdateMultiple: [],
        beforeUpdateMultiple: [],
        afterCreate: [],
        formatElapsedTime,
        id,
        plugins,
        version: getVersion()
    };
    orama.data = {
        index: orama.index.create(orama, internalDocumentStore, schema),
        docs: orama.documentsStore.create(orama, internalDocumentStore),
        sorting: orama.sorter.create(orama, internalDocumentStore, schema, sort)
    };
    for (const hook of plugins_js_1.AVAILABLE_PLUGIN_HOOKS) {
        orama[hook] = (orama[hook] ?? []).concat((0, plugins_js_1.getAllPluginsByHook)(orama, hook));
    }
    const afterCreate = orama['afterCreate'];
    if (afterCreate) {
        (0, hooks_js_1.runAfterCreate)(afterCreate, orama);
    }
    return orama;
}
function getVersion() {
    return '{{VERSION}}';
}
//# sourceMappingURL=create.js.map